# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Production builds
build/
dist/
.next/
out/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# MongoDB data
data/

# Temporary files
tmp/
temp/

# Package lock files (keep only one)
package-lock.json
yarn.lock

# Hot reload files
*.hot-update.js
*.hot-update.json

# React specific
.eslintcache

# Testing
/coverage

# Misc
.sass-cache/
connect.lock
typings/
.grunt

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Local History for Visual Studio Code
.history/

# Windows image file caches
Thumbs.db
ehthumbs.db

# Folder config file
Desktop.ini

# Recycle Bin used on file shares
$RECYCLE.BIN/

# Windows Installer files
*.cab
*.msi
*.msm
*.msp

# Windows shortcuts
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Icon must end with two \r
Icon

# Thumbnails
._*

# Files that might appear in the root of a volume
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Directories potentially created on remote AFP share
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~

# temporary files which can be created if a process still has a handle open of a deleted file
.fuse_hidden*

# KDE directory preferences
.directory

# Linux trash folder which might appear on any partition or disk
.Trash-*

# .nfs files are created when an open file is removed but is still being accessed
.nfs*

# ===========================
# PROJECT SPECIFIC FILES
# ===========================
# Project documentation files
CahierDeCharge.pdf
Contenu de la page Web EMBA.pdf
Formulaire d'inscription EMBA ESPRIT 2023-2024.pdf
Logo-EMBA.png
test-auth.html

# Backend specific
backend/.env
backend/uploads/
backend/logs/
backend/debug-*.js
backend/scripts/debug-*.js

# Frontend specific
frontend/.env
frontend/build/
frontend/.eslintcache

# Database files
*.db
*.sqlite
*.sqlite3
data/
mongodb-data/

# Upload directories
uploads/
public/uploads/
static/uploads/

# Email templates backup
email-templates-backup/

# Test and mock data
test-data/
mock-data/
fixtures/

# SSL certificates and keys
*.pem
*.key
*.crt
*.cert
ssl/
certificates/

# Backup files
*.backup
*.bak
*.old

# Generated documentation
docs/build/
documentation/build/

# Deployment files
deploy.sh
deployment/
docker-compose.override.yml

# IDE workspace files
*.code-workspace

# Package manager specific
.pnpm-store/
.yarn/
.yarnrc.yml

# Testing
cypress/videos/
cypress/screenshots/
jest-coverage/

# Cache directories
.cache/
.parcel-cache/
.eslintcache
.stylelintcache

# Runtime and process files
*.pid
*.seed
*.pid.lock

# Optional directories
jspm_packages/
bower_components/

# Serverless
.serverless/

# Cloud deployment
.vercel
.netlify

# Local development
.local/
local/