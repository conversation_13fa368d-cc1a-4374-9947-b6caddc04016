# EMBA Management System - API Documentation

## Base URL
```
http://localhost:5000/api
```

## Authentication
La plupart des endpoints nécessitent une authentification JWT. Incluez le token dans le header :
```
Authorization: Bearer <your_jwt_token>
```

## Endpoints Principaux

### 🔐 Authentification
```http
POST /auth/login
POST /auth/register
POST /auth/forgot-password
POST /auth/reset-password
```

### 👥 Utilisateurs
```http
GET    /users              # Liste des utilisateurs
GET    /users/:id          # Détails d'un utilisateur
POST   /users              # Créer un utilisateur
PUT    /users/:id          # Modifier un utilisateur
DELETE /users/:id          # Supprimer un utilisateur
GET    /users/search       # Rechercher des utilisateurs
```

### 🎓 Étudiants
```http
GET    /students                    # Liste des étudiants
GET    /students/:id               # Détails d'un étudiant
POST   /students                   # Créer un étudiant
PUT    /students/:id               # Modifier un étudiant
GET    /students/:id/progress      # Progression académique
GET    /students/:id/attendance    # Statistiques de présence
GET    /students/:id/financial     # Informations financières
POST   /students/:id/notes         # Ajouter une note
GET    /students/statistics        # Statistiques globales
```

### 👨‍🏫 Professeurs
```http
GET    /professors                 # Liste des professeurs
GET    /professors/:id            # Détails d'un professeur
POST   /professors                # Créer un professeur
PUT    /professors/:id            # Modifier un professeur
GET    /professors/:id/courses    # Cours d'un professeur
GET    /professors/:id/teaching-load  # Charge d'enseignement
```

### 📚 Cours
```http
GET    /courses                   # Liste des cours
GET    /courses/:id              # Détails d'un cours
POST   /courses                  # Créer un cours
PUT    /courses/:id              # Modifier un cours
GET    /courses/:id/students     # Étudiants inscrits
```

### 📦 Modules
```http
GET    /modules                  # Liste des modules
GET    /modules/:id             # Détails d'un module
POST   /modules                 # Créer un module
PUT    /modules/:id             # Modifier un module
DELETE /modules/:id             # Supprimer un module
```

### 📝 Inscriptions
```http
GET    /enrollments             # Liste des inscriptions
GET    /enrollments/:id        # Détails d'une inscription
POST   /enrollments            # Créer une inscription
PUT    /enrollments/:id        # Modifier une inscription
```

### 📅 Planification
```http
GET    /schedules                    # Liste des sessions
GET    /schedules/:id               # Détails d'une session
POST   /schedules                   # Créer une session
PUT    /schedules/:id               # Modifier une session
POST   /schedules/:id/attendance    # Marquer la présence
```

### 📊 Notes
```http
GET    /grades                 # Liste des notes
GET    /grades/:id            # Détails d'une note
POST   /grades                # Créer une note
PUT    /grades/:id            # Modifier une note
POST   /grades/:id/publish    # Publier une note
```

### 📋 Devoirs
```http
GET    /assignments              # Liste des devoirs
GET    /assignments/:id         # Détails d'un devoir
POST   /assignments             # Créer un devoir
PUT    /assignments/:id         # Modifier un devoir
POST   /assignments/:id/submit  # Soumettre un devoir
POST   /assignments/:id/grade   # Noter un devoir
```

### 📈 Présence
```http
GET    /attendance                        # Liste des présences
GET    /attendance/:id                   # Détails d'une présence
POST   /attendance                       # Créer un enregistrement
PUT    /attendance/:id                   # Modifier une présence
GET    /attendance/student/:id/stats     # Statistiques d'un étudiant
```

### 💰 Paiements
```http
GET    /payments                    # Liste des paiements
GET    /payments/:id               # Détails d'un paiement
POST   /payments                   # Créer un paiement
PUT    /payments/:id               # Modifier un paiement
POST   /payments/:id/process       # Traiter un paiement
GET    /payments/overdue           # Paiements en retard
```

### 🔔 Notifications
```http
GET    /notifications                     # Liste des notifications
GET    /notifications/:id                # Détails d'une notification
POST   /notifications                    # Créer une notification
PUT    /notifications/:id                # Modifier une notification
POST   /notifications/:id/mark-read      # Marquer comme lu
GET    /notifications/user/:id/unread    # Notifications non lues
```

### 📄 Documents
```http
GET    /documents                 # Liste des documents
GET    /documents/:id            # Détails d'un document
POST   /documents                # Uploader un document
PUT    /documents/:id            # Modifier un document
POST   /documents/:id/approve    # Approuver un document
GET    /documents/search         # Rechercher des documents
```

### 📋 Candidatures
```http
GET    /applications                        # Liste des candidatures
GET    /applications/:id                   # Détails d'une candidature
POST   /applications                       # Créer une candidature
PUT    /applications/:id                   # Modifier une candidature
POST   /applications/:id/submit            # Soumettre une candidature
POST   /applications/:id/accept            # Accepter une candidature
POST   /applications/:id/reject            # Rejeter une candidature
POST   /applications/:id/schedule-interview # Programmer un entretien
GET    /applications/statistics            # Statistiques des candidatures
```

## Paramètres de Requête Communs

### Pagination
```
?page=1&limit=10
```

### Filtrage
```
?status=active&academicYear=2024-2025
```

### Tri
```
?sort=createdAt&order=desc
```

### Recherche
```
?q=search_term
```

## Codes de Réponse HTTP

- `200` - Succès
- `201` - Créé avec succès
- `400` - Erreur de validation
- `401` - Non authentifié
- `403` - Non autorisé
- `404` - Ressource non trouvée
- `429` - Trop de requêtes
- `500` - Erreur serveur

## Format de Réponse

### Succès
```json
{
  "success": true,
  "data": {...},
  "message": "Operation successful"
}
```

### Erreur
```json
{
  "success": false,
  "message": "Error description",
  "errors": [...]
}
```

### Liste avec Pagination
```json
{
  "data": [...],
  "totalPages": 5,
  "currentPage": 1,
  "total": 50
}
```

## Exemples d'Utilisation

### Créer un Utilisateur
```bash
curl -X POST http://localhost:5000/api/users \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "password": "SecurePass123",
    "role": "student"
  }'
```

### Obtenir les Cours d'un Étudiant
```bash
curl -X GET "http://localhost:5000/api/students/123/courses" \
  -H "Authorization: Bearer <token>"
```

### Marquer la Présence
```bash
curl -X POST http://localhost:5000/api/schedules/456/attendance \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "studentId": "123",
    "status": "present",
    "recordedBy": "789"
  }'
```

## Variables d'Environnement Requises

```env
MONGO_URI=mongodb://localhost:27017/emba_db
JWT_SECRET=your_jwt_secret
PORT=5000
NODE_ENV=development
```

## Limites de Taux

- 100 requêtes par 15 minutes par IP
- Taille maximale des fichiers : 10MB
- Timeout des requêtes : 30 secondes

## Support

Pour toute question sur l'API :
- Email: <EMAIL>
- Documentation complète : [docs.emba-system.com](https://docs.emba-system.com)

---

**Version API :** 1.0.0  
**Dernière mise à jour :** 2024
