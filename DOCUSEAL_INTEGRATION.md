# Migration vers DocuSeal

## Changements effectués

### 1. Service de signature
- **Ancien**: YouSign (`backend/services/youSignService.js`)
- **Nouveau**: DocuSeal (`backend/services/docuSealService.js`)

### 2. API DocuSeal vs YouSign

#### YouSign (Ancien)
```javascript
// Processus en 4 étapes
1. Créer une demande de signature
2. Uploader le document
3. Ajouter le signataire
4. Activer la demande
```

#### DocuSeal (Nouveau)
```javascript
// Processus en 2 étapes
1. Créer un template avec champs de signature
2. Créer une soumission avec document et signataire
```

### 3. Modèle de données mis à jour

#### Ancien (YouSign)
```javascript
signature: {
  provider: 'yousign',
  signatureRequestId: String,
  documentId: String,
  signerId: String,
  status: String
}
```

#### Nouveau (DocuSeal)
```javascript
signature: {
  provider: 'docuseal',
  submissionId: String,
  templateId: String,
  status: String
}
```

### 4. Webhooks

#### YouSign
- Endpoint: `/api/applications/yousign-webhook`
- Events: `signature_request.signed`, `signature_request.declined`

#### DocuSeal
- Endpoint: `/api/applications/docuseal-webhook`
- Events: `submission.completed`, `submission.declined`

### 5. Configuration

Mettre à jour le fichier `.env`:
```env
# DocuSeal Configuration
DOCUSEAL_API_URL=https://api.docuseal.co
DOCUSEAL_API_KEY=YOUR_DOCUSEAL_API_KEY_HERE
DOCUSEAL_WEBHOOK_SECRET=YOUR_DOCUSEAL_WEBHOOK_SECRET_HERE
```

### 6. Avantages de DocuSeal

1. **API plus simple**: Moins d'étapes pour créer une demande de signature
2. **Templates réutilisables**: Créer des templates une fois, les réutiliser
3. **Interface moderne**: Interface utilisateur plus intuitive
4. **Pricing**: Généralement plus abordable que YouSign
5. **Documentation**: API bien documentée et exemples clairs

### 7. Configuration DocuSeal

1. Créer un compte sur [DocuSeal](https://www.docuseal.co/)
2. Obtenir votre API key depuis le dashboard
3. Configurer le webhook endpoint dans DocuSeal:
   - URL: `https://votre-domaine.com/api/applications/docuseal-webhook`
   - Events: `submission.completed`, `submission.declined`

### 8. Test de l'intégration

Pour tester l'intégration:
1. Remplir le formulaire de candidature
2. Soumettre à l'étape 4
3. Vérifier les logs du serveur pour les messages DocuSeal
4. Vérifier la réception de l'email de signature

### 9. Prochaines étapes

- [ ] Configurer un compte DocuSeal de production
- [ ] Mettre à jour les clés API en production
- [ ] Tester le processus complet de signature
- [ ] Implémenter la génération de PDF avec les données de candidature
- [ ] Ajouter la gestion des erreurs avancée
