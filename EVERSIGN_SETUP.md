# Configuration EverSign pour EMBA - Guide Complet

## 🎯 Pourquoi EverSign ?

- **API simple et puissante**
- **Légalement reconnu** dans le monde entier
- **Interface professionnelle**
- **Excellent support**
- **Tarification flexible**
- **Webhooks en temps réel**

## 📋 Étapes pour obtenir les clés API

### 1. Créer un compte EverSign

1. Allez sur [eversign.com](https://eversign.com)
2. Cliquez sur **"Get Started"** ou **"Sign Up"**
3. Créez votre compte avec votre email professionnel
4. Vérifiez votre email et activez votre compte
5. Complétez votre profil d'entreprise

### 2. Accéder aux paramètres API

1. Connectez-vous à votre compte EverSign
2. Cliquez sur votre **profil** (en haut à droite)
3. Sélectionnez **"API Settings"** ou **"Paramètres API"**
4. Ou allez directement dans **Settings → API**

### 3. Générer votre clé API

1. Dans la section **"API Access"**
2. Cliquez sur **"Generate API Key"** ou **"Générer une clé API"**
3. Copiez votre **API Key** (gardez-la secrète !)
4. Notez votre **Business ID** (affiché dans les paramètres)

### 4. Configurer les Webhooks (Important)

1. Dans **"API Settings"**
2. Section **"Webhooks"**
3. Ajoutez l'URL : `http://localhost:5000/api/applications/eversign-webhook`
4. Sélectionnez les événements :
   - ✅ `document_signed`
   - ✅ `document_declined`
   - ✅ `document_completed`
5. Sauvegardez

## 🔧 Configuration dans votre projet

### 1. Mettre à jour le fichier `.env`

```env
# EverSign Configuration
EVERSIGN_API_KEY=votre_api_key_ici
EVERSIGN_BUSINESS_ID=votre_business_id_ici
EVERSIGN_WEBHOOK_URL=http://localhost:5000/api/applications/eversign-webhook
```

### 2. Exemple de clés (format)

```env
# Exemple (ne pas utiliser ces valeurs)
EVERSIGN_API_KEY=1a2b3c4d5e6f7g8h9i0j1k2l3m4n5o6p7q8r9s0t
EVERSIGN_BUSINESS_ID=123456
```

## 🔍 Où trouver vos clés

### API Key
- **Localisation** : Settings → API → API Access
- **Format** : Longue chaîne alphanumérique (40+ caractères)
- **Exemple** : `a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0`

### Business ID
- **Localisation** : Settings → API → Business Information
- **Format** : Nombre (généralement 6 chiffres)
- **Exemple** : `123456`

## 🧪 Test de la configuration

### 1. Redémarrer le serveur

```bash
cd backend
npm run dev
```

### 2. Vérifier les logs

Vous devriez voir :
```
📝 Utilisation d'EverSign pour la signature électronique
```

### 3. Tester une candidature

1. Soumettez une candidature depuis le formulaire
2. Vérifiez que vous recevez un email d'EverSign
3. Signez le document
4. Vérifiez que le webhook met à jour le statut

## 🔍 Dépannage

### Erreur "Configuration EverSign manquante"

- Vérifiez que `EVERSIGN_API_KEY` est définie
- Vérifiez que `EVERSIGN_BUSINESS_ID` est définie
- Assurez-vous qu'il n'y a pas d'espaces avant/après
- Redémarrez le serveur après modification

### Erreur "API Key invalide"

- Vérifiez que la clé API est correcte
- Assurez-vous que votre compte EverSign est activé
- Vérifiez que l'API est activée dans votre compte

### Pas d'email reçu

- Vérifiez votre dossier spam
- Assurez-vous que l'email du candidat est correct
- Vérifiez les logs du serveur pour les erreurs EverSign

### Webhook ne fonctionne pas

- En local, utilisez ngrok pour exposer votre serveur :
  ```bash
  ngrok http 5000
  ```
- Mettez à jour l'URL webhook dans EverSign avec l'URL ngrok
- Vérifiez que l'endpoint répond correctement

## 💰 Tarification EverSign

### Plan Gratuit
- **5 documents par mois**
- Toutes les fonctionnalités de base
- Support email

### Plan Pro ($20/mois)
- **100 documents par mois**
- API complète
- Webhooks
- Support prioritaire

### Plan Business ($40/mois)
- **500 documents par mois**
- Fonctionnalités avancées
- Support téléphonique

## 🚀 Passage en production

### 1. Domaine personnalisé

Remplacez `localhost` par votre domaine :

```env
EVERSIGN_WEBHOOK_URL=https://votre-domaine.com/api/applications/eversign-webhook
```

### 2. Mise à jour des webhooks EverSign

1. Retournez dans les paramètres API EverSign
2. Modifiez l'URL webhook pour utiliser le vrai domaine
3. Testez le webhook

### 3. Mode Production

Assurez-vous que `NODE_ENV=production` pour désactiver le mode sandbox.

## 📞 Support

- **Documentation EverSign** : [eversign.com/api/documentation](https://eversign.com/api/documentation)
- **Support EverSign** : <EMAIL>
- **Status page** : [status.eversign.com](https://status.eversign.com)

## ✅ Checklist finale

- [ ] Compte EverSign créé et vérifié
- [ ] API Key générée et copiée
- [ ] Business ID récupéré
- [ ] Webhooks configurés avec les bons événements
- [ ] Clés ajoutées dans `.env`
- [ ] Serveur redémarré
- [ ] Test de candidature effectué
- [ ] Email de signature reçu et testé
- [ ] Webhook testé et fonctionnel

## 🎉 Résultat attendu

Une fois configuré, voici le processus complet :

1. **Candidat soumet** sa candidature
2. **EverSign crée** automatiquement un document
3. **Email envoyé** au candidat avec lien de signature
4. **Candidat signe** électroniquement
5. **Webhook notifie** votre système
6. **Statut mis à jour** automatiquement
7. **Document signé** téléchargé et stocké

**Votre système de signature électronique EverSign sera opérationnel ! 🎉**
