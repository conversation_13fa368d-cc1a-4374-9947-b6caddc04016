# 🚀 Guide de Démarrage Rapide - EMBA Management System

## Prérequis
- Node.js >= 16.0.0
- MongoDB >= 5.0 (local ou cloud)
- npm >= 8.0.0

## Installation en 5 Minutes

### 1. <PERSON><PERSON><PERSON> et Installer
```bash
# Naviguer vers le dossier backend
cd EMBA_Project/backend

# Installer les dépendances
npm install
```

### 2. Vérifier le Système
```bash
# Vérifier que tout est correctement configuré
npm run check
```

### 3. Configurer MongoDB
Assurez-vous que MongoDB est démarré :

**Option A - MongoDB Local :**
```bash
# Démarrer MongoDB (selon votre installation)
mongod
# ou
brew services start mongodb/brew/mongodb-community
# ou
sudo systemctl start mongod
```

**Option B - MongoDB Cloud (Atlas) :**
1. Créez un compte sur [MongoDB Atlas](https://www.mongodb.com/cloud/atlas)
2. C<PERSON>ez un cluster gratuit
3. Obtenez votre URI de connexion
4. Modifiez `MONGO_URI` dans le fichier `.env`

### 4. Initialiser avec des Données de Test
```bash
# Créer des utilisateurs, étudiants, professeurs et cours de test
npm run seed
```

### 5. Démarrer le Serveur
```bash
# Mode développement avec rechargement automatique
npm run dev
```

## ✅ Vérification du Fonctionnement

### Test de l'API
Ouvrez votre navigateur ou utilisez curl :

```bash
# Test de base
curl http://localhost:5000

# Health check
curl http://localhost:5000/health

# Test des utilisateurs (nécessite authentification)
curl http://localhost:5000/api/users
```

### Comptes de Test Créés
Après avoir exécuté `npm run seed`, vous aurez :

**👨‍💼 Administrateur :**
- Email: `<EMAIL>`
- Mot de passe: `admin123`

**🎓 Étudiant :**
- Email: `<EMAIL>`
- Mot de passe: `student123`

**👨‍🏫 Professeur :**
- Email: `<EMAIL>`
- Mot de passe: `prof123`

## 🔧 Configuration Personnalisée

### Variables d'Environnement Importantes
Éditez le fichier `.env` pour personnaliser :

```env
# Base de données (changez si nécessaire)
MONGO_URI=mongodb://localhost:27017/EMBA

# Sécurité JWT (OBLIGATOIRE en production)
JWT_SECRET=votre_clé_secrète_très_sécurisée

# Email (pour les notifications)
EMAIL_HOST=smtp.gmail.com
EMAIL_USER=<EMAIL>
EMAIL_PASS=votre_mot_de_passe_app

# Port du serveur
PORT=5000
```

### Configuration Email (Optionnel)
Pour activer les notifications par email :

1. **Gmail :**
   - Activez l'authentification à 2 facteurs
   - Générez un mot de passe d'application
   - Utilisez ce mot de passe dans `EMAIL_PASS`

2. **Autres fournisseurs :**
   - Modifiez `EMAIL_HOST` et `EMAIL_PORT`
   - Configurez les credentials appropriés

## 📊 Endpoints Principaux

### Authentification
```bash
POST /api/auth/login
POST /api/auth/register
```

### Gestion des Données
```bash
GET /api/users          # Utilisateurs
GET /api/students       # Étudiants
GET /api/professors     # Professeurs
GET /api/courses        # Cours
GET /api/modules        # Modules
GET /api/enrollments    # Inscriptions
GET /api/grades         # Notes
GET /api/payments       # Paiements
```

## 🐛 Résolution de Problèmes

### Erreur de Connexion MongoDB
```bash
# Vérifier que MongoDB est démarré
ps aux | grep mongod

# Ou vérifier le service
sudo systemctl status mongod
```

### Port Déjà Utilisé
```bash
# Changer le port dans .env
PORT=5001

# Ou tuer le processus utilisant le port 5000
lsof -ti:5000 | xargs kill -9
```

### Erreur de Dépendances
```bash
# Nettoyer et réinstaller
rm -rf node_modules package-lock.json
npm install
```

### Problème de Permissions
```bash
# Vérifier les permissions du dossier
ls -la
chmod 755 .
```

## 📱 Prochaines Étapes

### Développement Frontend
1. Créer une interface React
2. Connecter aux APIs backend
3. Implémenter l'authentification JWT

### Fonctionnalités Avancées
1. Upload de fichiers
2. Notifications en temps réel
3. Rapports et analytics
4. Intégration paiements

### Déploiement
1. Configuration production
2. Variables d'environnement sécurisées
3. Base de données cloud
4. Serveur de production

## 📞 Support

### Logs et Debugging
```bash
# Voir les logs en temps réel
npm run dev

# Vérifier les erreurs MongoDB
tail -f /var/log/mongodb/mongod.log
```

### Commandes Utiles
```bash
npm run check     # Vérifier le système
npm run seed      # Réinitialiser les données
npm run dev       # Mode développement
npm start         # Mode production
```

### Ressources
- [Documentation MongoDB](https://docs.mongodb.com/)
- [Express.js Guide](https://expressjs.com/)
- [Mongoose Documentation](https://mongoosejs.com/)

---

**🎉 Félicitations !** Votre système EMBA est maintenant opérationnel !

Pour toute question : [Créer une issue](https://github.com/votre-repo/issues)
