# EMBA Management System

Un système de gestion complet pour les programmes Executive MBA (EMBA) développé avec Node.js, Express, MongoDB et React.

## 🚀 Fonctionnalités

### Gestion des Utilisateurs
- **Authentification et autorisation** avec rôles (étudiant, professeur, admin, staff)
- **Profils utilisateurs** complets avec informations personnelles et professionnelles
- **Système de permissions** granulaire
- **Réinitialisation de mot de passe** et vérification d'email

### Gestion Académique
- **Étudiants** : Profils complets, progression académique, présence, notes
- **Professeurs** : Profils académiques, expérience, publications, évaluations
- **Cours** : Planification, contenu, évaluations, statistiques
- **Modules** : Organisation modulaire des cours, prérequis, compétences

### Système d'Inscription
- **Candidatures** : Processus complet de candidature avec documents
- **Entretiens** : Planification et évaluation des candidats
- **Inscriptions** : Gestion des inscriptions et statuts des étudiants

### Gestion des Cours
- **Planification** : Horaires, salles, ressources
- **Devoirs** : Création, soumission, notation automatisée
- **Présence** : Suivi automatique avec alertes
- **Évaluations** : Système de notation flexible avec rubrics

### Système Financier
- **Paiements** : Gestion complète des frais de scolarité
- **Plans de paiement** : Échelonnement et rappels automatiques
- **Bourses** : Gestion des remises et aides financières
- **Rapports financiers** : Statistiques et analyses

### Communication
- **Notifications** : Multi-canal (email, SMS, push, in-app)
- **Documents** : Gestion centralisée avec versioning
- **Messagerie** : Communication entre utilisateurs

## 🏗️ Architecture

### Backend (Node.js + Express + MongoDB)
```
backend/
├── models/           # Modèles Mongoose
├── routes/           # Routes API REST
├── middleware/       # Middlewares personnalisés
├── controllers/      # Logique métier
├── services/         # Services externes
├── utils/           # Utilitaires
└── server.js        # Point d'entrée
```

### Frontend (React)
```
frontend/
├── src/
│   ├── components/   # Composants réutilisables
│   ├── pages/       # Pages de l'application
│   ├── services/    # Services API
│   ├── contexts/    # Contexts React
│   ├── hooks/       # Hooks personnalisés
│   └── utils/       # Utilitaires
└── public/          # Fichiers statiques
```

## 📊 Modèles de Données

### Modèles Principaux

1. **User** - Utilisateurs du système
2. **Student** - Profils étudiants EMBA
3. **Professor** - Profils professeurs
4. **Course** - Cours individuels
5. **Module** - Groupes de cours
6. **Enrollment** - Inscriptions étudiants
7. **Schedule** - Planification des sessions
8. **Grade** - Notes et évaluations
9. **Assignment** - Devoirs et projets
10. **Attendance** - Présence des étudiants
11. **Payment** - Gestion financière
12. **Notification** - Système de notifications
13. **Document** - Gestion documentaire
14. **Application** - Candidatures EMBA

### Relations Clés
- User → Student/Professor (1:1)
- Course → Enrollment → Student (N:M)
- Professor → Course (1:N)
- Assignment → Submission → Grade (1:N:1)
- Schedule → Attendance (1:N)

## 🛠️ Installation

### Prérequis
- Node.js >= 16.0.0
- MongoDB >= 5.0
- npm >= 8.0.0

### Backend
```bash
cd backend
npm install
# Le fichier .env est déjà configuré avec les valeurs par défaut
# Modifiez les variables selon vos besoins (email, JWT secret, etc.)
npm run dev
```

### Frontend
```bash
cd frontend
npm install
npm start
```

### Variables d'Environnement
Le fichier `.env` est déjà configuré avec les valeurs par défaut. Voici les principales variables à personnaliser :

```env
# Base de données
MONGO_URI=mongodb://localhost:27017/EMBA

# JWT (IMPORTANT: Changez cette clé en production !)
JWT_SECRET=emba_super_secret_jwt_key_change_in_production_2024

# Email (Configurez selon votre fournisseur)
EMAIL_HOST=smtp.gmail.com
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password

# Serveur
PORT=5000
NODE_ENV=development
```

**⚠️ Important :** En production, assurez-vous de :
- Changer le `JWT_SECRET` par une clé sécurisée
- Configurer les paramètres email avec vos vraies credentials
- Définir `NODE_ENV=production`

## 📡 API Endpoints

### Authentification
- `POST /api/auth/login` - Connexion
- `POST /api/auth/register` - Inscription
- `POST /api/auth/forgot-password` - Mot de passe oublié

### Utilisateurs
- `GET /api/users` - Liste des utilisateurs
- `GET /api/users/:id` - Détails utilisateur
- `POST /api/users` - Créer utilisateur
- `PUT /api/users/:id` - Modifier utilisateur

### Étudiants
- `GET /api/students` - Liste des étudiants
- `GET /api/students/:id/progress` - Progression académique
- `GET /api/students/:id/attendance` - Statistiques présence

### Cours
- `GET /api/courses` - Liste des cours
- `POST /api/courses` - Créer cours
- `GET /api/courses/:id/students` - Étudiants inscrits

### Paiements
- `GET /api/payments` - Liste des paiements
- `POST /api/payments/:id/process` - Traiter paiement
- `GET /api/payments/overdue` - Paiements en retard

## 🔒 Sécurité

- **Authentification JWT** avec refresh tokens
- **Validation des données** avec express-validator
- **Rate limiting** pour prévenir les attaques
- **Helmet** pour sécuriser les headers HTTP
- **Chiffrement des mots de passe** avec bcrypt
- **Validation des permissions** par rôle

## 📈 Fonctionnalités Avancées

### Analytics et Rapports
- Tableaux de bord personnalisés
- Statistiques de performance
- Rapports financiers
- Analyses de présence

### Notifications Intelligentes
- Rappels automatiques
- Alertes de performance
- Notifications push en temps réel
- Templates personnalisables

### Gestion Documentaire
- Upload sécurisé de fichiers
- Versioning automatique
- Contrôle d'accès granulaire
- Recherche full-text

## 🧪 Tests

```bash
# Tests backend
cd backend
npm test

# Tests frontend
cd frontend
npm test
```

## 🚀 Déploiement

### Production
```bash
# Build frontend
cd frontend
npm run build

# Démarrer backend
cd backend
npm start
```

### Docker
```bash
docker-compose up -d
```

## 📝 Contribution

1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/AmazingFeature`)
3. Commit les changes (`git commit -m 'Add AmazingFeature'`)
4. Push vers la branche (`git push origin feature/AmazingFeature`)
5. Ouvrir une Pull Request

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 👥 Équipe

- **Développement Backend** - API REST, Base de données
- **Développement Frontend** - Interface utilisateur React
- **DevOps** - Déploiement et infrastructure
- **QA** - Tests et assurance qualité

## 📞 Support

Pour toute question ou support :
- Email: <EMAIL>
- Documentation: [docs.emba-system.com](https://docs.emba-system.com)
- Issues: [GitHub Issues](https://github.com/emba-system/issues)

---

**EMBA Management System** - Transforming Executive Education Management
