# 📧 Configuration Email pour EMBA System

## 🎯 Objectif
Ce guide vous aide à configurer l'envoi d'emails automatiques pour le système EMBA, notamment pour l'envoi des identifiants de connexion aux nouveaux étudiants.

## ⚙️ Configuration Actuelle

### Variables d'Environnement (fichier `.env`)
```env
# Configuration Email
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=233AMT2237
EMAIL_FROM=<EMAIL>
```

## 🔧 Options de Configuration

### Option 1: Gmail (Recommandé pour les tests)

1. **Activer l'authentification à 2 facteurs** sur votre compte Gmail
2. **Générer un mot de passe d'application** :
   - Allez dans les paramètres de votre compte Google
   - Sécurité → Authentification à 2 facteurs → Mots de passe des applications
   - Gén<PERSON>rez un mot de passe pour "Mail"

3. **Configurer les variables** :
```env
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=votre-mot-de-passe-application
EMAIL_FROM=<EMAIL>
```

### Option 2: Outlook/Hotmail

```env
EMAIL_HOST=smtp-mail.outlook.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=votre-mot-de-passe
EMAIL_FROM=<EMAIL>
```

### Option 3: Serveur SMTP Personnalisé

```env
EMAIL_HOST=smtp.votre-domaine.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=votre-mot-de-passe
EMAIL_FROM=<EMAIL>
```

## 🧪 Test de Configuration

### Méthode 1: Script de Test
```bash
cd backend
node test-email.js
```

### Méthode 2: Test Manuel
1. Validez une candidature dans l'interface admin
2. Vérifiez les logs du serveur
3. Vérifiez la réception de l'email

## 🔍 Diagnostic des Problèmes

### Erreur: "createTransporter is not a function"
✅ **Résolu** - Correction appliquée dans `emailService.js`

### Erreur: "Invalid login"
- Vérifiez vos identifiants
- Pour Gmail, utilisez un mot de passe d'application
- Vérifiez que l'authentification à 2 facteurs est activée

### Erreur: "Connection timeout"
- Vérifiez votre connexion internet
- Vérifiez les paramètres de pare-feu
- Essayez un autre port (465 avec `EMAIL_SECURE=true`)

### Erreur: "Authentication failed"
- Vérifiez le nom d'utilisateur et mot de passe
- Pour Gmail, activez "Accès aux applications moins sécurisées" (non recommandé)
- Utilisez plutôt un mot de passe d'application

## 🛡️ Mode Fallback

Le système est configuré pour continuer à fonctionner même si l'email ne peut pas être envoyé :

- ✅ Le compte étudiant est créé
- ✅ Les identifiants sont affichés dans les logs
- ⚠️ L'email n'est pas envoyé mais le processus continue

## 📋 Logs à Surveiller

### Succès
```
📧 Configuration email: { host: 'smtp.gmail.com', port: 587, user: '<EMAIL>' }
📧 Tentative d'envoi d'email à: <EMAIL>
✅ Email envoyé avec succès: <message-id>
```

### Mode Simulation
```
⚠️ Variables d'environnement EMAIL_USER et EMAIL_PASS non configurées
📧 Les emails ne seront pas envoyés
⚠️ Transporteur email non disponible - Simulation d'envoi
📧 Email simulé envoyé à: <EMAIL>
```

### Mode Fallback
```
❌ Erreur lors de l'envoi de l'email: [détails de l'erreur]
⚠️ Email non envoyé mais processus continué
```

## 🎯 Recommandations

### Pour le Développement
- Utilisez Gmail avec un mot de passe d'application
- Testez avec votre propre adresse email
- Surveillez les logs pour diagnostiquer les problèmes

### Pour la Production
- Utilisez un service email professionnel (SendGrid, Mailgun, etc.)
- Configurez un domaine email dédié
- Mettez en place une surveillance des emails

### Sécurité
- Ne jamais commiter les mots de passe dans le code
- Utilisez des mots de passe d'application pour Gmail
- Changez régulièrement les mots de passe

## 🚀 Services Email Recommandés pour la Production

1. **SendGrid** - Service professionnel avec API
2. **Mailgun** - Bon pour les applications
3. **Amazon SES** - Intégré avec AWS
4. **Postmark** - Spécialisé dans les emails transactionnels

## 📞 Support

Si vous rencontrez des problèmes :
1. Vérifiez les logs du serveur
2. Testez avec le script `test-email.js`
3. Vérifiez votre configuration `.env`
4. Consultez la documentation de votre fournisseur email
