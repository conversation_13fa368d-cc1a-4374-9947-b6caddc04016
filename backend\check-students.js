const mongoose = require('mongoose');
const User = require('./models/User');
const Student = require('./models/Student');

async function checkStudents() {
  try {
    await mongoose.connect('mongodb://localhost:27017/emba_management');
    console.log('✅ Connecté à MongoDB');

    // Vérifier les utilisateurs avec le rôle student
    const studentUsers = await User.find({ role: 'student' });
    console.log(`\n📊 Utilisateurs avec rôle 'student': ${studentUsers.length}`);
    
    studentUsers.forEach((user, index) => {
      console.log(`${index + 1}. ${user.firstName} ${user.lastName} (${user.email}) - Actif: ${user.isActive}`);
    });

    // Vérifier les profils étudiants
    const students = await Student.find().populate('user', 'firstName lastName email role');
    console.log(`\n📚 Profils étudiants: ${students.length}`);
    
    students.forEach((student, index) => {
      console.log(`${index + 1}. ${student.studentNumber} - ${student.user?.firstName} ${student.user?.lastName} (${student.user?.email})`);
    });

    // Test de connexion avec les identifiants de seed
    const testUser = await User.findOne({ email: '<EMAIL>' });
    if (testUser) {
      console.log('\n🔍 Test utilisateur Ahmed Ben Ali:');
      console.log(`   Email: ${testUser.email}`);
      console.log(`   Rôle: ${testUser.role}`);
      console.log(`   Actif: ${testUser.isActive}`);
      console.log(`   Vérifié: ${testUser.isVerified}`);
      
      // Test du mot de passe
      const isPasswordValid = await testUser.comparePassword('student123');
      console.log(`   Mot de passe 'student123' valide: ${isPasswordValid}`);
    } else {
      console.log('\n❌ Utilisateur Ahmed Ben Ali non trouvé');
    }

  } catch (error) {
    console.error('❌ Erreur:', error);
  } finally {
    await mongoose.disconnect();
  }
}

checkStudents();
