const mongoose = require('mongoose');
const User = require('./models/User');
const Student = require('./models/Student');

async function createTestStudent() {
  try {
    await mongoose.connect('mongodb://localhost:27017/emba_management');
    console.log('✅ Connecté à MongoDB');

    // Vérifier si l'utilisateur existe déjà
    let user = await User.findOne({ email: '<EMAIL>' });
    
    if (!user) {
      // Créer un utilisateur étudiant de test
      user = new User({
        firstName: 'Test',
        lastName: 'Student',
        email: '<EMAIL>',
        password: 'student123',
        role: 'student',
        phone: '+21612345678',
        isActive: true,
        isVerified: true
      });

      await user.save();
      console.log('✅ Utilisateur étudiant créé:', user.email);
    } else {
      console.log('ℹ️  Utilisateur étudiant existe déjà:', user.email);
    }

    // Vérifier si le profil étudiant existe
    let student = await Student.findOne({ user: user._id });
    
    if (!student) {
      // Créer le profil étudiant
      student = new Student({
        user: user._id,
        studentNumber: 'TEST2025001',
        academicYear: '2024-2025',
        cohort: 'TEST2025',
        program: 'EMBA',
        specialization: 'General Management',
        enrollmentStatus: 'active',
        enrollmentDate: new Date(),
        expectedGraduationDate: new Date('2026-06-30'),
        academicRecord: {
          creditsRequired: 60,
          creditsCompleted: 0,
          currentGPA: 0,
          academicStanding: 'satisfactory'
        },
        tuitionFee: {
          total: 25000,
          paid: 0,
          remaining: 25000,
          currency: 'TND',
          paymentPlan: 'installments'
        }
      });

      await student.save();
      console.log('✅ Profil étudiant créé:', student.studentNumber);
    } else {
      console.log('ℹ️  Profil étudiant existe déjà:', student.studentNumber);
    }

    console.log('\n🎯 Identifiants de test:');
    console.log('   Email: <EMAIL>');
    console.log('   Mot de passe: student123');
    console.log('   Rôle: student');

  } catch (error) {
    console.error('❌ Erreur:', error);
  } finally {
    await mongoose.disconnect();
  }
}

createTestStudent();
