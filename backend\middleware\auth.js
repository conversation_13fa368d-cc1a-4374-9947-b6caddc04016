const jwt = require('jsonwebtoken');
const User = require('../models/User');

// Middleware d'authentification
const authenticate = async (req, res, next) => {
  try {
    let token;

    // Vérifier si le token est dans les headers
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1];
    }

    // Vérifier si le token existe
    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Access denied. No token provided.'
      });
    }

    try {
      // Vérifier le token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      
      // Récupérer l'utilisateur
      const user = await User.findById(decoded.id).select('-password');
      
      if (!user) {
        return res.status(401).json({
          success: false,
          message: 'Token is not valid. User not found.'
        });
      }

      // Vérifier si l'utilisateur est actif
      if (!user.isActive) {
        return res.status(401).json({
          success: false,
          message: 'Account is deactivated.'
        });
      }

      // Vérifier si le compte n'est pas verrouillé
      if (user.isLocked) {
        return res.status(401).json({
          success: false,
          message: 'Account is temporarily locked. Please try again later.'
        });
      }

      // Ajouter l'utilisateur à la requête
      req.user = user;
      next();

    } catch (error) {
      return res.status(401).json({
        success: false,
        message: 'Token is not valid.'
      });
    }

  } catch (error) {
    console.error('Authentication error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error during authentication.'
    });
  }
};

// Middleware d'autorisation par rôle
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Access denied. Please authenticate first.'
      });
    }

    // Aplatir le tableau des rôles au cas où il y aurait des tableaux imbriqués
    const flatRoles = roles.flat();

    console.log(`🔐 Authorization check: User role = "${req.user.role}", Required roles = [${flatRoles.join(', ')}]`);

    if (!flatRoles.includes(req.user.role)) {
      console.log(`❌ Access denied for user ${req.user.email} with role "${req.user.role}"`);
      return res.status(403).json({
        success: false,
        message: `Access denied. Required role: ${flatRoles.join(' or ')}, but user has role: ${req.user.role}`
      });
    }

    console.log(`✅ Access granted for user ${req.user.email} with role "${req.user.role}"`);
    next();
  };
};

// Middleware d'autorisation par permission
const requirePermission = (permission) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Access denied. Please authenticate first.'
      });
    }

    if (!req.user.hasPermission(permission)) {
      return res.status(403).json({
        success: false,
        message: `Access denied. Required permission: ${permission}`
      });
    }

    next();
  };
};

// Middleware pour vérifier si l'utilisateur peut accéder à ses propres données
const authorizeOwnerOrAdmin = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      message: 'Access denied. Please authenticate first.'
    });
  }

  const userId = req.params.id || req.params.userId;
  
  // Admin peut accéder à tout
  if (req.user.role === 'admin') {
    return next();
  }

  // L'utilisateur peut accéder à ses propres données
  if (req.user._id.toString() === userId) {
    return next();
  }

  return res.status(403).json({
    success: false,
    message: 'Access denied. You can only access your own data.'
  });
};

// Middleware pour vérifier si l'utilisateur est vérifié
const requireVerification = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      message: 'Access denied. Please authenticate first.'
    });
  }

  if (!req.user.isVerified) {
    return res.status(403).json({
      success: false,
      message: 'Access denied. Please verify your email address first.'
    });
  }

  next();
};

// Middleware optionnel d'authentification (ne bloque pas si pas de token)
const optionalAuth = async (req, res, next) => {
  try {
    let token;

    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1];
    }

    if (token) {
      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const user = await User.findById(decoded.id).select('-password');
        
        if (user && user.isActive && !user.isLocked) {
          req.user = user;
        }
      } catch (error) {
        // Token invalide, mais on continue sans utilisateur
        console.log('Invalid token in optional auth:', error.message);
      }
    }

    next();
  } catch (error) {
    console.error('Optional auth error:', error);
    next();
  }
};

// Middleware pour limiter l'accès selon le rôle et l'entité
const authorizeEntityAccess = (entityType) => {
  return async (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Access denied. Please authenticate first.'
      });
    }

    const entityId = req.params.id;
    const userRole = req.user.role;

    try {
      // Admin a accès à tout
      if (userRole === 'admin') {
        return next();
      }

      // Logique spécifique selon le type d'entité
      switch (entityType) {
        case 'student':
          // Un étudiant ne peut accéder qu'à ses propres données
          if (userRole === 'student') {
            const Student = require('../models/Student');
            const student = await Student.findOne({ user: req.user._id });
            if (student && student._id.toString() === entityId) {
              return next();
            }
          }
          // Les professeurs peuvent accéder aux étudiants de leurs cours
          if (userRole === 'professor') {
            // Logique pour vérifier si le professeur enseigne à cet étudiant
            return next(); // Simplifié pour l'exemple
          }
          break;

        case 'professor':
          // Un professeur ne peut accéder qu'à ses propres données
          if (userRole === 'professor') {
            const Professor = require('../models/Professor');
            const professor = await Professor.findOne({ user: req.user._id });
            if (professor && professor._id.toString() === entityId) {
              return next();
            }
          }
          break;

        case 'course':
          // Les étudiants peuvent accéder aux cours auxquels ils sont inscrits
          // Les professeurs peuvent accéder aux cours qu'ils enseignent
          return next(); // Simplifié pour l'exemple

        default:
          return next();
      }

      return res.status(403).json({
        success: false,
        message: 'Access denied. Insufficient permissions.'
      });

    } catch (error) {
      console.error('Entity access authorization error:', error);
      return res.status(500).json({
        success: false,
        message: 'Server error during authorization.'
      });
    }
  };
};

// Middleware pour enregistrer l'activité utilisateur
const logUserActivity = (action) => {
  return (req, res, next) => {
    if (req.user) {
      // Ici, vous pourriez enregistrer l'activité dans une base de données
      console.log(`User ${req.user.email} performed action: ${action} at ${new Date().toISOString()}`);
      
      // Mettre à jour la dernière activité de l'utilisateur
      req.user.lastLogin = new Date();
      req.user.save().catch(err => console.error('Error updating last login:', err));
    }
    next();
  };
};

module.exports = {
  authenticate,
  authorize,
  requirePermission,
  authorizeOwnerOrAdmin,
  requireVerification,
  optionalAuth,
  authorizeEntityAccess,
  logUserActivity
};
