const { body, param, query, validationResult } = require('express-validator');

// Middleware pour gérer les erreurs de validation
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation errors',
      errors: errors.array().map(error => ({
        field: error.path,
        message: error.msg,
        value: error.value
      }))
    });
  }
  
  next();
};

// Validations pour les utilisateurs
const validateUser = [
  body('firstName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters'),
  
  body('lastName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters'),
  
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  
  body('password')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number'),
  
  body('phone')
    .optional()
    .matches(/^[\+]?[1-9][\d]{0,15}$/)
    .withMessage('Please provide a valid phone number'),
  
  body('role')
    .optional()
    .isIn(['student', 'professor', 'admin', 'staff'])
    .withMessage('Role must be one of: student, professor, admin, staff'),
  
  handleValidationErrors
];

// Validations pour la mise à jour d'utilisateur
const validateUserUpdate = [
  body('firstName')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters'),
  
  body('lastName')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters'),
  
  body('email')
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  
  body('phone')
    .optional()
    .matches(/^[\+]?[1-9][\d]{0,15}$/)
    .withMessage('Please provide a valid phone number'),
  
  handleValidationErrors
];

// Validations pour les étudiants
const validateStudent = [
  body('academicYear')
    .matches(/^\d{4}-\d{4}$/)
    .withMessage('Academic year must be in format YYYY-YYYY'),
  
  body('cohort')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Cohort is required and must be less than 50 characters'),
  
  body('program')
    .isIn(['EMBA', 'Executive MBA', 'Part-time MBA', 'Full-time MBA'])
    .withMessage('Invalid program type'),
  
  body('expectedGraduationDate')
    .isISO8601()
    .withMessage('Expected graduation date must be a valid date'),
  
  body('tuitionFee.total')
    .isFloat({ min: 0 })
    .withMessage('Tuition fee total must be a positive number'),
  
  handleValidationErrors
];

// Validations pour les cours
const validateCourse = [
  body('courseCode')
    .trim()
    .matches(/^[A-Z]{2,4}\d{3,4}$/)
    .withMessage('Course code must be in format ABC123 or ABCD1234'),
  
  body('title')
    .trim()
    .isLength({ min: 5, max: 200 })
    .withMessage('Course title must be between 5 and 200 characters'),
  
  body('description')
    .trim()
    .isLength({ min: 10, max: 2000 })
    .withMessage('Course description must be between 10 and 2000 characters'),
  
  body('creditHours')
    .isInt({ min: 1, max: 10 })
    .withMessage('Credit hours must be between 1 and 10'),
  
  body('contactHours')
    .isInt({ min: 1 })
    .withMessage('Contact hours must be at least 1'),
  
  body('level')
    .isIn(['Foundation', 'Core', 'Elective', 'Capstone', 'Specialization'])
    .withMessage('Invalid course level'),
  
  body('category')
    .isIn(['Management', 'Finance', 'Marketing', 'Operations', 'Strategy', 'Leadership', 'Economics', 'Accounting', 'Information Systems', 'Human Resources', 'Entrepreneurship'])
    .withMessage('Invalid course category'),
  
  body('startDate')
    .isISO8601()
    .withMessage('Start date must be a valid date'),
  
  body('endDate')
    .isISO8601()
    .withMessage('End date must be a valid date')
    .custom((endDate, { req }) => {
      if (new Date(endDate) <= new Date(req.body.startDate)) {
        throw new Error('End date must be after start date');
      }
      return true;
    }),
  
  handleValidationErrors
];

// Validations pour les devoirs
const validateAssignment = [
  body('title')
    .trim()
    .isLength({ min: 5, max: 200 })
    .withMessage('Assignment title must be between 5 and 200 characters'),
  
  body('description')
    .trim()
    .isLength({ min: 10, max: 2000 })
    .withMessage('Assignment description must be between 10 and 2000 characters'),
  
  body('type')
    .isIn(['homework', 'project', 'essay', 'case_study', 'presentation', 'quiz', 'exam', 'lab', 'research', 'group_work'])
    .withMessage('Invalid assignment type'),
  
  body('dueDate')
    .isISO8601()
    .withMessage('Due date must be a valid date')
    .custom((dueDate) => {
      if (new Date(dueDate) <= new Date()) {
        throw new Error('Due date must be in the future');
      }
      return true;
    }),
  
  body('grading.totalPoints')
    .isFloat({ min: 1 })
    .withMessage('Total points must be at least 1'),
  
  body('grading.weight')
    .isFloat({ min: 0, max: 100 })
    .withMessage('Weight must be between 0 and 100'),
  
  handleValidationErrors
];

// Validations pour les notes
const validateGrade = [
  body('gradeType')
    .isIn(['assignment', 'quiz', 'midterm', 'final_exam', 'project', 'participation', 'attendance', 'overall'])
    .withMessage('Invalid grade type'),
  
  body('rawScore')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('Raw score must be between 0 and 100'),
  
  body('maxScore')
    .isFloat({ min: 1 })
    .withMessage('Max score must be at least 1'),
  
  body('gradeOutOf20')
    .optional()
    .isFloat({ min: 0, max: 20 })
    .withMessage('Grade out of 20 must be between 0 and 20'),
  
  body('assessmentDate')
    .isISO8601()
    .withMessage('Assessment date must be a valid date'),
  
  handleValidationErrors
];

// Validations pour les paiements
const validatePayment = [
  body('amount')
    .isFloat({ min: 0 })
    .withMessage('Payment amount must be a positive number'),
  
  body('currency')
    .isIn(['TND', 'USD', 'EUR'])
    .withMessage('Currency must be TND, USD, or EUR'),
  
  body('paymentType')
    .isIn(['tuition', 'registration', 'exam_fee', 'material_fee', 'late_fee', 'penalty', 'refund', 'scholarship', 'other'])
    .withMessage('Invalid payment type'),
  
  body('paymentMethod')
    .isIn(['cash', 'bank_transfer', 'credit_card', 'debit_card', 'check', 'online', 'mobile_payment', 'installment'])
    .withMessage('Invalid payment method'),
  
  body('dueDate')
    .isISO8601()
    .withMessage('Due date must be a valid date'),
  
  handleValidationErrors
];

// Validations pour les candidatures
const validateApplication = [
  body('firstName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters'),

  body('lastName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters'),

  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),

  body('dateOfBirth')
    .isISO8601()
    .withMessage('Date of birth must be a valid date')
    .custom((dateOfBirth) => {
      const age = (new Date() - new Date(dateOfBirth)) / (1000 * 60 * 60 * 24 * 365.25);
      if (age < 18 || age > 100) {
        throw new Error('Age must be between 18 and 100 years');
      }
      return true;
    }),
  
  body('programInfo.program')
    .isIn(['EMBA', 'Executive MBA', 'Part-time MBA', 'Full-time MBA'])
    .withMessage('Invalid program type'),
  
  body('programInfo.intakeYear')
    .matches(/^\d{4}$/)
    .withMessage('Intake year must be a 4-digit year'),
  
  body('motivation.whyMBA')
    .trim()
    .isLength({ min: 100, max: 2000 })
    .withMessage('Why MBA essay must be between 100 and 2000 characters'),
  
  body('motivation.careerGoals')
    .trim()
    .isLength({ min: 50, max: 1500 })
    .withMessage('Career goals must be between 50 and 1500 characters'),
  
  handleValidationErrors
];

// Validations pour les paramètres de requête
const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  
  handleValidationErrors
];

// Validations pour les paramètres d'ID
const validateObjectId = [
  param('id')
    .isMongoId()
    .withMessage('Invalid ID format'),
  
  handleValidationErrors
];

// Validation pour le changement de mot de passe
const validatePasswordChange = [
  body('currentPassword')
    .notEmpty()
    .withMessage('Current password is required'),
  
  body('newPassword')
    .isLength({ min: 6 })
    .withMessage('New password must be at least 6 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('New password must contain at least one lowercase letter, one uppercase letter, and one number'),
  
  body('confirmPassword')
    .custom((confirmPassword, { req }) => {
      if (confirmPassword !== req.body.newPassword) {
        throw new Error('Password confirmation does not match new password');
      }
      return true;
    }),
  
  handleValidationErrors
];

module.exports = {
  handleValidationErrors,
  validateUser,
  validateUserUpdate,
  validateStudent,
  validateCourse,
  validateAssignment,
  validateGrade,
  validatePayment,
  validateApplication,
  validatePagination,
  validateObjectId,
  validatePasswordChange
};
