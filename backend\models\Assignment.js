const mongoose = require('mongoose');

const assignmentSchema = new mongoose.Schema({
  // Informations de base
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  description: {
    type: String,
    required: true,
    trim: true,
    maxlength: 2000
  },
  instructions: {
    type: String,
    trim: true,
    maxlength: 5000
  },
  
  // Références
  course: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Course',
    required: true
  },
  module: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Module'
  },
  instructor: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Professor',
    required: true
  },
  
  // Classification
  type: {
    type: String,
    enum: ['homework', 'project', 'essay', 'case_study', 'presentation', 'quiz', 'exam', 'lab', 'research', 'group_work'],
    required: true
  },
  category: {
    type: String,
    enum: ['individual', 'group', 'peer_review', 'self_assessment'],
    default: 'individual'
  },
  difficulty: {
    type: String,
    enum: ['beginner', 'intermediate', 'advanced', 'expert'],
    default: 'intermediate'
  },
  
  // Dates et délais
  assignedDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  dueDate: {
    type: Date,
    required: true
  },
  availableFrom: {
    type: Date,
    default: Date.now
  },
  availableUntil: {
    type: Date
  },
  
  // Paramètres de soumission
  submissionSettings: {
    allowLateSubmission: { type: Boolean, default: true },
    latePenaltyPerDay: { type: Number, min: 0, max: 100, default: 5 },
    maxLateDays: { type: Number, min: 0, default: 7 },
    allowMultipleAttempts: { type: Boolean, default: false },
    maxAttempts: { type: Number, min: 1, default: 1 },
    submissionFormat: {
      type: String,
      enum: ['online', 'paper', 'presentation', 'both'],
      default: 'online'
    },
    fileTypes: [{ type: String, trim: true }], // e.g., ['pdf', 'docx', 'pptx']
    maxFileSize: { type: Number, default: 10 }, // in MB
    plagiarismCheck: { type: Boolean, default: true }
  },
  
  // Notation et évaluation
  grading: {
    totalPoints: { type: Number, required: true, min: 1, default: 100 },
    weight: { type: Number, min: 0, max: 100, required: true },
    gradingMethod: {
      type: String,
      enum: ['points', 'percentage', 'rubric', 'pass_fail'],
      default: 'points'
    },
    passingScore: { type: Number, min: 0, default: 60 },
    rubric: [{
      criterion: { type: String, required: true, trim: true },
      description: { type: String, trim: true },
      maxPoints: { type: Number, required: true, min: 1 },
      levels: [{
        name: { type: String, required: true, trim: true },
        description: { type: String, trim: true },
        points: { type: Number, required: true, min: 0 }
      }]
    }]
  },
  
  // Objectifs d'apprentissage
  learningObjectives: [{
    objective: { type: String, required: true, trim: true },
    bloomLevel: {
      type: String,
      enum: ['Remember', 'Understand', 'Apply', 'Analyze', 'Evaluate', 'Create']
    }
  }],
  
  // Ressources et matériaux
  resources: {
    requiredReadings: [{
      title: { type: String, required: true, trim: true },
      author: { type: String, trim: true },
      source: { type: String, trim: true },
      url: { type: String, trim: true },
      pages: { type: String, trim: true }
    }],
    supplementaryMaterials: [{
      title: { type: String, required: true, trim: true },
      type: {
        type: String,
        enum: ['video', 'article', 'website', 'software', 'dataset', 'template']
      },
      url: { type: String, trim: true },
      description: { type: String, trim: true }
    }],
    attachments: [{
      filename: { type: String, required: true },
      originalName: { type: String, required: true },
      path: { type: String, required: true },
      size: { type: Number },
      mimeType: { type: String },
      uploadDate: { type: Date, default: Date.now }
    }]
  },
  
  // Paramètres de groupe (si applicable)
  groupSettings: {
    isGroupAssignment: { type: Boolean, default: false },
    minGroupSize: { type: Number, min: 1, default: 1 },
    maxGroupSize: { type: Number, min: 1, default: 1 },
    allowSelfSelection: { type: Boolean, default: true },
    groupFormationDeadline: { type: Date },
    groups: [{
      name: { type: String, trim: true },
      members: [{
        student: {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'Student'
        },
        role: {
          type: String,
          enum: ['leader', 'member', 'coordinator'],
          default: 'member'
        }
      }],
      createdDate: { type: Date, default: Date.now }
    }]
  },
  
  // Soumissions
  submissions: [{
    student: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Student',
      required: true
    },
    group: { type: String, trim: true },
    submissionDate: { type: Date, default: Date.now },
    attemptNumber: { type: Number, min: 1, default: 1 },
    isLate: { type: Boolean, default: false },
    daysLate: { type: Number, min: 0, default: 0 },
    
    // Contenu de la soumission
    textSubmission: { type: String, trim: true },
    files: [{
      filename: { type: String, required: true },
      originalName: { type: String, required: true },
      path: { type: String, required: true },
      size: { type: Number },
      mimeType: { type: String },
      uploadDate: { type: Date, default: Date.now }
    }],
    
    // Évaluation
    grade: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Grade'
    },
    feedback: { type: String, trim: true },
    rubricScores: [{
      criterion: { type: String, required: true },
      score: { type: Number, required: true, min: 0 },
      feedback: { type: String, trim: true }
    }],
    
    // Statut
    status: {
      type: String,
      enum: ['draft', 'submitted', 'graded', 'returned', 'resubmitted'],
      default: 'draft'
    },
    
    // Métadonnées
    ipAddress: { type: String },
    userAgent: { type: String },
    plagiarismScore: { type: Number, min: 0, max: 100 },
    plagiarismReport: { type: String }
  }],
  
  // Statut de l'assignment
  status: {
    type: String,
    enum: ['draft', 'published', 'active', 'closed', 'graded', 'archived'],
    default: 'draft'
  },
  
  // Visibilité et accès
  visibility: {
    type: String,
    enum: ['all_students', 'specific_students', 'groups_only'],
    default: 'all_students'
  },
  specificStudents: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Student'
  }],
  
  // Notifications et rappels
  notifications: {
    sendReminders: { type: Boolean, default: true },
    reminderDays: [{ type: Number, min: 1 }], // e.g., [7, 3, 1] for 7, 3, and 1 day before due
    notifyOnSubmission: { type: Boolean, default: true },
    notifyOnGrading: { type: Boolean, default: true }
  },
  
  // Statistiques
  statistics: {
    totalSubmissions: { type: Number, default: 0 },
    onTimeSubmissions: { type: Number, default: 0 },
    lateSubmissions: { type: Number, default: 0 },
    averageGrade: { type: Number, min: 0, max: 100, default: 0 },
    submissionRate: { type: Number, min: 0, max: 100, default: 0 },
    averageSubmissionTime: { type: Number, default: 0 } // in hours before due date
  },
  
  // Peer review settings (si applicable)
  peerReview: {
    enabled: { type: Boolean, default: false },
    reviewsPerStudent: { type: Number, min: 1, default: 2 },
    reviewDeadline: { type: Date },
    anonymousReview: { type: Boolean, default: true },
    reviewCriteria: [{
      criterion: { type: String, required: true, trim: true },
      description: { type: String, trim: true },
      weight: { type: Number, min: 0, max: 100 }
    }]
  },
  
  // Métadonnées
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  publishedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  publishedDate: {
    type: Date
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Index pour améliorer les performances
assignmentSchema.index({ course: 1, dueDate: 1 });
assignmentSchema.index({ instructor: 1 });
assignmentSchema.index({ status: 1 });
assignmentSchema.index({ type: 1 });
assignmentSchema.index({ 'submissions.student': 1 });

// Virtual pour vérifier si l'assignment est en retard
assignmentSchema.virtual('isOverdue').get(function() {
  return new Date() > this.dueDate;
});

// Virtual pour calculer le temps restant
assignmentSchema.virtual('timeRemaining').get(function() {
  const now = new Date();
  const due = new Date(this.dueDate);
  return due > now ? due - now : 0;
});

// Virtual pour vérifier si l'assignment est disponible
assignmentSchema.virtual('isAvailable').get(function() {
  const now = new Date();
  return now >= this.availableFrom && (!this.availableUntil || now <= this.availableUntil);
});

// Middleware pour valider les dates
assignmentSchema.pre('save', function(next) {
  if (this.dueDate <= this.assignedDate) {
    return next(new Error('Due date must be after assigned date'));
  }
  
  if (this.availableUntil && this.availableUntil < this.availableFrom) {
    return next(new Error('Available until date must be after available from date'));
  }
  
  next();
});

// Middleware pour calculer les statistiques
assignmentSchema.pre('save', function(next) {
  if (this.submissions.length > 0) {
    const totalSubmissions = this.submissions.length;
    const onTimeSubmissions = this.submissions.filter(sub => !sub.isLate).length;
    const lateSubmissions = totalSubmissions - onTimeSubmissions;
    
    this.statistics.totalSubmissions = totalSubmissions;
    this.statistics.onTimeSubmissions = onTimeSubmissions;
    this.statistics.lateSubmissions = lateSubmissions;
    
    // Calculer le taux de soumission (nécessite le nombre total d'étudiants inscrits)
    // Cette logique devrait être implémentée dans une méthode séparée
  }
  
  next();
});

// Méthode pour publier l'assignment
assignmentSchema.methods.publish = function(publishedBy) {
  this.status = 'published';
  this.publishedBy = publishedBy;
  this.publishedDate = new Date();
  return this.save();
};

// Méthode pour soumettre un travail
assignmentSchema.methods.submitWork = function(studentId, submissionData) {
  const isLate = new Date() > this.dueDate;
  const daysLate = isLate ? Math.ceil((new Date() - this.dueDate) / (1000 * 60 * 60 * 24)) : 0;
  
  const submission = {
    student: studentId,
    submissionDate: new Date(),
    isLate: isLate,
    daysLate: daysLate,
    status: 'submitted',
    ...submissionData
  };
  
  // Vérifier si l'étudiant a déjà soumis
  const existingSubmissionIndex = this.submissions.findIndex(
    sub => sub.student.toString() === studentId.toString()
  );
  
  if (existingSubmissionIndex !== -1) {
    if (!this.submissionSettings.allowMultipleAttempts) {
      throw new Error('Multiple attempts not allowed');
    }
    submission.attemptNumber = this.submissions[existingSubmissionIndex].attemptNumber + 1;
    this.submissions[existingSubmissionIndex] = submission;
  } else {
    this.submissions.push(submission);
  }
  
  return this.save();
};

// Méthode pour noter une soumission
assignmentSchema.methods.gradeSubmission = function(studentId, gradeData) {
  const submission = this.submissions.find(
    sub => sub.student.toString() === studentId.toString()
  );
  
  if (!submission) {
    throw new Error('Submission not found');
  }
  
  submission.feedback = gradeData.feedback;
  submission.rubricScores = gradeData.rubricScores;
  submission.status = 'graded';
  
  return this.save();
};

// Méthode pour calculer les statistiques détaillées
assignmentSchema.methods.calculateStatistics = function() {
  return mongoose.model('Enrollment').countDocuments({
    course: this.course,
    status: 'enrolled'
  }).then(totalStudents => {
    const submissionRate = totalStudents > 0 ? 
      Math.round((this.statistics.totalSubmissions / totalStudents) * 100) : 0;
    
    this.statistics.submissionRate = submissionRate;
    return this.save();
  });
};

// Méthode pour obtenir les soumissions en retard
assignmentSchema.methods.getLateSubmissions = function() {
  return this.submissions.filter(submission => submission.isLate);
};

// Méthode pour envoyer des rappels
assignmentSchema.methods.sendReminders = function() {
  if (!this.notifications.sendReminders) return;
  
  const now = new Date();
  const dueDate = new Date(this.dueDate);
  const daysUntilDue = Math.ceil((dueDate - now) / (1000 * 60 * 60 * 24));
  
  if (this.notifications.reminderDays.includes(daysUntilDue)) {
    // Logique pour envoyer des notifications
    // Cette partie devrait être implémentée avec un service de notification
    console.log(`Sending reminder for assignment: ${this.title}, due in ${daysUntilDue} days`);
  }
};

module.exports = mongoose.model('Assignment', assignmentSchema);
