const mongoose = require('mongoose');

const attendanceSchema = new mongoose.Schema({
  // Références principales
  student: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Student',
    required: true
  },
  course: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Course'
  },
  module: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Module'
  },
  schedule: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Schedule',
    required: true
  },
  
  // Informations temporelles
  date: {
    type: Date,
    required: true
  },
  sessionStartTime: {
    type: Date,
    required: true
  },
  sessionEndTime: {
    type: Date,
    required: true
  },
  
  // Statut de présence
  status: {
    type: String,
    enum: ['present', 'absent', 'late', 'excused', 'partial', 'left_early'],
    required: true,
    default: 'absent'
  },
  
  // Heures d'arrivée et de départ
  arrivalTime: {
    type: Date
  },
  departureTime: {
    type: Date
  },
  
  // Calculs de temps
  minutesLate: {
    type: Number,
    min: 0,
    default: 0
  },
  minutesEarly: {
    type: Number,
    min: 0,
    default: 0
  },
  totalMinutesPresent: {
    type: Number,
    min: 0,
    default: 0
  },
  attendancePercentage: {
    type: Number,
    min: 0,
    max: 100,
    default: 0
  },
  
  // Méthode d'enregistrement
  recordingMethod: {
    type: String,
    enum: ['manual', 'qr_code', 'digital_signature', 'biometric', 'automatic', 'self_reported'],
    default: 'manual'
  },
  
  // Informations sur l'absence
  absenceReason: {
    type: String,
    enum: ['illness', 'family_emergency', 'work_conflict', 'transportation', 'personal', 'technical_issues', 'other'],
    required: function() { return ['absent', 'excused'].includes(this.status); }
  },
  absenceDetails: {
    type: String,
    trim: true,
    maxlength: 500
  },
  
  // Documentation de l'absence
  documentation: {
    hasDocumentation: { type: Boolean, default: false },
    documentType: {
      type: String,
      enum: ['medical_certificate', 'work_letter', 'official_document', 'other']
    },
    documentPath: { type: String, trim: true },
    verificationStatus: {
      type: String,
      enum: ['pending', 'verified', 'rejected', 'not_required'],
      default: 'not_required'
    },
    verifiedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    verificationDate: { type: Date }
  },
  
  // Approbation de l'excuse
  excuseApproval: {
    isApproved: { type: Boolean, default: false },
    approvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    approvalDate: { type: Date },
    approvalComments: { type: String, trim: true }
  },
  
  // Participation et engagement
  participation: {
    level: {
      type: String,
      enum: ['excellent', 'good', 'average', 'poor', 'none'],
      default: 'none'
    },
    score: { type: Number, min: 0, max: 10, default: 0 },
    comments: { type: String, trim: true },
    activities: [{
      activity: { type: String, trim: true },
      participation: {
        type: String,
        enum: ['active', 'passive', 'disruptive', 'absent']
      }
    }]
  },
  
  // Informations de localisation (pour vérification)
  location: {
    ipAddress: { type: String, trim: true },
    gpsCoordinates: {
      latitude: { type: Number },
      longitude: { type: Number }
    },
    deviceInfo: { type: String, trim: true },
    browserInfo: { type: String, trim: true }
  },
  
  // Notifications et rappels
  notifications: {
    reminderSent: { type: Boolean, default: false },
    reminderSentAt: { type: Date },
    absenceNotificationSent: { type: Boolean, default: false },
    absenceNotificationSentAt: { type: Date },
    followUpRequired: { type: Boolean, default: false }
  },
  
  // Historique des modifications
  modifications: [{
    previousStatus: {
      type: String,
      enum: ['present', 'absent', 'late', 'excused', 'partial', 'left_early']
    },
    newStatus: {
      type: String,
      enum: ['present', 'absent', 'late', 'excused', 'partial', 'left_early']
    },
    reason: { type: String, trim: true },
    modifiedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    modificationDate: { type: Date, default: Date.now },
    approvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  }],
  
  // Informations académiques
  academicImpact: {
    affectsGrade: { type: Boolean, default: true },
    penaltyApplied: { type: Boolean, default: false },
    penaltyAmount: { type: Number, min: 0, default: 0 },
    makeupRequired: { type: Boolean, default: false },
    makeupCompleted: { type: Boolean, default: false },
    makeupDate: { type: Date }
  },
  
  // Patterns et alertes
  patterns: {
    isRepeatedAbsence: { type: Boolean, default: false },
    consecutiveAbsences: { type: Number, min: 0, default: 0 },
    totalAbsencesThisSemester: { type: Number, min: 0, default: 0 },
    attendanceRate: { type: Number, min: 0, max: 100, default: 100 },
    riskLevel: {
      type: String,
      enum: ['low', 'medium', 'high', 'critical'],
      default: 'low'
    }
  },
  
  // Commentaires et notes
  notes: [{
    author: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    content: { type: String, required: true, trim: true },
    isPrivate: { type: Boolean, default: false },
    createdAt: { type: Date, default: Date.now }
  }],
  
  // Métadonnées d'enregistrement
  recordedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  recordedAt: {
    type: Date,
    default: Date.now
  },
  lastModifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Index pour améliorer les performances
attendanceSchema.index({ student: 1, date: 1 });
attendanceSchema.index({ schedule: 1 });
attendanceSchema.index({ course: 1, date: 1 });
attendanceSchema.index({ status: 1 });
attendanceSchema.index({ date: 1 });

// Index composé pour éviter les doublons
attendanceSchema.index({ student: 1, schedule: 1 }, { unique: true });

// Virtual pour vérifier si l'étudiant était en retard
attendanceSchema.virtual('isLate').get(function() {
  return this.status === 'late' || this.minutesLate > 0;
});

// Virtual pour vérifier si l'absence est justifiée
attendanceSchema.virtual('isExcused').get(function() {
  return this.status === 'excused' && this.excuseApproval.isApproved;
});

// Virtual pour calculer la durée de présence effective
attendanceSchema.virtual('effectivePresenceDuration').get(function() {
  if (!this.arrivalTime || !this.departureTime) return 0;
  return Math.max(0, (this.departureTime - this.arrivalTime) / (1000 * 60)); // en minutes
});

// Middleware pour calculer automatiquement les valeurs dérivées
attendanceSchema.pre('save', function(next) {
  // Calculer les minutes de retard
  if (this.arrivalTime && this.sessionStartTime) {
    const lateness = (this.arrivalTime - this.sessionStartTime) / (1000 * 60);
    this.minutesLate = Math.max(0, lateness);
    
    if (this.minutesLate > 0 && this.status === 'present') {
      this.status = 'late';
    }
  }
  
  // Calculer les minutes de départ anticipé
  if (this.departureTime && this.sessionEndTime) {
    const earlyLeaving = (this.sessionEndTime - this.departureTime) / (1000 * 60);
    this.minutesEarly = Math.max(0, earlyLeaving);
    
    if (this.minutesEarly > 15 && ['present', 'late'].includes(this.status)) {
      this.status = 'left_early';
    }
  }
  
  // Calculer le temps total de présence
  if (this.arrivalTime && this.departureTime) {
    this.totalMinutesPresent = Math.max(0, (this.departureTime - this.arrivalTime) / (1000 * 60));
  }
  
  // Calculer le pourcentage de présence pour cette session
  if (this.sessionStartTime && this.sessionEndTime) {
    const sessionDuration = (this.sessionEndTime - this.sessionStartTime) / (1000 * 60);
    this.attendancePercentage = sessionDuration > 0 ? 
      Math.min(100, Math.round((this.totalMinutesPresent / sessionDuration) * 100)) : 0;
  }
  
  next();
});

// Middleware pour enregistrer les modifications
attendanceSchema.pre('save', function(next) {
  if (this.isModified('status') && !this.isNew) {
    this.modifications.push({
      previousStatus: this.status,
      newStatus: this.status,
      modifiedBy: this.lastModifiedBy,
      modificationDate: new Date()
    });
  }
  next();
});

// Méthode pour marquer la présence
attendanceSchema.methods.markPresent = function(arrivalTime, recordedBy) {
  this.status = 'present';
  this.arrivalTime = arrivalTime || new Date();
  this.recordedBy = recordedBy;
  this.recordedAt = new Date();
  return this.save();
};

// Méthode pour marquer l'absence
attendanceSchema.methods.markAbsent = function(reason, details, recordedBy) {
  this.status = 'absent';
  this.absenceReason = reason;
  this.absenceDetails = details;
  this.recordedBy = recordedBy;
  this.recordedAt = new Date();
  return this.save();
};

// Méthode pour excuser une absence
attendanceSchema.methods.excuseAbsence = function(approvedBy, comments) {
  this.status = 'excused';
  this.excuseApproval = {
    isApproved: true,
    approvedBy: approvedBy,
    approvalDate: new Date(),
    approvalComments: comments
  };
  return this.save();
};

// Méthode pour calculer les statistiques de présence d'un étudiant
attendanceSchema.statics.calculateStudentAttendanceStats = function(studentId, courseId, startDate, endDate) {
  const matchConditions = { student: studentId };
  
  if (courseId) matchConditions.course = courseId;
  if (startDate || endDate) {
    matchConditions.date = {};
    if (startDate) matchConditions.date.$gte = startDate;
    if (endDate) matchConditions.date.$lte = endDate;
  }
  
  return this.aggregate([
    { $match: matchConditions },
    {
      $group: {
        _id: null,
        totalSessions: { $sum: 1 },
        presentSessions: {
          $sum: {
            $cond: [{ $in: ['$status', ['present', 'late']] }, 1, 0]
          }
        },
        absentSessions: {
          $sum: {
            $cond: [{ $eq: ['$status', 'absent'] }, 1, 0]
          }
        },
        excusedSessions: {
          $sum: {
            $cond: [{ $eq: ['$status', 'excused'] }, 1, 0]
          }
        },
        lateSessions: {
          $sum: {
            $cond: [{ $eq: ['$status', 'late'] }, 1, 0]
          }
        },
        totalMinutesLate: { $sum: '$minutesLate' },
        averageAttendancePercentage: { $avg: '$attendancePercentage' }
      }
    },
    {
      $addFields: {
        attendanceRate: {
          $multiply: [
            { $divide: ['$presentSessions', '$totalSessions'] },
            100
          ]
        }
      }
    }
  ]);
};

// Méthode pour détecter les patterns d'absence
attendanceSchema.methods.detectAbsencePatterns = function() {
  return this.constructor.find({
    student: this.student,
    course: this.course,
    date: { $lte: this.date }
  })
  .sort({ date: -1 })
  .limit(10)
  .then(recentAttendance => {
    let consecutiveAbsences = 0;
    let totalAbsences = 0;
    
    for (const record of recentAttendance) {
      if (record.status === 'absent') {
        if (record.date.toDateString() === this.date.toDateString() || consecutiveAbsences > 0) {
          consecutiveAbsences++;
        }
        totalAbsences++;
      } else if (consecutiveAbsences > 0) {
        break;
      }
    }
    
    this.patterns.consecutiveAbsences = consecutiveAbsences;
    this.patterns.totalAbsencesThisSemester = totalAbsences;
    this.patterns.isRepeatedAbsence = consecutiveAbsences >= 2;
    
    // Déterminer le niveau de risque
    if (consecutiveAbsences >= 4) {
      this.patterns.riskLevel = 'critical';
    } else if (consecutiveAbsences >= 3 || totalAbsences >= 5) {
      this.patterns.riskLevel = 'high';
    } else if (consecutiveAbsences >= 2 || totalAbsences >= 3) {
      this.patterns.riskLevel = 'medium';
    } else {
      this.patterns.riskLevel = 'low';
    }
    
    return this.save();
  });
};

// Méthode pour générer un rapport de présence
attendanceSchema.statics.generateAttendanceReport = function(filters) {
  const matchConditions = {};
  
  if (filters.studentId) matchConditions.student = filters.studentId;
  if (filters.courseId) matchConditions.course = filters.courseId;
  if (filters.startDate || filters.endDate) {
    matchConditions.date = {};
    if (filters.startDate) matchConditions.date.$gte = filters.startDate;
    if (filters.endDate) matchConditions.date.$lte = filters.endDate;
  }
  
  return this.aggregate([
    { $match: matchConditions },
    {
      $group: {
        _id: {
          student: '$student',
          course: '$course'
        },
        totalSessions: { $sum: 1 },
        presentCount: {
          $sum: { $cond: [{ $in: ['$status', ['present', 'late']] }, 1, 0] }
        },
        absentCount: {
          $sum: { $cond: [{ $eq: ['$status', 'absent'] }, 1, 0] }
        },
        excusedCount: {
          $sum: { $cond: [{ $eq: ['$status', 'excused'] }, 1, 0] }
        },
        averageParticipation: { $avg: '$participation.score' }
      }
    },
    {
      $addFields: {
        attendanceRate: {
          $multiply: [{ $divide: ['$presentCount', '$totalSessions'] }, 100]
        }
      }
    },
    {
      $lookup: {
        from: 'students',
        localField: '_id.student',
        foreignField: '_id',
        as: 'studentInfo'
      }
    },
    {
      $lookup: {
        from: 'courses',
        localField: '_id.course',
        foreignField: '_id',
        as: 'courseInfo'
      }
    }
  ]);
};

module.exports = mongoose.model('Attendance', attendanceSchema);
