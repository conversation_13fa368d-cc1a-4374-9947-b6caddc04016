const mongoose = require('mongoose');

const courseSchema = new mongoose.Schema({
  // Informations de base du cours
  courseCode: {
    type: String,
    required: true,
    trim: true,
    uppercase: true,
    match: [/^[A-Z]{2,4}\d{3,4}$/, 'Format: ABC123 ou ABCD1234']
  },
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  description: {
    type: String,
    required: true,
    trim: true,
    maxlength: 2000
  },
  shortDescription: {
    type: String,
    trim: true,
    maxlength: 500
  },
  
  // Détails académiques
  creditHours: {
    type: Number,
    required: true,
    min: 1,
    max: 10
  },
  contactHours: {
    type: Number,
    required: true,
    min: 1
  },
  level: {
    type: String,
    enum: ['Facile', 'Intermidière', 'Difficile'],
    required: true
  },
  
  // Prérequis et corequisites
  prerequisites: [{
    course: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Course'
    },
    minimumGrade: {
      type: Number,
      min: 0,
      max: 20,
      default: 10
    }
  }],
  corequisites: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Course'
  }],
  
  // Note: Les instructeurs sont maintenant gérés dans CourseAssignment
  // instructor: Supprimé - géré par CourseAssignment
  coInstructors: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Professor'
  }],
  teachingAssistants: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],

  // Classe spécifique pour ce cours
  class: {
    type: String,
    required: true,
    index: true,
    match: [/^EMBA[12][A-Z]$/, 'Format: EMBA1A, EMBA1B, etc.']
  },
  promotion: {
    type: String,
    enum: ['EMBA1', 'EMBA2'],
    required: true,
    index: true
  },
  
  // Planification
  academicYear: {
    type: String,
    required: true,
    match: [/^\d{4}-\d{4}$/, 'Format: YYYY-YYYY']
  },
  semester: {
    type: String,
    enum: ['Semestre 1', 'Semestre 2'],
    required: true
  },
  startDate: {
    type: Date,
    required: true
  },
  endDate: {
    type: Date,
    required: true
  },
  
  // Note: Le planning est maintenant géré dans CourseAssignment avec génération automatique
  // schedule: Supprimé - géré par CourseAssignment
  
  // Capacité et inscriptions
  capacity: {
    maximum: { type: Number, required: true, min: 1 },
    minimum: { type: Number, required: true, min: 1 },
    current: { type: Number, default: 0, min: 0 },
    waitingList: { type: Number, default: 0, min: 0 }
  },
  
  // Statut du cours
  status: {
    type: String,
    enum: ['draft', 'active', 'full', 'cancelled', 'completed', 'archived'],
    default: 'draft'
  },
  registrationStatus: {
    type: String,
    enum: ['open', 'closed', 'waitlist_only'],
    default: 'closed'
  },
  
  // Objectifs d'apprentissage
  learningObjectives: [{
    objective: { type: String, required: true, trim: true },
    bloomLevel: {
      type: String,
      enum: ['Remember', 'Understand', 'Apply', 'Analyze', 'Evaluate', 'Create']
    }
  }],
  
  // Contenu du cours
  syllabus: {
    overview: { type: String, trim: true },
    topics: [{
      week: { type: Number, min: 1 },
      title: { type: String, required: true, trim: true },
      description: { type: String, trim: true },
      learningOutcomes: [{ type: String, trim: true }],
      readings: [{
        type: { type: String, enum: ['Required', 'Recommended', 'Supplementary'] },
        title: { type: String, required: true, trim: true },
        author: { type: String, trim: true },
        source: { type: String, trim: true },
        pages: { type: String, trim: true },
        url: { type: String, trim: true }
      }],
      activities: [{ type: String, trim: true }]
    }],
    textbooks: [{
      title: { type: String, required: true, trim: true },
      author: { type: String, required: true, trim: true },
      edition: { type: String, trim: true },
      publisher: { type: String, trim: true },
      isbn: { type: String, trim: true },
      isRequired: { type: Boolean, default: true },
      price: { type: Number, min: 0 }
    }]
  },
  
  // Évaluation et notation
  gradingScheme: {
    components: [{
      name: {
        type: String,
        required: true,
        enum: ['Participation', 'Assignments', 'Quizzes', 'Midterm Exam', 'Final Exam', 'Project', 'Case Study', 'Presentation', 'Other']
      },
      weight: { type: Number, required: true, min: 0, max: 100 },
      description: { type: String, trim: true }
    }],
    gradingScale: [{
      grade: { type: String, required: true },
      minPercentage: { type: Number, required: true, min: 0, max: 100 },
      maxPercentage: { type: Number, required: true, min: 0, max: 100 },
      gpaPoints: { type: Number, required: true, min: 0, max: 4 }
    }],
    passingGrade: { type: Number, default: 60, min: 0, max: 100 }
  },
  
  // Politiques du cours
  policies: {
    attendancePolicy: { type: String, trim: true },
    lateSubmissionPolicy: { type: String, trim: true },
    makeupExamPolicy: { type: String, trim: true },
    academicIntegrityPolicy: { type: String, trim: true },
    accommodationsPolicy: { type: String, trim: true }
  },
  
  // Ressources et matériaux
  resources: {
    onlineResources: [{
      title: { type: String, required: true, trim: true },
      url: { type: String, required: true, trim: true },
      description: { type: String, trim: true },
      type: {
        type: String,
        enum: ['Video', 'Article', 'Website', 'Database', 'Software', 'Other']
      }
    }],
    softwareRequired: [{
      name: { type: String, required: true, trim: true },
      version: { type: String, trim: true },
      license: { type: String, trim: true },
      downloadUrl: { type: String, trim: true }
    }],
    equipmentNeeded: [{ type: String, trim: true }]
  },
  
  // Évaluations du cours
  evaluations: {
    studentFeedback: [{
      student: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Student'
      },
      ratings: {
        courseContent: { type: Number, min: 1, max: 5 },
        instructorEffectiveness: { type: Number, min: 1, max: 5 },
        courseDifficulty: { type: Number, min: 1, max: 5 },
        workload: { type: Number, min: 1, max: 5 },
        overallSatisfaction: { type: Number, min: 1, max: 5 }
      },
      comments: { type: String, trim: true },
      suggestions: { type: String, trim: true },
      submittedAt: { type: Date, default: Date.now }
    }],
    averageRatings: {
      courseContent: { type: Number, min: 0, max: 5, default: 0 },
      instructorEffectiveness: { type: Number, min: 0, max: 5, default: 0 },
      courseDifficulty: { type: Number, min: 0, max: 5, default: 0 },
      workload: { type: Number, min: 0, max: 5, default: 0 },
      overallSatisfaction: { type: Number, min: 0, max: 5, default: 0 }
    }
  },
  
  // Statistiques du cours
  statistics: {
    enrollmentHistory: [{
      date: { type: Date, default: Date.now },
      enrolled: { type: Number, default: 0 },
      dropped: { type: Number, default: 0 },
      completed: { type: Number, default: 0 }
    }],
    gradeDistribution: [{
      grade: { type: String, required: true },
      count: { type: Number, default: 0 },
      percentage: { type: Number, default: 0 }
    }],
    averageGrade: { type: Number, min: 0, max: 20, default: 0 },
    passRate: { type: Number, min: 0, max: 100, default: 0 },
    completionRate: { type: Number, min: 0, max: 100, default: 0 }
  },
  
  // Métadonnées
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  lastModified: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Index pour améliorer les performances
courseSchema.index({ courseCode: 1, class: 1 }, { unique: true }); // Unicité par courseCode + class
courseSchema.index({ instructor: 1 });
courseSchema.index({ academicYear: 1, semester: 1 });
courseSchema.index({ status: 1 });
courseSchema.index({ category: 1 });
courseSchema.index({ level: 1 });

// Virtual pour calculer la durée du cours en semaines
courseSchema.virtual('durationWeeks').get(function() {
  const diffTime = Math.abs(this.endDate - this.startDate);
  const diffWeeks = Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 7));
  return diffWeeks;
});

// Virtual pour vérifier si le cours est complet
courseSchema.virtual('isFull').get(function() {
  return this.capacity.current >= this.capacity.maximum;
});

// Virtual pour calculer le taux d'occupation
courseSchema.virtual('occupancyRate').get(function() {
  return Math.round((this.capacity.current / this.capacity.maximum) * 100);
});

// Virtual pour vérifier si les inscriptions sont ouvertes
courseSchema.virtual('canEnroll').get(function() {
  return this.registrationStatus === 'open' && !this.isFull;
});

// Middleware pour valider les dates
courseSchema.pre('save', function(next) {
  if (this.startDate >= this.endDate) {
    return next(new Error('End date must be after start date'));
  }
  
  // Valider que la somme des poids des composants d'évaluation = 100%
  if (this.gradingScheme.components.length > 0) {
    const totalWeight = this.gradingScheme.components.reduce((sum, comp) => sum + comp.weight, 0);
    if (totalWeight !== 100) {
      return next(new Error('Total weight of grading components must equal 100%'));
    }
  }
  
  next();
});

// Middleware pour mettre à jour lastModified
courseSchema.pre('save', function(next) {
  this.lastModified = new Date();
  next();
});

// Méthode pour calculer les notes moyennes des évaluations
courseSchema.methods.calculateAverageRatings = function() {
  if (this.evaluations.studentFeedback.length === 0) return;
  
  const ratings = this.evaluations.studentFeedback;
  const avgRatings = {
    courseContent: 0,
    instructorEffectiveness: 0,
    courseDifficulty: 0,
    workload: 0,
    overallSatisfaction: 0
  };
  
  Object.keys(avgRatings).forEach(key => {
    const sum = ratings.reduce((total, feedback) => total + (feedback.ratings[key] || 0), 0);
    avgRatings[key] = Math.round((sum / ratings.length) * 10) / 10;
  });
  
  this.evaluations.averageRatings = avgRatings;
  return this.save();
};

// Méthode pour vérifier si un étudiant peut s'inscrire
courseSchema.methods.canStudentEnroll = function(studentId) {
  return mongoose.model('Enrollment').findOne({
    student: studentId,
    course: this._id,
    status: { $in: ['enrolled', 'completed'] }
  }).then(enrollment => !enrollment);
};

// Méthode pour obtenir les étudiants inscrits
courseSchema.methods.getEnrolledStudents = function() {
  return mongoose.model('Enrollment').find({
    course: this._id,
    status: 'enrolled'
  }).populate('student');
};

// Méthode pour mettre à jour les statistiques
courseSchema.methods.updateStatistics = function() {
  return mongoose.model('Enrollment').aggregate([
    { $match: { course: this._id } },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 }
      }
    }
  ]).then(results => {
    const stats = results.reduce((acc, result) => {
      acc[result._id] = result.count;
      return acc;
    }, {});
    
    this.capacity.current = stats.enrolled || 0;
    this.statistics.completionRate = stats.enrolled > 0 ? 
      Math.round(((stats.completed || 0) / stats.enrolled) * 100) : 0;
    
    return this.save();
  });
};

// Méthode statique pour détecter les conflits horaires
courseSchema.statics.checkScheduleConflicts = async function(courseData) {
  const conflicts = [];

  for (let scheduleItem of courseData.schedule) {
    // Vérifier les conflits de professeur
    const instructorConflicts = await this.find({
      instructor: courseData.instructor,
      _id: { $ne: courseData._id }, // Exclure le cours actuel si modification
      'schedule.dayOfWeek': scheduleItem.dayOfWeek,
      $or: [
        {
          'schedule.startTime': { $lt: scheduleItem.endTime },
          'schedule.endTime': { $gt: scheduleItem.startTime }
        }
      ]
    }).populate('instructor', 'user');

    if (instructorConflicts.length > 0) {
      conflicts.push({
        type: 'instructor',
        message: `Le professeur a déjà un cours le ${scheduleItem.dayOfWeek} de ${scheduleItem.startTime} à ${scheduleItem.endTime}`,
        conflictingCourses: instructorConflicts.map(c => ({ id: c._id, title: c.title }))
      });
    }

    // Vérifier les conflits de salle
    if (scheduleItem.location && scheduleItem.location.room) {
      const roomConflicts = await this.find({
        _id: { $ne: courseData._id },
        'schedule.dayOfWeek': scheduleItem.dayOfWeek,
        'schedule.location.building': scheduleItem.location.building,
        'schedule.location.room': scheduleItem.location.room,
        $or: [
          {
            'schedule.startTime': { $lt: scheduleItem.endTime },
            'schedule.endTime': { $gt: scheduleItem.startTime }
          }
        ]
      });

      if (roomConflicts.length > 0) {
        conflicts.push({
          type: 'room',
          message: `La salle ${scheduleItem.location.building}-${scheduleItem.location.room} est déjà occupée le ${scheduleItem.dayOfWeek} de ${scheduleItem.startTime} à ${scheduleItem.endTime}`,
          conflictingCourses: roomConflicts.map(c => ({ id: c._id, title: c.title }))
        });
      }
    }

    // Vérifier les conflits de classe (étudiants)
    const classConflicts = await this.find({
      class: courseData.class,
      _id: { $ne: courseData._id },
      'schedule.dayOfWeek': scheduleItem.dayOfWeek,
      $or: [
        {
          'schedule.startTime': { $lt: scheduleItem.endTime },
          'schedule.endTime': { $gt: scheduleItem.startTime }
        }
      ]
    });

    if (classConflicts.length > 0) {
      conflicts.push({
        type: 'class',
        message: `La classe ${courseData.class} a déjà un cours le ${scheduleItem.dayOfWeek} de ${scheduleItem.startTime} à ${scheduleItem.endTime}`,
        conflictingCourses: classConflicts.map(c => ({ id: c._id, title: c.title }))
      });
    }
  }

  return conflicts;
};

// Méthode statique pour obtenir l'emploi du temps d'une classe
courseSchema.statics.getClassSchedule = function(className) {
  return this.find({ class: className })
    .populate('instructor', 'user academicTitle')
    .populate('module', 'name code')
    .sort({ 'schedule.dayOfWeek': 1, 'schedule.startTime': 1 });
};

// Méthode statique pour obtenir l'emploi du temps d'un professeur
courseSchema.statics.getProfessorSchedule = function(professorId) {
  return this.find({ instructor: professorId })
    .populate('module', 'name code')
    .sort({ 'schedule.dayOfWeek': 1, 'schedule.startTime': 1 });
};

// Méthode statique pour créer automatiquement des cours pour toutes les classes d'une promotion
courseSchema.statics.createCourseForAllClasses = async function(courseData) {
  const Student = mongoose.model('Student');
  const Notification = mongoose.model('Notification');

  const { promotion, ...baseCourseData } = courseData;

  // Obtenir toutes les classes existantes pour cette promotion
  const classStats = await Student.getClassStats(promotion);
  const createdCourses = [];

  // Si aucune classe n'existe encore, créer au moins une classe A
  if (classStats.length === 0) {
    const defaultClass = `${promotion}A`;
    const course = new this({
      ...baseCourseData,
      courseCode: baseCourseData.courseCode, // Garder le code original
      title: `${baseCourseData.title} - ${defaultClass}`,
      class: defaultClass,
      promotion: promotion,
      capacity: {
        maximum: baseCourseData.capacity?.maximum || 30,
        minimum: baseCourseData.capacity?.minimum || 10,
        current: 0,
        waitingList: 0
      }
    });

    await course.save();
    createdCourses.push(course);

    // Note: Création de notification désactivée temporairement
    // car le modèle Notification nécessite un recipient spécifique (ObjectId User)
    // et non un système de recipients multiples par rôles/classes
    console.log(`📧 Notification à créer: Nouveau cours "${course.title}" pour ${defaultClass}`);
  } else {
    // Créer un cours pour chaque classe existante
    for (let classData of classStats) {
      const className = classData._id.class;

      // Vérifier si un cours avec le même code existe déjà pour cette classe
      const existingCourse = await this.findOne({
        courseCode: baseCourseData.courseCode,
        class: className
      });

      if (!existingCourse) {
        const course = new this({
          ...baseCourseData,
          courseCode: baseCourseData.courseCode, // Garder le code original
          title: `${baseCourseData.title} - ${className}`,
          class: className,
          promotion: promotion,
          capacity: {
            maximum: baseCourseData.capacity?.maximum || 30,
            minimum: baseCourseData.capacity?.minimum || 10,
            current: classData.count,
            waitingList: 0
          }
        });

        await course.save();
        createdCourses.push(course);

        // Note: Création de notification désactivée temporairement
        // car le modèle Notification nécessite un recipient spécifique (ObjectId User)
        console.log(`📧 Notification à créer: Nouveau cours "${course.title}" pour ${className}`);

        // Inscrire automatiquement tous les étudiants de cette classe
        await this.autoEnrollStudents(course._id, className);
      }
    }
  }

  return createdCourses;
};

// Méthode statique pour inscrire automatiquement les étudiants d'une classe à un cours
courseSchema.statics.autoEnrollStudents = async function(courseId, className) {
  const Student = mongoose.model('Student');
  const Enrollment = mongoose.model('Enrollment');

  try {
    // Récupérer tous les étudiants actifs de cette classe
    const students = await Student.find({
      class: className,
      enrollmentStatus: 'active'
    });

    const enrollments = [];
    const currentYear = new Date().getFullYear();

    for (let student of students) {
      // Vérifier si l'étudiant n'est pas déjà inscrit à ce cours
      const existingEnrollment = await Enrollment.findOne({
        student: student._id,
        course: courseId
      });

      if (!existingEnrollment) {
        const enrollment = new Enrollment({
          student: student._id,
          course: courseId,
          academicYear: `${currentYear}-${currentYear + 1}`,
          semester: 'Semestre 1',
          enrollmentDate: new Date(),
          startDate: new Date(),
          expectedEndDate: new Date(Date.now() + 120 * 24 * 60 * 60 * 1000), // 4 mois
          status: 'enrolled',
          tuitionFee: {
            amount: 0, // Inclus dans les frais généraux
            currency: 'TND',
            dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
            paidAmount: 0,
            remainingAmount: 0
          }
        });

        await enrollment.save();
        enrollments.push(enrollment);
      }
    }

    console.log(`✅ ${enrollments.length} étudiants inscrits automatiquement au cours pour la classe ${className}`);
    return enrollments;
  } catch (error) {
    console.error('❌ Erreur lors de l\'inscription automatique:', error);
    throw error;
  }
};

module.exports = mongoose.model('Course', courseSchema);
