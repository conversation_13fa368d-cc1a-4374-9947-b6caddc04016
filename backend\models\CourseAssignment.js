const mongoose = require('mongoose');

const courseAssignmentSchema = new mongoose.Schema({
  // Référence au cours
  course: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Course',
    required: true
  },
  
  // Professeurs assignés (plusieurs possibles)
  instructors: [{
    professor: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Professor',
      required: true
    },
    role: {
      type: String,
      enum: ['primary', 'secondary', 'guest'],
      default: 'primary'
    },
    hoursAssigned: {
      type: Number,
      min: 0,
      default: 0
    }
  }],
  
  // Classes assignées (plusieurs possibles)
  classes: [{
    className: {
      type: String,
      required: true,
      match: [/^EMBA[12][A-Z]$/, 'Format: EMBA1A, EMBA1B, etc.']
    },
    promotion: {
      type: String,
      enum: ['EMBA1', 'EMBA2'],
      required: true
    },
    maxStudents: {
      type: Number,
      default: 30
    }
  }],
  
  // Séances individuelles (remplace le planning automatique)
  sessions: [{
    // Date et heure spécifiques de la séance
    date: {
      type: Date,
      required: true
    },
    startTime: {
      type: String,
      required: true,
      match: [/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Format: HH:MM']
    },
    endTime: {
      type: String,
      required: true,
      match: [/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Format: HH:MM']
    },

    // Lieu de la séance
    location: {
      building: {
        type: String,
        default: 'Bâtiment A'
      },
      room: {
        type: String,
        default: 'Salle 101'
      },
      capacity: {
        type: Number,
        default: 30
      }
    },

    // Statut de la séance
    status: {
      type: String,
      enum: ['scheduled', 'completed', 'cancelled', 'rescheduled'],
      default: 'scheduled'
    },

    // Notes spécifiques à cette séance
    notes: {
      type: String,
      trim: true
    },

    // Professeur(s) pour cette séance spécifique (peut différer de l'affectation principale)
    sessionInstructors: [{
      professor: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Professor'
      },
      role: {
        type: String,
        enum: ['primary', 'secondary', 'guest'],
        default: 'primary'
      }
    }]
  }],
  
  // Statut de l'affectation
  status: {
    type: String,
    enum: ['draft', 'active', 'completed', 'cancelled'],
    default: 'draft'
  },
  
  // Métadonnées
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  notes: {
    type: String,
    trim: true
  }
}, {
  timestamps: true
});

// Index pour améliorer les performances
courseAssignmentSchema.index({ course: 1 });
courseAssignmentSchema.index({ 'instructors.professor': 1 });
courseAssignmentSchema.index({ 'classes.className': 1 });
courseAssignmentSchema.index({ 'sessions.date': 1 });

// Créneaux horaires disponibles du système EMBA
const TIME_SLOTS = {
  morning1: { start: '09:00', end: '10:30' },
  morning2: { start: '10:45', end: '12:15' },
  afternoon1: { start: '13:30', end: '15:00' },
  afternoon2: { start: '15:15', end: '16:45' }
};

// Méthode pour ajouter une nouvelle séance
courseAssignmentSchema.methods.addSession = function(sessionData) {
  const { date, startTime, endTime, location, notes, sessionInstructors } = sessionData;

  // Validation des données
  if (!date || !startTime || !endTime) {
    throw new Error('Date, heure de début et heure de fin sont requises');
  }

  // Vérifier que l'heure de fin est après l'heure de début
  if (startTime >= endTime) {
    throw new Error('L\'heure de fin doit être après l\'heure de début');
  }

  const newSession = {
    date: new Date(date),
    startTime,
    endTime,
    location: location || {
      building: 'Bâtiment A',
      room: 'Salle 101',
      capacity: 30
    },
    status: 'scheduled',
    notes: notes || '',
    sessionInstructors: sessionInstructors || []
  };

  this.sessions.push(newSession);
  return this;
};

// Méthode pour supprimer une séance
courseAssignmentSchema.methods.removeSession = function(sessionId) {
  this.sessions = this.sessions.filter(session => session._id.toString() !== sessionId.toString());
  return this;
};

// Méthode pour mettre à jour une séance
courseAssignmentSchema.methods.updateSession = function(sessionId, updateData) {
  const sessionIndex = this.sessions.findIndex(session => session._id.toString() === sessionId.toString());

  if (sessionIndex === -1) {
    throw new Error('Séance non trouvée');
  }

  // Validation des heures si elles sont modifiées
  if (updateData.startTime && updateData.endTime && updateData.startTime >= updateData.endTime) {
    throw new Error('L\'heure de fin doit être après l\'heure de début');
  }

  Object.assign(this.sessions[sessionIndex], updateData);
  return this;
};

// Méthode pour obtenir les statistiques des séances
courseAssignmentSchema.methods.getSessionsStats = function() {
  const total = this.sessions.length;
  const scheduled = this.sessions.filter(s => s.status === 'scheduled').length;
  const completed = this.sessions.filter(s => s.status === 'completed').length;
  const cancelled = this.sessions.filter(s => s.status === 'cancelled').length;

  return {
    total,
    scheduled,
    completed,
    cancelled,
    completionRate: total > 0 ? Math.round((completed / total) * 100) : 0
  };
};

// Méthode pour obtenir la première et dernière séance
courseAssignmentSchema.methods.getDateRange = function() {
  if (this.sessions.length === 0) {
    return { startDate: null, endDate: null };
  }

  const dates = this.sessions.map(session => new Date(session.date)).sort((a, b) => a - b);
  return {
    startDate: dates[0],
    endDate: dates[dates.length - 1]
  };
};

// Méthode statique pour obtenir les dates des semestres 2025-2026
courseAssignmentSchema.statics.getSemesterDates = function(semester) {
  const dates = {
    'Fall': {
      start: new Date('2025-09-01'),
      end: new Date('2025-12-20'),
      examPeriod: { start: new Date('2026-01-05'), end: new Date('2026-01-20') }
    },
    'Spring': {
      start: new Date('2026-01-20'),
      end: new Date('2026-05-30'),
      examPeriod: { start: new Date('2026-06-01'), end: new Date('2026-06-15') }
    }
  };
  
  return dates[semester] || null;
};

// Méthode pour détecter les conflits pour une séance spécifique
courseAssignmentSchema.methods.detectSessionConflicts = async function(sessionData) {
  const conflicts = [];
  const { date, startTime, endTime } = sessionData;

  // Convertir la date en début et fin de journée pour la recherche
  const sessionDate = new Date(date);
  const dayStart = new Date(sessionDate);
  dayStart.setHours(0, 0, 0, 0);
  const dayEnd = new Date(sessionDate);
  dayEnd.setHours(23, 59, 59, 999);

  // Vérifier les conflits pour chaque professeur
  for (const instructor of this.instructors) {
    const professorConflicts = await this.constructor.find({
      'instructors.professor': instructor.professor,
      'sessions': {
        $elemMatch: {
          date: { $gte: dayStart, $lte: dayEnd },
          $or: [
            {
              startTime: { $lt: endTime },
              endTime: { $gt: startTime }
            }
          ]
        }
      },
      _id: { $ne: this._id },
      status: { $in: ['active', 'draft'] }
    }).populate('course', 'courseCode title');

      professorConflicts.forEach(conflict => {
        conflicts.push({
          type: 'professor',
          severity: 'warning', // Les conflits professeur sont des avertissements
          professorId: instructor.professor,
          conflictWith: conflict.course.courseCode,
          date: sessionDate,
          startTime,
          endTime,
          message: `Le professeur a déjà un cours (${conflict.course.courseCode}) le ${sessionDate.toLocaleDateString('fr-FR')} de ${startTime} à ${endTime}`
        });
      });
    }

    // Vérifier les conflits pour chaque classe
    for (const classInfo of this.classes) {
      const classConflicts = await this.constructor.find({
        'classes.className': classInfo.className,
        'sessions': {
          $elemMatch: {
            date: { $gte: dayStart, $lte: dayEnd },
            $or: [
              {
                startTime: { $lt: endTime },
                endTime: { $gt: startTime }
              }
            ]
          }
        },
        _id: { $ne: this._id },
        status: { $in: ['active', 'draft'] }
      }).populate('course', 'courseCode title');

      classConflicts.forEach(conflict => {
        conflicts.push({
          type: 'class',
          severity: 'critical', // Les conflits de classe sont critiques
          className: classInfo.className,
          conflictWith: conflict.course.courseCode,
          date: sessionDate,
          startTime,
          endTime,
          message: `La classe ${classInfo.className} a déjà un cours (${conflict.course.courseCode}) le ${sessionDate.toLocaleDateString('fr-FR')} de ${startTime} à ${endTime}`
        });
      });
    }

  return conflicts;
};

// Méthode pour valider une séance avant ajout
courseAssignmentSchema.methods.validateSession = function(sessionData) {
  const errors = [];

  if (!sessionData.date) {
    errors.push('Date de la séance requise');
  }

  if (!sessionData.startTime) {
    errors.push('Heure de début requise');
  }

  if (!sessionData.endTime) {
    errors.push('Heure de fin requise');
  }

  if (sessionData.startTime && sessionData.endTime && sessionData.startTime >= sessionData.endTime) {
    errors.push('L\'heure de fin doit être après l\'heure de début');
  }

  // Vérifier que la date n'est pas dans le passé
  const sessionDate = new Date(sessionData.date);
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  if (sessionDate < today) {
    errors.push('La date de la séance ne peut pas être dans le passé');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

module.exports = mongoose.model('CourseAssignment', courseAssignmentSchema);
