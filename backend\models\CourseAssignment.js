const mongoose = require('mongoose');

const courseAssignmentSchema = new mongoose.Schema({
  // Référence au cours
  course: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Course',
    required: true
  },
  
  // Professeurs assignés (plusieurs possibles)
  instructors: [{
    professor: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Professor',
      required: true
    },
    role: {
      type: String,
      enum: ['primary', 'secondary', 'guest'],
      default: 'primary'
    },
    hoursAssigned: {
      type: Number,
      min: 0,
      default: 0
    }
  }],
  
  // Classes assignées (plusieurs possibles)
  classes: [{
    className: {
      type: String,
      required: true,
      match: [/^EMBA[12][A-Z]$/, 'Format: EMBA1A, EMBA1B, etc.']
    },
    promotion: {
      type: String,
      enum: ['EMBA1', 'EMBA2'],
      required: true
    },
    maxStudents: {
      type: Number,
      default: 30
    }
  }],
  
  // Planning automatique
  schedule: {
    // Date de début du cours
    startDate: {
      type: Date,
      required: true
    },
    
    // Durée en semaines (15 par défaut)
    durationWeeks: {
      type: Number,
      default: 15,
      min: 1,
      max: 20
    },
    
    // Heures par semaine (3h par défaut)
    hoursPerWeek: {
      type: Number,
      default: 3,
      min: 1,
      max: 6
    },
    
    // Créneaux générés automatiquement
    timeSlots: [{
      dayOfWeek: {
        type: String,
        enum: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
        required: true
      },
      startTime: {
        type: String,
        required: true,
        match: [/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Format: HH:MM']
      },
      endTime: {
        type: String,
        required: true,
        match: [/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Format: HH:MM']
      },
      location: {
        building: String,
        room: String,
        capacity: Number
      }
    }],
    
    // Dates calculées automatiquement
    endDate: Date,
    totalSessions: Number
  },
  
  // Statut de l'affectation
  status: {
    type: String,
    enum: ['draft', 'active', 'completed', 'cancelled'],
    default: 'draft'
  },
  
  // Métadonnées
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  notes: {
    type: String,
    trim: true
  }
}, {
  timestamps: true
});

// Index pour améliorer les performances
courseAssignmentSchema.index({ course: 1 });
courseAssignmentSchema.index({ 'instructors.professor': 1 });
courseAssignmentSchema.index({ 'classes.className': 1 });
courseAssignmentSchema.index({ 'schedule.startDate': 1 });

// Créneaux horaires disponibles du système EMBA
const TIME_SLOTS = {
  morning1: { start: '09:00', end: '10:30' },
  morning2: { start: '10:45', end: '12:15' },
  afternoon1: { start: '13:30', end: '15:00' },
  afternoon2: { start: '15:15', end: '16:45' }
};

// Méthode pour générer automatiquement les créneaux
courseAssignmentSchema.methods.generateTimeSlots = function() {
  const hoursNeeded = this.schedule.hoursPerWeek;
  
  // Pour 3h : un bloc de 3h (2 créneaux consécutifs)
  if (hoursNeeded === 3) {
    // Proposer des créneaux de 3h (2 × 1h30 avec pause)
    const availableBlocks = [
      { 
        dayOfWeek: 'Monday', 
        startTime: '09:00', 
        endTime: '12:15',
        description: 'Lundi matin (9h-12h15)' 
      },
      { 
        dayOfWeek: 'Tuesday', 
        startTime: '09:00', 
        endTime: '12:15',
        description: 'Mardi matin (9h-12h15)' 
      },
      { 
        dayOfWeek: 'Wednesday', 
        startTime: '09:00', 
        endTime: '12:15',
        description: 'Mercredi matin (9h-12h15)' 
      },
      { 
        dayOfWeek: 'Thursday', 
        startTime: '09:00', 
        endTime: '12:15',
        description: 'Jeudi matin (9h-12h15)' 
      },
      { 
        dayOfWeek: 'Friday', 
        startTime: '09:00', 
        endTime: '12:15',
        description: 'Vendredi matin (9h-12h15)' 
      },
      { 
        dayOfWeek: 'Monday', 
        startTime: '13:30', 
        endTime: '16:45',
        description: 'Lundi après-midi (13h30-16h45)' 
      },
      { 
        dayOfWeek: 'Tuesday', 
        startTime: '13:30', 
        endTime: '16:45',
        description: 'Mardi après-midi (13h30-16h45)' 
      },
      { 
        dayOfWeek: 'Wednesday', 
        startTime: '13:30', 
        endTime: '16:45',
        description: 'Mercredi après-midi (13h30-16h45)' 
      },
      { 
        dayOfWeek: 'Thursday', 
        startTime: '13:30', 
        endTime: '16:45',
        description: 'Jeudi après-midi (13h30-16h45)' 
      },
      { 
        dayOfWeek: 'Friday', 
        startTime: '13:30', 
        endTime: '16:45',
        description: 'Vendredi après-midi (13h30-16h45)' 
      },
      { 
        dayOfWeek: 'Saturday', 
        startTime: '09:00', 
        endTime: '12:15',
        description: 'Samedi matin (9h-12h15) - Rattrapage' 
      }
    ];
    
    return availableBlocks;
  }
  
  return [];
};

// Méthode pour calculer les dates de fin et nombre de sessions
courseAssignmentSchema.methods.calculateScheduleDates = function() {
  const startDate = new Date(this.schedule.startDate);
  const durationWeeks = this.schedule.durationWeeks;
  
  // Calculer la date de fin (en sautant les weekends)
  const endDate = new Date(startDate);
  endDate.setDate(startDate.getDate() + (durationWeeks * 7));
  
  this.schedule.endDate = endDate;
  this.schedule.totalSessions = durationWeeks; // Une session par semaine
  
  return this;
};

// Méthode statique pour obtenir les dates des semestres 2025-2026
courseAssignmentSchema.statics.getSemesterDates = function(semester) {
  const dates = {
    'Fall': {
      start: new Date('2025-09-01'),
      end: new Date('2025-12-20'),
      examPeriod: { start: new Date('2026-01-05'), end: new Date('2026-01-20') }
    },
    'Spring': {
      start: new Date('2026-01-20'),
      end: new Date('2026-05-30'),
      examPeriod: { start: new Date('2026-06-01'), end: new Date('2026-06-15') }
    }
  };
  
  return dates[semester] || null;
};

// Méthode pour détecter les conflits
courseAssignmentSchema.methods.detectConflicts = async function() {
  const conflicts = [];
  
  for (const timeSlot of this.schedule.timeSlots) {
    // Vérifier les conflits pour chaque professeur
    for (const instructor of this.instructors) {
      const professorConflicts = await this.constructor.find({
        'instructors.professor': instructor.professor,
        'schedule.timeSlots': {
          $elemMatch: {
            dayOfWeek: timeSlot.dayOfWeek,
            $or: [
              {
                startTime: { $lt: timeSlot.endTime },
                endTime: { $gt: timeSlot.startTime }
              }
            ]
          }
        },
        _id: { $ne: this._id },
        status: { $in: ['active', 'draft'] }
      }).populate('course', 'courseCode title');
      
      professorConflicts.forEach(conflict => {
        conflicts.push({
          type: 'professor',
          severity: 'warning', // Les conflits professeur sont des avertissements
          professorId: instructor.professor,
          conflictWith: conflict.course.courseCode,
          timeSlot: timeSlot,
          message: `Le professeur a déjà un cours (${conflict.course.courseCode}) le ${getDayLabel(timeSlot.dayOfWeek)} de ${timeSlot.startTime} à ${timeSlot.endTime}`
        });
      });
    }
    
    // Vérifier les conflits pour chaque classe
    for (const classInfo of this.classes) {
      const classConflicts = await this.constructor.find({
        'classes.className': classInfo.className,
        'schedule.timeSlots': {
          $elemMatch: {
            dayOfWeek: timeSlot.dayOfWeek,
            $or: [
              {
                startTime: { $lt: timeSlot.endTime },
                endTime: { $gt: timeSlot.startTime }
              }
            ]
          }
        },
        _id: { $ne: this._id },
        status: { $in: ['active', 'draft'] }
      }).populate('course', 'courseCode title');
      
      classConflicts.forEach(conflict => {
        conflicts.push({
          type: 'class',
          severity: 'critical', // Les conflits de classe sont critiques
          className: classInfo.className,
          conflictWith: conflict.course.courseCode,
          timeSlot: timeSlot,
          message: `La classe ${classInfo.className} a déjà un cours (${conflict.course.courseCode}) le ${getDayLabel(timeSlot.dayOfWeek)} de ${timeSlot.startTime} à ${timeSlot.endTime}`
        });
      });
    }
  }
  
  return conflicts;
};

// Fonction utilitaire pour les labels des jours
function getDayLabel(day) {
  const labels = {
    'Monday': 'Lundi',
    'Tuesday': 'Mardi',
    'Wednesday': 'Mercredi',
    'Thursday': 'Jeudi',
    'Friday': 'Vendredi',
    'Saturday': 'Samedi'
  };
  return labels[day] || day;
}

module.exports = mongoose.model('CourseAssignment', courseAssignmentSchema);
