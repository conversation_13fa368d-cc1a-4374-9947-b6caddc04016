const mongoose = require('mongoose');

const courseDocumentSchema = new mongoose.Schema({
  // Référence au cours
  course: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Course',
    required: true
  },

  // Informations du document
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  description: {
    type: String,
    trim: true,
    maxlength: 1000
  },
  
  // Type de document
  documentType: {
    type: String,
    enum: ['course_material', 'syllabus', 'assignment', 'exam', 'reference', 'other'],
    default: 'course_material'
  },

  // Informations du fichier
  filename: {
    type: String,
    required: true,
    trim: true
  },
  originalName: {
    type: String,
    required: true,
    trim: true
  },
  filePath: {
    type: String,
    required: true,
    trim: true
  },
  fileSize: {
    type: Number,
    required: true,
    min: 0
  },
  mimeType: {
    type: String,
    required: true,
    trim: true
  },
  fileExtension: {
    type: String,
    required: true,
    trim: true,
    lowercase: true
  },

  // Permissions et visibilité
  isVisible: {
    type: Boolean,
    default: true
  },
  visibleToStudents: {
    type: Boolean,
    default: true
  },
  downloadable: {
    type: Boolean,
    default: true
  },

  // Ordre d'affichage
  displayOrder: {
    type: Number,
    default: 0
  },

  // Métadonnées
  uploadedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },

  // Statistiques de téléchargement
  downloadCount: {
    type: Number,
    default: 0
  },
  lastDownloaded: {
    type: Date
  },

  // Tags pour la recherche
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],

  // Statut
  status: {
    type: String,
    enum: ['active', 'archived', 'deleted'],
    default: 'active'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Index pour améliorer les performances
courseDocumentSchema.index({ course: 1 });
courseDocumentSchema.index({ uploadedBy: 1 });
courseDocumentSchema.index({ documentType: 1 });
courseDocumentSchema.index({ status: 1 });
courseDocumentSchema.index({ displayOrder: 1 });

// Virtual pour obtenir l'URL de téléchargement
courseDocumentSchema.virtual('downloadUrl').get(function() {
  return `/api/courses/${this.course}/documents/${this._id}/download`;
});

// Virtual pour formater la taille du fichier
courseDocumentSchema.virtual('formattedFileSize').get(function() {
  const bytes = this.fileSize;
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
});

// Méthode pour incrémenter le compteur de téléchargements
courseDocumentSchema.methods.incrementDownloadCount = function() {
  this.downloadCount += 1;
  this.lastDownloaded = new Date();
  return this.save();
};

// Méthode pour vérifier si le fichier est une image
courseDocumentSchema.methods.isImage = function() {
  const imageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  return imageTypes.includes(this.mimeType);
};

// Méthode pour vérifier si le fichier est un PDF
courseDocumentSchema.methods.isPDF = function() {
  return this.mimeType === 'application/pdf';
};

// Méthode pour vérifier si le fichier est un document Word
courseDocumentSchema.methods.isWordDocument = function() {
  const wordTypes = [
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ];
  return wordTypes.includes(this.mimeType);
};

// Middleware pour nettoyer les tags avant sauvegarde
courseDocumentSchema.pre('save', function(next) {
  if (this.tags && this.tags.length > 0) {
    this.tags = this.tags.filter(tag => tag && tag.trim().length > 0);
  }
  next();
});

module.exports = mongoose.model('CourseDocument', courseDocumentSchema);
