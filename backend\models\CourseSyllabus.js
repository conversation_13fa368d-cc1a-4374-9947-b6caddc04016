const mongoose = require('mongoose');

const courseSyllabusSchema = new mongoose.Schema({
  // Référence au cours
  course: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Course',
    required: true,
    unique: true
  },

  // Informations de base du cours
  courseName: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  numberOfHours: {
    type: Number,
    required: true,
    min: 1
  },
  numberOfCredits: {
    type: Number,
    required: true,
    min: 1
  },
  courseResponsible: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  teachingStaff: [{
    name: {
      type: String,
      required: true,
      trim: true,
      maxlength: 100
    },
    email: {
      type: String,
      required: true,
      trim: true,
      lowercase: true,
      match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
    }
  }],

  // 1. Présentation du cours
  coursePresentation: {
    description: {
      type: String,
      required: true,
      trim: true,
      maxlength: 2000
    },
    programObjectives: [{
      type: String,
      enum: [
        'PO.1', 'PO.2', 'PO.3', 'PO.4', 'PO.5', 
        'PO.6', 'PO.7', 'PO.8', 'PO.9'
      ]
    }],
    learningOutcomes: [{
      outcome: {
        type: String,
        required: true,
        trim: true,
        maxlength: 500
      },
      code: {
        type: String,
        enum: ['CLO.1', 'CLO.2', 'CLO.3', 'CLO.4', 'CLO.5']
      }
    }],
    pedagogicalApproach: {
      type: String,
      required: true,
      trim: true,
      maxlength: 1000
    }
  },

  // 2. Support de cours
  courseSupport: {
    referenceBook: {
      type: String,
      trim: true,
      maxlength: 500
    },
    platformSupports: {
      type: String,
      default: 'Supports à télécharger sur la plateforme Teams.',
      trim: true,
      maxlength: 500
    },
    recommendedBooks: [{
      type: String,
      trim: true,
      maxlength: 500
    }]
  },

  // 3. Contenu, activités et organisation
  courseContent: {
    sessions: [{
      week: {
        type: String,
        required: true,
        trim: true
      },
      sessionNumber: {
        type: Number,
        required: true,
        min: 1
      },
      theme: {
        type: String,
        required: true,
        trim: true,
        maxlength: 200
      },
      duration: {
        type: Number,
        required: true,
        min: 1
      },
      isOnline: {
        type: Boolean,
        default: false
      },
      isFaceToFace: {
        type: Boolean,
        default: true
      },
      preparatoryWork: {
        type: String,
        trim: true,
        maxlength: 1000
      }
    }],
    totalDuration: {
      type: Number,
      required: true,
      min: 1
    }
  },

  // 4. Évaluations
  evaluations: {
    continuousAssessment: {
      description: {
        type: String,
        trim: true,
        maxlength: 1000
      },
      weight: {
        type: Number,
        default: 30,
        min: 0,
        max: 100
      }
    },
    finalExam: {
      type: {
        type: String,
        trim: true,
        maxlength: 200
      },
      duration: {
        type: String,
        trim: true,
        maxlength: 100
      },
      documentsAllowed: {
        type: String,
        trim: true,
        maxlength: 200
      },
      calculatorAllowed: {
        type: Boolean,
        default: false
      },
      weight: {
        type: Number,
        default: 70,
        min: 0,
        max: 100
      }
    },
    gradingScale: [{
      minScore: {
        type: Number,
        required: true,
        min: 0,
        max: 100
      },
      maxScore: {
        type: Number,
        required: true,
        min: 0,
        max: 100
      },
      grade: {
        type: String,
        required: true,
        enum: ['A', 'B', 'C', 'D', 'E', 'Fx', 'F']
      },
      evaluation: {
        type: String,
        required: true,
        enum: ['Excellent', 'Très bien', 'Bien', 'Satisfaisant', 'Passable', 'Insuffisant', 'Échec']
      }
    }]
  },

  // Métadonnées
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  version: {
    type: Number,
    default: 1
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Index pour améliorer les performances
courseSyllabusSchema.index({ course: 1 });
courseSyllabusSchema.index({ createdBy: 1 });
courseSyllabusSchema.index({ isActive: 1 });

// Virtual pour calculer le total des poids d'évaluation
courseSyllabusSchema.virtual('totalEvaluationWeight').get(function() {
  return (this.evaluations?.continuousAssessment?.weight || 0) + 
         (this.evaluations?.finalExam?.weight || 0);
});

// Middleware pour valider que les poids d'évaluation totalisent 100%
courseSyllabusSchema.pre('save', function(next) {
  const totalWeight = (this.evaluations?.continuousAssessment?.weight || 0) + 
                     (this.evaluations?.finalExam?.weight || 0);
  
  if (totalWeight !== 100) {
    return next(new Error('Les poids d\'évaluation doivent totaliser 100%'));
  }
  
  next();
});

// Méthode pour initialiser l'échelle de notation par défaut
courseSyllabusSchema.methods.initializeDefaultGradingScale = function() {
  this.evaluations.gradingScale = [
    { minScore: 90, maxScore: 100, grade: 'A', evaluation: 'Excellent' },
    { minScore: 80, maxScore: 89.99, grade: 'B', evaluation: 'Très bien' },
    { minScore: 70, maxScore: 79.99, grade: 'C', evaluation: 'Bien' },
    { minScore: 60, maxScore: 69.99, grade: 'D', evaluation: 'Satisfaisant' },
    { minScore: 50, maxScore: 59.99, grade: 'E', evaluation: 'Passable' },
    { minScore: 35, maxScore: 49.99, grade: 'Fx', evaluation: 'Insuffisant' },
    { minScore: 0, maxScore: 34.99, grade: 'F', evaluation: 'Échec' }
  ];
};

module.exports = mongoose.model('CourseSyllabus', courseSyllabusSchema);
