const mongoose = require('mongoose');

const documentSchema = new mongoose.Schema({
  // Informations de base
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  description: {
    type: String,
    trim: true,
    maxlength: 1000
  },
  
  // Informations du fichier
  filename: {
    type: String,
    required: true,
    trim: true
  },
  originalName: {
    type: String,
    required: true,
    trim: true
  },
  path: {
    type: String,
    required: true,
    trim: true
  },
  size: {
    type: Number,
    required: true,
    min: 0
  },
  mimeType: {
    type: String,
    required: true,
    trim: true
  },
  extension: {
    type: String,
    trim: true,
    lowercase: true
  },
  
  // Classification
  category: {
    type: String,
    enum: [
      'academic_record', 'transcript', 'diploma', 'certificate',
      'identification', 'photo', 'cv_resume', 'recommendation_letter',
      'financial_document', 'payment_receipt', 'scholarship_document',
      'course_material', 'assignment', 'project', 'thesis',
      'administrative', 'form', 'contract', 'agreement',
      'policy', 'procedure', 'guideline', 'manual',
      'report', 'presentation', 'research', 'publication',
      'other'
    ],
    required: true
  },
  subcategory: {
    type: String,
    trim: true
  },
  
  // Propriétaire et accès
  owner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  // Références contextuelles
  relatedTo: {
    entityType: {
      type: String,
      enum: ['student', 'professor', 'course', 'assignment', 'application', 'enrollment', 'payment', 'grade']
    },
    entityId: { type: mongoose.Schema.Types.ObjectId },
    entityTitle: { type: String, trim: true }
  },
  
  // Contrôle d'accès
  visibility: {
    type: String,
    enum: ['private', 'shared', 'public', 'restricted'],
    default: 'private'
  },
  permissions: {
    canView: [{
      user: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
      role: { type: String, enum: ['student', 'professor', 'admin', 'staff'] },
      grantedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
      grantedAt: { type: Date, default: Date.now }
    }],
    canEdit: [{
      user: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
      role: { type: String, enum: ['student', 'professor', 'admin', 'staff'] },
      grantedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
      grantedAt: { type: Date, default: Date.now }
    }],
    canDelete: [{
      user: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
      role: { type: String, enum: ['student', 'professor', 'admin', 'staff'] },
      grantedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
      grantedAt: { type: Date, default: Date.now }
    }]
  },
  
  // Statut et workflow
  status: {
    type: String,
    enum: ['draft', 'pending_review', 'approved', 'rejected', 'archived', 'deleted'],
    default: 'draft'
  },
  
  // Processus d'approbation
  approval: {
    isRequired: { type: Boolean, default: false },
    approvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    approvedAt: { type: Date },
    rejectedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    rejectedAt: { type: Date },
    rejectionReason: { type: String, trim: true },
    approvalComments: { type: String, trim: true }
  },
  
  // Versioning
  version: {
    number: { type: Number, default: 1 },
    isLatest: { type: Boolean, default: true },
    parentDocument: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Document'
    },
    versionHistory: [{
      version: { type: Number, required: true },
      filename: { type: String, required: true },
      path: { type: String, required: true },
      size: { type: Number, required: true },
      uploadedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      uploadedAt: { type: Date, default: Date.now },
      changes: { type: String, trim: true }
    }]
  },
  
  // Métadonnées du document
  metadata: {
    tags: [{ type: String, trim: true, lowercase: true }],
    keywords: [{ type: String, trim: true }],
    subject: { type: String, trim: true },
    author: { type: String, trim: true },
    createdDate: { type: Date },
    modifiedDate: { type: Date },
    language: {
      type: String,
      enum: ['fr', 'en', 'ar', 'other'],
      default: 'fr'
    },
    pageCount: { type: Number, min: 0 },
    wordCount: { type: Number, min: 0 }
  },
  
  // Sécurité et confidentialité
  security: {
    isEncrypted: { type: Boolean, default: false },
    encryptionMethod: { type: String, trim: true },
    isPasswordProtected: { type: Boolean, default: false },
    confidentialityLevel: {
      type: String,
      enum: ['public', 'internal', 'confidential', 'restricted'],
      default: 'internal'
    },
    accessLog: [{
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      action: {
        type: String,
        enum: ['view', 'download', 'edit', 'delete', 'share']
      },
      timestamp: { type: Date, default: Date.now },
      ipAddress: { type: String, trim: true },
      userAgent: { type: String, trim: true }
    }]
  },
  
  // Validation et vérification
  validation: {
    isValidated: { type: Boolean, default: false },
    validatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    validatedAt: { type: Date },
    validationMethod: {
      type: String,
      enum: ['manual', 'automated', 'third_party']
    },
    validationNotes: { type: String, trim: true },
    checksumMD5: { type: String, trim: true },
    checksumSHA256: { type: String, trim: true }
  },
  
  // Expiration et archivage
  retention: {
    expiresAt: { type: Date },
    retentionPeriod: { type: Number, min: 0 }, // en jours
    autoDelete: { type: Boolean, default: false },
    archiveAfterDays: { type: Number, min: 0 },
    isArchived: { type: Boolean, default: false },
    archivedAt: { type: Date },
    archivedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  },
  
  // Partage et collaboration
  sharing: {
    isShared: { type: Boolean, default: false },
    shareLink: { type: String, trim: true },
    shareLinkExpires: { type: Date },
    sharePassword: { type: String, trim: true },
    allowDownload: { type: Boolean, default: true },
    allowPrint: { type: Boolean, default: true },
    trackViews: { type: Boolean, default: true },
    sharedWith: [{
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      email: { type: String, trim: true, lowercase: true },
      permissions: {
        type: String,
        enum: ['view', 'comment', 'edit'],
        default: 'view'
      },
      sharedAt: { type: Date, default: Date.now },
      sharedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      }
    }]
  },
  
  // Commentaires et annotations
  comments: [{
    author: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    content: { type: String, required: true, trim: true },
    pageNumber: { type: Number, min: 1 },
    position: {
      x: { type: Number },
      y: { type: Number }
    },
    isResolved: { type: Boolean, default: false },
    resolvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    resolvedAt: { type: Date },
    replies: [{
      author: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
      },
      content: { type: String, required: true, trim: true },
      createdAt: { type: Date, default: Date.now }
    }],
    createdAt: { type: Date, default: Date.now }
  }],
  
  // Statistiques d'utilisation
  statistics: {
    viewCount: { type: Number, default: 0 },
    downloadCount: { type: Number, default: 0 },
    shareCount: { type: Number, default: 0 },
    lastViewed: { type: Date },
    lastDownloaded: { type: Date },
    uniqueViewers: { type: Number, default: 0 },
    averageViewDuration: { type: Number, default: 0 } // en secondes
  },
  
  // Informations de stockage
  storage: {
    provider: {
      type: String,
      enum: ['local', 'aws_s3', 'google_drive', 'dropbox', 'azure'],
      default: 'local'
    },
    bucket: { type: String, trim: true },
    region: { type: String, trim: true },
    storageClass: { type: String, trim: true },
    backupLocation: { type: String, trim: true },
    isBackedUp: { type: Boolean, default: false },
    lastBackup: { type: Date }
  },
  
  // Métadonnées
  uploadedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Index pour améliorer les performances
documentSchema.index({ owner: 1, category: 1 });
documentSchema.index({ filename: 1 });
documentSchema.index({ status: 1 });
documentSchema.index({ 'relatedTo.entityType': 1, 'relatedTo.entityId': 1 });
documentSchema.index({ 'metadata.tags': 1 });
documentSchema.index({ createdAt: -1 });

// Virtual pour obtenir la taille formatée
documentSchema.virtual('formattedSize').get(function() {
  const bytes = this.size;
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
});

// Virtual pour vérifier si le document est expiré
documentSchema.virtual('isExpired').get(function() {
  return this.retention.expiresAt && new Date() > this.retention.expiresAt;
});

// Virtual pour obtenir l'URL de téléchargement
documentSchema.virtual('downloadUrl').get(function() {
  return `/api/documents/${this._id}/download`;
});

// Middleware pour extraire l'extension du fichier
documentSchema.pre('save', function(next) {
  if (this.originalName && !this.extension) {
    const lastDotIndex = this.originalName.lastIndexOf('.');
    if (lastDotIndex !== -1) {
      this.extension = this.originalName.substring(lastDotIndex + 1).toLowerCase();
    }
  }
  
  // Définir la date d'expiration si une période de rétention est définie
  if (this.retention.retentionPeriod && !this.retention.expiresAt) {
    const expirationDate = new Date();
    expirationDate.setDate(expirationDate.getDate() + this.retention.retentionPeriod);
    this.retention.expiresAt = expirationDate;
  }
  
  next();
});

// Méthode pour vérifier les permissions d'accès
documentSchema.methods.hasPermission = function(userId, permission) {
  // Le propriétaire a tous les droits
  if (this.owner.toString() === userId.toString()) {
    return true;
  }
  
  // Vérifier les permissions spécifiques
  const permissionArray = this.permissions[`can${permission.charAt(0).toUpperCase() + permission.slice(1)}`];
  return permissionArray.some(perm => 
    perm.user && perm.user.toString() === userId.toString()
  );
};

// Méthode pour enregistrer un accès
documentSchema.methods.logAccess = function(userId, action, ipAddress, userAgent) {
  this.security.accessLog.push({
    user: userId,
    action: action,
    ipAddress: ipAddress,
    userAgent: userAgent
  });
  
  // Mettre à jour les statistiques
  if (action === 'view') {
    this.statistics.viewCount += 1;
    this.statistics.lastViewed = new Date();
  } else if (action === 'download') {
    this.statistics.downloadCount += 1;
    this.statistics.lastDownloaded = new Date();
  }
  
  return this.save();
};

// Méthode pour approuver le document
documentSchema.methods.approve = function(approvedBy, comments) {
  this.status = 'approved';
  this.approval.approvedBy = approvedBy;
  this.approval.approvedAt = new Date();
  this.approval.approvalComments = comments;
  
  return this.save();
};

// Méthode pour rejeter le document
documentSchema.methods.reject = function(rejectedBy, reason) {
  this.status = 'rejected';
  this.approval.rejectedBy = rejectedBy;
  this.approval.rejectedAt = new Date();
  this.approval.rejectionReason = reason;
  
  return this.save();
};

// Méthode pour créer une nouvelle version
documentSchema.methods.createNewVersion = function(newFileData, uploadedBy, changes) {
  // Marquer la version actuelle comme non-latest
  this.version.isLatest = false;
  
  // Ajouter à l'historique des versions
  this.version.versionHistory.push({
    version: this.version.number,
    filename: this.filename,
    path: this.path,
    size: this.size,
    uploadedBy: this.uploadedBy,
    uploadedAt: this.createdAt,
    changes: changes
  });
  
  // Mettre à jour avec les nouvelles données
  this.filename = newFileData.filename;
  this.path = newFileData.path;
  this.size = newFileData.size;
  this.mimeType = newFileData.mimeType;
  this.version.number += 1;
  this.version.isLatest = true;
  this.uploadedBy = uploadedBy;
  this.updatedBy = uploadedBy;
  
  return this.save();
};

// Méthode pour partager le document
documentSchema.methods.shareWith = function(userIdOrEmail, permissions, sharedBy) {
  this.sharing.isShared = true;
  this.sharing.sharedWith.push({
    user: mongoose.Types.ObjectId.isValid(userIdOrEmail) ? userIdOrEmail : undefined,
    email: mongoose.Types.ObjectId.isValid(userIdOrEmail) ? undefined : userIdOrEmail,
    permissions: permissions,
    sharedBy: sharedBy
  });
  
  this.statistics.shareCount += 1;
  return this.save();
};

// Méthode pour archiver le document
documentSchema.methods.archive = function(archivedBy) {
  this.retention.isArchived = true;
  this.retention.archivedAt = new Date();
  this.retention.archivedBy = archivedBy;
  this.status = 'archived';
  
  return this.save();
};

// Méthode statique pour nettoyer les documents expirés
documentSchema.statics.cleanupExpired = function() {
  return this.updateMany(
    {
      'retention.expiresAt': { $lt: new Date() },
      'retention.autoDelete': true,
      status: { $ne: 'deleted' }
    },
    {
      $set: { status: 'deleted' }
    }
  );
};

// Méthode statique pour rechercher des documents
documentSchema.statics.search = function(query, userId, filters = {}) {
  const searchConditions = {
    $or: [
      { owner: userId },
      { 'permissions.canView.user': userId },
      { visibility: 'public' }
    ]
  };
  
  if (query) {
    searchConditions.$and = [
      {
        $or: [
          { title: { $regex: query, $options: 'i' } },
          { description: { $regex: query, $options: 'i' } },
          { originalName: { $regex: query, $options: 'i' } },
          { 'metadata.tags': { $in: [new RegExp(query, 'i')] } },
          { 'metadata.keywords': { $in: [new RegExp(query, 'i')] } }
        ]
      }
    ];
  }
  
  if (filters.category) searchConditions.category = filters.category;
  if (filters.status) searchConditions.status = filters.status;
  if (filters.dateFrom || filters.dateTo) {
    searchConditions.createdAt = {};
    if (filters.dateFrom) searchConditions.createdAt.$gte = new Date(filters.dateFrom);
    if (filters.dateTo) searchConditions.createdAt.$lte = new Date(filters.dateTo);
  }
  
  return this.find(searchConditions)
    .populate('owner', 'firstName lastName')
    .populate('uploadedBy', 'firstName lastName')
    .sort({ createdAt: -1 });
};

module.exports = mongoose.model('Document', documentSchema);
