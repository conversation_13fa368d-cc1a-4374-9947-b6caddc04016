const mongoose = require('mongoose');

const enrollmentSchema = new mongoose.Schema({
  // Références principales
  student: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Student',
    required: true
  },
  course: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Course'
  },
  module: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Module'
  },
  
  // Informations d'inscription
  enrollmentNumber: {
    type: String,
    unique: true,
    trim: true
  },
  academicYear: {
    type: String,
    required: true,
    match: [/^\d{4}-\d{4}$/, 'Format: YYYY-YYYY']
  },
  semester: {
    type: String,
    enum: ['Fall', 'Spring', 'Summer', 'Year-long'],
    required: true
  },
  
  // Dates importantes
  enrollmentDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  startDate: {
    type: Date,
    required: true
  },
  expectedEndDate: {
    type: Date,
    required: true
  },
  actualEndDate: {
    type: Date
  },
  withdrawalDate: {
    type: Date
  },
  
  // Statut de l'inscription
  status: {
    type: String,
    enum: ['pending', 'enrolled', 'active', 'completed', 'withdrawn', 'failed', 'deferred', 'suspended'],
    default: 'pending'
  },
  previousStatus: {
    type: String,
    enum: ['pending', 'enrolled', 'active', 'completed', 'withdrawn', 'failed', 'deferred', 'suspended']
  },
  
  // Type d'inscription
  enrollmentType: {
    type: String,
    enum: ['regular', 'audit', 'credit', 'non_credit', 'transfer'],
    default: 'regular'
  },
  
  // Priorité d'inscription (pour la liste d'attente)
  priority: {
    type: Number,
    min: 1,
    max: 10,
    default: 5
  },
  
  // Informations de paiement
  paymentStatus: {
    type: String,
    enum: ['pending', 'partial', 'paid', 'overdue', 'waived'],
    default: 'pending'
  },
  tuitionFee: {
    amount: { type: Number, required: true, min: 0 },
    currency: { type: String, default: 'TND' },
    dueDate: { type: Date, required: true },
    paidAmount: { type: Number, default: 0, min: 0 },
    remainingAmount: { type: Number, default: 0, min: 0 }
  },
  
  // Raisons de changement de statut
  statusHistory: [{
    status: {
      type: String,
      enum: ['pending', 'enrolled', 'active', 'completed', 'withdrawn', 'failed', 'deferred', 'suspended'],
      required: true
    },
    reason: { type: String, trim: true },
    changedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    changedAt: { type: Date, default: Date.now },
    comments: { type: String, trim: true }
  }],
  
  // Informations de retrait/abandon
  withdrawal: {
    reason: {
      type: String,
      enum: ['Personal', 'Financial', 'Academic', 'Health', 'Work Conflict', 'Family', 'Other']
    },
    detailedReason: { type: String, trim: true },
    isRefundEligible: { type: Boolean, default: false },
    refundAmount: { type: Number, min: 0, default: 0 },
    processedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    approvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  },
  
  // Performance académique
  academicPerformance: {
    currentGrade: { type: Number, min: 0, max: 20 },
    midtermGrade: { type: Number, min: 0, max: 20 },
    finalGrade: { type: Number, min: 0, max: 20 },
    letterGrade: {
      type: String,
      enum: ['A+', 'A', 'A-', 'B+', 'B', 'B-', 'C+', 'C', 'C-', 'D+', 'D', 'F', 'I', 'W', 'P', 'NP']
    },
    gpaPoints: { type: Number, min: 0, max: 4 },
    creditHours: { type: Number, min: 0 },
    qualityPoints: { type: Number, min: 0 }
  },
  
  // Présence et participation
  attendance: {
    totalSessions: { type: Number, default: 0 },
    attendedSessions: { type: Number, default: 0 },
    excusedAbsences: { type: Number, default: 0 },
    unexcusedAbsences: { type: Number, default: 0 },
    attendanceRate: { type: Number, min: 0, max: 100, default: 0 },
    participationGrade: { type: Number, min: 0, max: 20 }
  },
  
  // Évaluations et devoirs
  assignments: [{
    assignment: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Assignment'
    },
    submissionDate: { type: Date },
    grade: { type: Number, min: 0, max: 20 },
    feedback: { type: String, trim: true },
    isLate: { type: Boolean, default: false },
    latePenalty: { type: Number, min: 0, default: 0 }
  }],
  
  // Accommodations spéciales
  accommodations: [{
    type: {
      type: String,
      enum: ['Extended Time', 'Alternative Format', 'Assistive Technology', 'Note Taking', 'Interpreter', 'Other']
    },
    description: { type: String, required: true, trim: true },
    approvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    startDate: { type: Date },
    endDate: { type: Date },
    isActive: { type: Boolean, default: true }
  }],
  
  // Préférences d'apprentissage
  learningPreferences: {
    preferredSeatLocation: {
      type: String,
      enum: ['Front', 'Middle', 'Back', 'Side', 'No Preference']
    },
    groupWorkPreference: {
      type: String,
      enum: ['Individual', 'Small Groups', 'Large Groups', 'Mixed']
    },
    communicationPreference: {
      type: String,
      enum: ['Email', 'Phone', 'In Person', 'Text', 'Platform Messages']
    }
  },
  
  // Feedback de l'étudiant
  studentFeedback: {
    courseRating: { type: Number, min: 1, max: 5 },
    instructorRating: { type: Number, min: 1, max: 5 },
    difficultyRating: { type: Number, min: 1, max: 5 },
    workloadRating: { type: Number, min: 1, max: 5 },
    recommendationRating: { type: Number, min: 1, max: 5 },
    comments: { type: String, trim: true },
    suggestions: { type: String, trim: true },
    feedbackDate: { type: Date }
  },
  
  // Informations de transfert de crédits
  creditTransfer: {
    isTransferCredit: { type: Boolean, default: false },
    previousInstitution: { type: String, trim: true },
    originalCourse: { type: String, trim: true },
    originalGrade: { type: String, trim: true },
    transferGrade: { type: String, trim: true },
    evaluatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    evaluationDate: { type: Date }
  },
  
  // Notifications et rappels
  notifications: [{
    type: {
      type: String,
      enum: ['Assignment Due', 'Grade Posted', 'Attendance Warning', 'Payment Due', 'Course Update', 'Other']
    },
    message: { type: String, required: true, trim: true },
    sentDate: { type: Date, default: Date.now },
    isRead: { type: Boolean, default: false },
    readDate: { type: Date }
  }],
  
  // Métadonnées
  enrolledBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  approvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  lastModifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Index pour améliorer les performances
enrollmentSchema.index({ student: 1, course: 1 }, { unique: true, sparse: true });
enrollmentSchema.index({ student: 1, module: 1 }, { unique: true, sparse: true });
enrollmentSchema.index({ enrollmentNumber: 1 });
enrollmentSchema.index({ status: 1 });
enrollmentSchema.index({ academicYear: 1, semester: 1 });
enrollmentSchema.index({ paymentStatus: 1 });

// Virtual pour calculer le taux de présence
enrollmentSchema.virtual('attendancePercentage').get(function() {
  if (this.attendance.totalSessions === 0) return 0;
  return Math.round((this.attendance.attendedSessions / this.attendance.totalSessions) * 100);
});

// Virtual pour calculer le solde restant
enrollmentSchema.virtual('remainingBalance').get(function() {
  return this.tuitionFee.amount - this.tuitionFee.paidAmount;
});

// Virtual pour vérifier si l'inscription est active
enrollmentSchema.virtual('isActive').get(function() {
  return ['enrolled', 'active'].includes(this.status);
});

// Virtual pour vérifier si l'étudiant a réussi
enrollmentSchema.virtual('hasPassed').get(function() {
  return this.academicPerformance.finalGrade >= 10; // Assuming 10/20 is passing grade
});

// Middleware pour générer automatiquement le numéro d'inscription
enrollmentSchema.pre('save', async function(next) {
  if (!this.enrollmentNumber) {
    const year = new Date().getFullYear();
    const count = await this.constructor.countDocuments({
      enrollmentNumber: new RegExp(`^ENR${year}`)
    });
    this.enrollmentNumber = `ENR${year}${String(count + 1).padStart(6, '0')}`;
  }
  
  // Calculer le solde restant
  this.tuitionFee.remainingAmount = this.tuitionFee.amount - this.tuitionFee.paidAmount;
  
  // Calculer le taux de présence
  if (this.attendance.totalSessions > 0) {
    this.attendance.attendanceRate = Math.round(
      (this.attendance.attendedSessions / this.attendance.totalSessions) * 100
    );
  }
  
  next();
});

// Middleware pour enregistrer l'historique des changements de statut
enrollmentSchema.pre('save', function(next) {
  if (this.isModified('status') && !this.isNew) {
    this.statusHistory.push({
      status: this.status,
      changedAt: new Date(),
      changedBy: this.lastModifiedBy
    });
    this.previousStatus = this.status;
  }
  next();
});

// Méthode pour calculer la note finale
enrollmentSchema.methods.calculateFinalGrade = function() {
  // Cette méthode devrait être implémentée selon le système de notation spécifique
  // Pour l'instant, on utilise la note finale directement
  return this.academicPerformance.finalGrade;
};

// Méthode pour vérifier l'éligibilité au retrait
enrollmentSchema.methods.canWithdraw = function() {
  const now = new Date();
  const withdrawalDeadline = new Date(this.startDate);
  withdrawalDeadline.setDate(withdrawalDeadline.getDate() + 14); // 2 semaines après le début
  
  return now <= withdrawalDeadline && ['enrolled', 'active'].includes(this.status);
};

// Méthode pour calculer le remboursement
enrollmentSchema.methods.calculateRefund = function() {
  if (!this.canWithdraw()) return 0;
  
  const now = new Date();
  const daysSinceStart = Math.floor((now - this.startDate) / (1000 * 60 * 60 * 24));
  
  if (daysSinceStart <= 7) return this.tuitionFee.paidAmount * 0.8; // 80% refund
  if (daysSinceStart <= 14) return this.tuitionFee.paidAmount * 0.5; // 50% refund
  return 0;
};

// Méthode pour marquer une notification comme lue
enrollmentSchema.methods.markNotificationAsRead = function(notificationId) {
  const notification = this.notifications.id(notificationId);
  if (notification) {
    notification.isRead = true;
    notification.readDate = new Date();
    return this.save();
  }
  return Promise.reject(new Error('Notification not found'));
};

// Méthode pour ajouter une notification
enrollmentSchema.methods.addNotification = function(type, message) {
  this.notifications.push({
    type: type,
    message: message,
    sentDate: new Date(),
    isRead: false
  });
  return this.save();
};

// Méthode pour obtenir les notifications non lues
enrollmentSchema.methods.getUnreadNotifications = function() {
  return this.notifications.filter(notification => !notification.isRead);
};

// Méthode pour mettre à jour le statut de paiement
enrollmentSchema.methods.updatePaymentStatus = function() {
  const remaining = this.remainingBalance;
  
  if (remaining <= 0) {
    this.paymentStatus = 'paid';
  } else if (this.tuitionFee.paidAmount > 0) {
    this.paymentStatus = 'partial';
  } else if (new Date() > this.tuitionFee.dueDate) {
    this.paymentStatus = 'overdue';
  } else {
    this.paymentStatus = 'pending';
  }
  
  return this.save();
};

module.exports = mongoose.model('Enrollment', enrollmentSchema);
