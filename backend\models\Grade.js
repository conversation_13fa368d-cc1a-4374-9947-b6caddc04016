const mongoose = require('mongoose');

const gradeSchema = new mongoose.Schema({
  // Références principales
  student: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Student',
    required: true
  },
  course: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Course'
  },
  module: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Module'
  },
  assignment: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Assignment'
  },

  // Informations académiques
  academicYear: {
    type: String,
    required: true,
    match: [/^\d{4}-\d{4}$/, 'Format: YYYY-YYYY']
  },
  semester: {
    type: String,
    enum: ['Fall', 'Spring', 'Summer', 'Year-long'],
    required: true
  },

  // Type d'évaluation
  gradeType: {
    type: String,
    enum: ['assignment', 'quiz', 'midterm', 'final_exam', 'project', 'participation', 'attendance', 'overall'],
    required: true
  },
  gradeCategory: {
    type: String,
    enum: ['formative', 'summative', 'diagnostic'],
    default: 'summative'
  },

  // Notes numériques
  rawScore: {
    type: Number,
    min: 0,
    max: 100
  },
  maxScore: {
    type: Number,
    required: true,
    min: 1,
    default: 100
  },
  percentage: {
    type: Number,
    min: 0,
    max: 100
  },

  // Note sur 20 (système tunisien)
  gradeOutOf20: {
    type: Number,
    min: 0,
    max: 20
  },

  // Note littérale
  letterGrade: {
    type: String,
    enum: ['A+', 'A', 'A-', 'B+', 'B', 'B-', 'C+', 'C', 'C-', 'D+', 'D', 'F', 'I', 'W', 'P', 'NP']
  },

  // Points GPA
  gpaPoints: {
    type: Number,
    min: 0,
    max: 4
  },

  // Poids de l'évaluation
  weight: {
    type: Number,
    min: 0,
    max: 100,
    default: 100
  },

  // Dates importantes
  assessmentDate: {
    type: Date,
    required: true
  },
  submissionDate: {
    type: Date
  },
  gradedDate: {
    type: Date,
    default: Date.now
  },
  publishedDate: {
    type: Date
  },

  // Statut de la note
  status: {
    type: String,
    enum: ['draft', 'submitted', 'graded', 'published', 'disputed', 'revised'],
    default: 'draft'
  },
  isPublished: {
    type: Boolean,
    default: false
  },

  // Feedback et commentaires
  feedback: {
    strengths: [{ type: String, trim: true }],
    areasForImprovement: [{ type: String, trim: true }],
    generalComments: { type: String, trim: true },
    specificComments: [{
      criterion: { type: String, trim: true },
      comment: { type: String, trim: true },
      score: { type: Number, min: 0 }
    }]
  },

  // Critères d'évaluation détaillés
  rubricScores: [{
    criterion: { type: String, required: true, trim: true },
    maxPoints: { type: Number, required: true, min: 1 },
    earnedPoints: { type: Number, required: true, min: 0 },
    level: {
      type: String,
      enum: ['Excellent', 'Good', 'Satisfactory', 'Needs Improvement', 'Unsatisfactory']
    },
    comments: { type: String, trim: true }
  }],

  // Informations sur la soumission
  submission: {
    isLate: { type: Boolean, default: false },
    daysLate: { type: Number, min: 0, default: 0 },
    latePenalty: { type: Number, min: 0, default: 0 },
    attemptNumber: { type: Number, min: 1, default: 1 },
    timeSpent: { type: Number, min: 0 }, // en minutes
    submissionMethod: {
      type: String,
      enum: ['online', 'paper', 'email', 'presentation', 'other'],
      default: 'online'
    }
  },

  // Révisions et historique
  revisions: [{
    previousGrade: { type: Number, min: 0, max: 20 },
    newGrade: { type: Number, min: 0, max: 20 },
    reason: { type: String, required: true, trim: true },
    revisedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    revisionDate: { type: Date, default: Date.now },
    approvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  }],

  // Contestation de note
  dispute: {
    isDisputed: { type: Boolean, default: false },
    disputeReason: { type: String, trim: true },
    disputeDate: { type: Date },
    disputeStatus: {
      type: String,
      enum: ['pending', 'under_review', 'resolved', 'rejected']
    },
    resolution: { type: String, trim: true },
    resolvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    resolvedDate: { type: Date }
  },

  // Statistiques comparatives
  statistics: {
    classAverage: { type: Number, min: 0, max: 20 },
    classMedian: { type: Number, min: 0, max: 20 },
    classStandardDeviation: { type: Number, min: 0 },
    percentile: { type: Number, min: 0, max: 100 },
    rank: { type: Number, min: 1 },
    totalStudents: { type: Number, min: 1 }
  },

  // Amélioration et recommandations
  recommendations: [{
    area: { type: String, required: true, trim: true },
    suggestion: { type: String, required: true, trim: true },
    priority: {
      type: String,
      enum: ['high', 'medium', 'low'],
      default: 'medium'
    },
    resources: [{ type: String, trim: true }]
  }],

  // Accommodations appliquées
  accommodationsUsed: [{
    type: {
      type: String,
      enum: ['Extended Time', 'Alternative Format', 'Assistive Technology', 'Other']
    },
    description: { type: String, trim: true },
    impact: { type: String, trim: true }
  }],

  // Métadonnées de notation
  gradingMetadata: {
    gradedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    gradingMethod: {
      type: String,
      enum: ['manual', 'automated', 'peer_review', 'self_assessment'],
      default: 'manual'
    },
    gradingTime: { type: Number, min: 0 }, // temps en minutes
    confidenceLevel: {
      type: String,
      enum: ['high', 'medium', 'low'],
      default: 'high'
    }
  },

  // Flags et alertes
  flags: [{
    type: {
      type: String,
      enum: ['academic_concern', 'improvement_needed', 'excellent_work', 'plagiarism_suspected', 'late_submission', 'other']
    },
    description: { type: String, trim: true },
    flaggedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    flaggedDate: { type: Date, default: Date.now },
    isResolved: { type: Boolean, default: false },
    resolution: { type: String, trim: true }
  }],

  // Métadonnées
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Index pour améliorer les performances
gradeSchema.index({ student: 1, course: 1, gradeType: 1 });
gradeSchema.index({ student: 1, module: 1 });
gradeSchema.index({ assignment: 1 });
gradeSchema.index({ academicYear: 1, semester: 1 });
gradeSchema.index({ status: 1 });
gradeSchema.index({ isPublished: 1 });

// Virtual pour calculer le pourcentage
gradeSchema.virtual('calculatedPercentage').get(function() {
  if (this.rawScore && this.maxScore) {
    return Math.round((this.rawScore / this.maxScore) * 100);
  }
  return this.percentage || 0;
});

// Virtual pour vérifier si l'étudiant a réussi
gradeSchema.virtual('isPassing').get(function() {
  return this.gradeOutOf20 >= 10; // Assuming 10/20 is passing
});

// Virtual pour obtenir le niveau de performance
gradeSchema.virtual('performanceLevel').get(function() {
  const grade = this.gradeOutOf20;
  if (grade >= 16) return 'Excellent';
  if (grade >= 14) return 'Very Good';
  if (grade >= 12) return 'Good';
  if (grade >= 10) return 'Satisfactory';
  return 'Needs Improvement';
});

// Middleware pour calculer automatiquement les valeurs dérivées
gradeSchema.pre('save', function(next) {
  // Calculer le pourcentage si rawScore et maxScore sont fournis
  if (this.rawScore && this.maxScore) {
    this.percentage = Math.round((this.rawScore / this.maxScore) * 100);
  }

  // Convertir en note sur 20
  if (this.percentage) {
    this.gradeOutOf20 = Math.round((this.percentage / 100) * 20 * 100) / 100;
  }

  // Calculer la note littérale
  if (this.gradeOutOf20) {
    this.letterGrade = this.calculateLetterGrade(this.gradeOutOf20);
    this.gpaPoints = this.calculateGpaPoints(this.gradeOutOf20);
  }

  // Appliquer la pénalité de retard
  if (this.submission.isLate && this.submission.latePenalty > 0) {
    this.gradeOutOf20 = Math.max(0, this.gradeOutOf20 - this.submission.latePenalty);
  }

  next();
});

// Méthode pour calculer la note littérale
gradeSchema.methods.calculateLetterGrade = function(grade) {
  if (grade >= 18) return 'A+';
  if (grade >= 16) return 'A';
  if (grade >= 15) return 'A-';
  if (grade >= 14) return 'B+';
  if (grade >= 12) return 'B';
  if (grade >= 11) return 'B-';
  if (grade >= 10.5) return 'C+';
  if (grade >= 10) return 'C';
  if (grade >= 9) return 'C-';
  if (grade >= 8) return 'D+';
  if (grade >= 7) return 'D';
  return 'F';
};

// Méthode pour calculer les points GPA
gradeSchema.methods.calculateGpaPoints = function(grade) {
  if (grade >= 18) return 4.0;
  if (grade >= 16) return 3.7;
  if (grade >= 15) return 3.3;
  if (grade >= 14) return 3.0;
  if (grade >= 12) return 2.7;
  if (grade >= 11) return 2.3;
  if (grade >= 10.5) return 2.0;
  if (grade >= 10) return 1.7;
  if (grade >= 9) return 1.3;
  if (grade >= 8) return 1.0;
  if (grade >= 7) return 0.7;
  return 0.0;
};

// Méthode pour publier la note
gradeSchema.methods.publish = function(publishedBy) {
  this.status = 'published';
  this.isPublished = true;
  this.publishedDate = new Date();
  this.updatedBy = publishedBy;
  return this.save();
};

// Méthode pour contester une note
gradeSchema.methods.disputeGrade = function(reason, studentId) {
  this.dispute = {
    isDisputed: true,
    disputeReason: reason,
    disputeDate: new Date(),
    disputeStatus: 'pending'
  };
  this.status = 'disputed';
  return this.save();
};

// Méthode pour réviser une note
gradeSchema.methods.revise = function(newGrade, reason, revisedBy, approvedBy) {
  this.revisions.push({
    previousGrade: this.gradeOutOf20,
    newGrade: newGrade,
    reason: reason,
    revisedBy: revisedBy,
    approvedBy: approvedBy
  });

  this.gradeOutOf20 = newGrade;
  this.status = 'revised';
  this.updatedBy = revisedBy;

  return this.save();
};

// Méthode pour ajouter un flag
gradeSchema.methods.addFlag = function(type, description, flaggedBy) {
  this.flags.push({
    type: type,
    description: description,
    flaggedBy: flaggedBy
  });
  return this.save();
};

// Méthode pour calculer les statistiques de classe
gradeSchema.statics.calculateClassStatistics = function(courseId, gradeType) {
  return this.aggregate([
    { $match: { course: courseId, gradeType: gradeType, isPublished: true } },
    {
      $group: {
        _id: null,
        average: { $avg: '$gradeOutOf20' },
        median: { $median: '$gradeOutOf20' },
        standardDeviation: { $stdDevPop: '$gradeOutOf20' },
        count: { $sum: 1 },
        grades: { $push: '$gradeOutOf20' }
      }
    }
  ]);
};

module.exports = mongoose.model('Grade', gradeSchema);
