const mongoose = require('mongoose');

const interviewSchema = new mongoose.Schema({
  // Référence à la candidature
  application: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Application',
    required: true
  },

  // Informations de l'entretien
  scheduledDate: {
    type: Date,
    required: true
  },
  duration: {
    type: Number, // en minutes
    default: 60
  },
  location: {
    type: String,
    enum: ['in_person', 'video_call', 'phone'],
    default: 'video_call'
  },
  meetingLink: {
    type: String, // Lien Zoom, Teams, etc.
    trim: true
  },
  meetingRoom: {
    type: String, // Salle physique
    trim: true
  },

  // Interviewers
  interviewers: [{
    interviewer: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    role: {
      type: String,
      enum: ['primary', 'secondary', 'observer'],
      default: 'primary'
    }
  }],

  // Statut de l'entretien
  status: {
    type: String,
    enum: ['scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show'],
    default: 'scheduled'
  },

  // Notifications
  notifications: {
    candidateNotified: { type: Boolean, default: false },
    candidateNotifiedAt: { type: Date },
    reminderSent: { type: Boolean, default: false },
    reminderSentAt: { type: Date }
  },

  // Évaluation de l'entretien
  evaluation: {
    completed: { type: Boolean, default: false },
    completedAt: { type: Date },
    completedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },

    // Grille d'évaluation
    scores: {
      communication: {
        score: { type: Number, min: 1, max: 10 },
        comments: { type: String, trim: true }
      },
      leadership: {
        score: { type: Number, min: 1, max: 10 },
        comments: { type: String, trim: true }
      },
      motivation: {
        score: { type: Number, min: 1, max: 10 },
        comments: { type: String, trim: true }
      },
      experience: {
        score: { type: Number, min: 1, max: 10 },
        comments: { type: String, trim: true }
      },
      fitForProgram: {
        score: { type: Number, min: 1, max: 10 },
        comments: { type: String, trim: true }
      },
      overallImpression: {
        score: { type: Number, min: 1, max: 10 },
        comments: { type: String, trim: true }
      }
    },

    // Score total
    totalScore: { type: Number, min: 6, max: 60 },
    averageScore: { type: Number, min: 1, max: 10 },

    // Recommandation finale
    recommendation: {
      type: String,
      enum: ['strongly_recommend', 'recommend', 'neutral', 'not_recommend', 'strongly_not_recommend']
    },

    // Points forts et préoccupations
    strengths: [{ type: String, trim: true }],
    concerns: [{ type: String, trim: true }],

    // Commentaires généraux
    generalComments: { type: String, trim: true },

    // Questions spécifiques posées
    questionsAsked: [{ type: String, trim: true }],

    // Réponses du candidat (résumé)
    candidateResponses: { type: String, trim: true }
  },

  // Notes de l'entretien
  notes: { type: String, trim: true },

  // Métadonnées
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Index pour améliorer les performances
interviewSchema.index({ application: 1 });
interviewSchema.index({ scheduledDate: 1 });
interviewSchema.index({ status: 1 });
interviewSchema.index({ 'interviewers.interviewer': 1 });

// Virtual pour le candidat
interviewSchema.virtual('candidate', {
  ref: 'Application',
  localField: 'application',
  foreignField: '_id',
  justOne: true
});

// Méthode pour calculer le score total
interviewSchema.methods.calculateTotalScore = function() {
  const scores = this.evaluation.scores;
  let total = 0;
  let count = 0;

  Object.keys(scores).forEach(key => {
    if (scores[key].score) {
      total += scores[key].score;
      count++;
    }
  });

  this.evaluation.totalScore = total;
  this.evaluation.averageScore = count > 0 ? total / count : 0;
  return this.evaluation.averageScore;
};

// Méthode pour marquer l'entretien comme terminé
interviewSchema.methods.complete = function(evaluatedBy) {
  this.status = 'completed';
  this.evaluation.completed = true;
  this.evaluation.completedAt = new Date();
  this.evaluation.completedBy = evaluatedBy;
  this.calculateTotalScore();
  return this.save();
};

// Méthode pour envoyer une notification au candidat
interviewSchema.methods.notifyCandidate = function() {
  this.notifications.candidateNotified = true;
  this.notifications.candidateNotifiedAt = new Date();
  return this.save();
};

// Middleware pour mettre à jour updatedBy
interviewSchema.pre('save', function(next) {
  if (this.isModified() && !this.isNew) {
    this.updatedAt = new Date();
  }
  next();
});

module.exports = mongoose.model('Interview', interviewSchema);
