const mongoose = require('mongoose');

const notificationSchema = new mongoose.Schema({
  // Destinataire
  recipient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  // Expéditeur
  sender: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  // Type et catégorie
  type: {
    type: String,
    enum: [
      'assignment_due', 'grade_posted', 'course_update', 'schedule_change',
      'payment_due', 'payment_received', 'attendance_warning', 'enrollment_confirmation',
      'system_maintenance', 'announcement', 'reminder', 'alert', 'welcome', 'other'
    ],
    required: true
  },
  category: {
    type: String,
    enum: ['academic', 'financial', 'administrative', 'system', 'personal'],
    required: true
  },
  
  // Priorité
  priority: {
    type: String,
    enum: ['low', 'normal', 'high', 'urgent'],
    default: 'normal'
  },
  
  // Contenu
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  message: {
    type: String,
    required: true,
    trim: true,
    maxlength: 2000
  },
  shortMessage: {
    type: String,
    trim: true,
    maxlength: 100
  },
  
  // Contenu riche
  richContent: {
    html: { type: String, trim: true },
    attachments: [{
      filename: { type: String, required: true },
      originalName: { type: String, required: true },
      path: { type: String, required: true },
      size: { type: Number },
      mimeType: { type: String }
    }],
    links: [{
      text: { type: String, required: true, trim: true },
      url: { type: String, required: true, trim: true },
      isExternal: { type: Boolean, default: false }
    }],
    buttons: [{
      text: { type: String, required: true, trim: true },
      action: { type: String, required: true, trim: true },
      style: {
        type: String,
        enum: ['primary', 'secondary', 'success', 'warning', 'danger'],
        default: 'primary'
      }
    }]
  },
  
  // Références contextuelles
  relatedTo: {
    entityType: {
      type: String,
      enum: ['course', 'assignment', 'grade', 'payment', 'schedule', 'enrollment', 'user']
    },
    entityId: { type: mongoose.Schema.Types.ObjectId },
    entityTitle: { type: String, trim: true }
  },
  
  // Canaux de diffusion
  channels: {
    email: {
      enabled: { type: Boolean, default: true },
      sent: { type: Boolean, default: false },
      sentAt: { type: Date },
      deliveryStatus: {
        type: String,
        enum: ['pending', 'sent', 'delivered', 'failed', 'bounced']
      },
      errorMessage: { type: String, trim: true }
    },
    sms: {
      enabled: { type: Boolean, default: false },
      sent: { type: Boolean, default: false },
      sentAt: { type: Date },
      deliveryStatus: {
        type: String,
        enum: ['pending', 'sent', 'delivered', 'failed']
      },
      errorMessage: { type: String, trim: true }
    },
    push: {
      enabled: { type: Boolean, default: true },
      sent: { type: Boolean, default: false },
      sentAt: { type: Date },
      deliveryStatus: {
        type: String,
        enum: ['pending', 'sent', 'delivered', 'failed']
      },
      errorMessage: { type: String, trim: true }
    },
    inApp: {
      enabled: { type: Boolean, default: true },
      displayed: { type: Boolean, default: false },
      displayedAt: { type: Date }
    }
  },
  
  // Statut et lecture
  status: {
    type: String,
    enum: ['draft', 'scheduled', 'sent', 'delivered', 'read', 'archived', 'failed'],
    default: 'draft'
  },
  isRead: {
    type: Boolean,
    default: false
  },
  readAt: {
    type: Date
  },
  
  // Planification
  scheduling: {
    isScheduled: { type: Boolean, default: false },
    scheduledFor: { type: Date },
    timezone: { type: String, default: 'Africa/Tunis' },
    recurring: {
      isRecurring: { type: Boolean, default: false },
      pattern: {
        type: String,
        enum: ['daily', 'weekly', 'monthly', 'custom']
      },
      frequency: { type: Number, min: 1, default: 1 },
      endDate: { type: Date },
      nextOccurrence: { type: Date }
    }
  },
  
  // Actions utilisateur
  actions: [{
    actionType: {
      type: String,
      enum: ['clicked', 'dismissed', 'archived', 'forwarded', 'replied']
    },
    timestamp: { type: Date, default: Date.now },
    details: { type: String, trim: true }
  }],
  
  // Groupement et threading
  thread: {
    threadId: { type: String, trim: true },
    isThreadStarter: { type: Boolean, default: true },
    parentNotification: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Notification'
    },
    replies: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Notification'
    }]
  },
  
  // Ciblage et segmentation
  targeting: {
    audience: {
      type: String,
      enum: ['all', 'students', 'professors', 'staff', 'admins', 'custom'],
      default: 'custom'
    },
    filters: {
      roles: [{ type: String }],
      courses: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Course' }],
      cohorts: [{ type: String }],
      academicYear: { type: String },
      customCriteria: { type: mongoose.Schema.Types.Mixed }
    }
  },
  
  // Métriques et analytics
  metrics: {
    deliveryAttempts: { type: Number, default: 0 },
    openRate: { type: Number, min: 0, max: 100, default: 0 },
    clickRate: { type: Number, min: 0, max: 100, default: 0 },
    responseRate: { type: Number, min: 0, max: 100, default: 0 },
    engagementScore: { type: Number, min: 0, max: 100, default: 0 }
  },
  
  // Expiration et archivage
  expiration: {
    expiresAt: { type: Date },
    autoArchive: { type: Boolean, default: false },
    archiveAfterDays: { type: Number, min: 1, default: 30 }
  },
  
  // Localisation
  localization: {
    language: {
      type: String,
      enum: ['fr', 'en', 'ar'],
      default: 'fr'
    },
    translations: [{
      language: {
        type: String,
        enum: ['fr', 'en', 'ar']
      },
      title: { type: String, trim: true },
      message: { type: String, trim: true },
      shortMessage: { type: String, trim: true }
    }]
  },
  
  // Métadonnées système
  system: {
    source: {
      type: String,
      enum: ['manual', 'automated', 'scheduled', 'triggered'],
      default: 'manual'
    },
    trigger: { type: String, trim: true },
    batchId: { type: String, trim: true },
    campaignId: { type: String, trim: true },
    version: { type: Number, default: 1 }
  },
  
  // Métadonnées
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Index pour améliorer les performances
notificationSchema.index({ recipient: 1, createdAt: -1 });
notificationSchema.index({ status: 1 });
notificationSchema.index({ type: 1, category: 1 });
notificationSchema.index({ isRead: 1 });
notificationSchema.index({ 'scheduling.scheduledFor': 1 });
notificationSchema.index({ 'expiration.expiresAt': 1 });

// Virtual pour vérifier si la notification est expirée
notificationSchema.virtual('isExpired').get(function() {
  return this.expiration.expiresAt && new Date() > this.expiration.expiresAt;
});

// Virtual pour vérifier si la notification est en attente d'envoi
notificationSchema.virtual('isPendingDelivery').get(function() {
  return this.scheduling.isScheduled && 
         this.scheduling.scheduledFor && 
         new Date() >= this.scheduling.scheduledFor &&
         this.status === 'scheduled';
});

// Virtual pour calculer le taux de livraison
notificationSchema.virtual('deliveryRate').get(function() {
  const totalChannels = Object.keys(this.channels).filter(channel => 
    this.channels[channel].enabled
  ).length;
  
  if (totalChannels === 0) return 0;
  
  const deliveredChannels = Object.keys(this.channels).filter(channel => 
    this.channels[channel].enabled && 
    ['delivered', 'sent'].includes(this.channels[channel].deliveryStatus)
  ).length;
  
  return Math.round((deliveredChannels / totalChannels) * 100);
});

// Middleware pour gérer l'expiration automatique
notificationSchema.pre('save', function(next) {
  if (this.expiration.autoArchive && !this.expiration.expiresAt) {
    const expirationDate = new Date();
    expirationDate.setDate(expirationDate.getDate() + this.expiration.archiveAfterDays);
    this.expiration.expiresAt = expirationDate;
  }
  next();
});

// Méthode pour marquer comme lu
notificationSchema.methods.markAsRead = function() {
  this.isRead = true;
  this.readAt = new Date();
  this.status = 'read';
  
  // Enregistrer l'action
  this.actions.push({
    actionType: 'clicked',
    timestamp: new Date()
  });
  
  return this.save();
};

// Méthode pour envoyer la notification
notificationSchema.methods.send = function() {
  const promises = [];
  
  // Envoyer par email si activé
  if (this.channels.email.enabled) {
    promises.push(this.sendEmail());
  }
  
  // Envoyer par SMS si activé
  if (this.channels.sms.enabled) {
    promises.push(this.sendSMS());
  }
  
  // Envoyer notification push si activé
  if (this.channels.push.enabled) {
    promises.push(this.sendPush());
  }
  
  // Marquer comme affiché dans l'app
  if (this.channels.inApp.enabled) {
    this.channels.inApp.displayed = true;
    this.channels.inApp.displayedAt = new Date();
  }
  
  this.status = 'sent';
  this.metrics.deliveryAttempts += 1;
  
  return Promise.all(promises).then(() => this.save());
};

// Méthode pour envoyer par email (à implémenter avec un service email)
notificationSchema.methods.sendEmail = function() {
  return new Promise((resolve, reject) => {
    // Logique d'envoi d'email
    // Cette partie devrait être implémentée avec un service comme SendGrid, Mailgun, etc.
    
    this.channels.email.sent = true;
    this.channels.email.sentAt = new Date();
    this.channels.email.deliveryStatus = 'sent';
    
    console.log(`Email sent to ${this.recipient}: ${this.title}`);
    resolve();
  });
};

// Méthode pour envoyer par SMS (à implémenter avec un service SMS)
notificationSchema.methods.sendSMS = function() {
  return new Promise((resolve, reject) => {
    // Logique d'envoi de SMS
    // Cette partie devrait être implémentée avec un service comme Twilio, etc.
    
    this.channels.sms.sent = true;
    this.channels.sms.sentAt = new Date();
    this.channels.sms.deliveryStatus = 'sent';
    
    console.log(`SMS sent to ${this.recipient}: ${this.shortMessage || this.title}`);
    resolve();
  });
};

// Méthode pour envoyer notification push (à implémenter avec un service push)
notificationSchema.methods.sendPush = function() {
  return new Promise((resolve, reject) => {
    // Logique d'envoi de notification push
    // Cette partie devrait être implémentée avec Firebase, OneSignal, etc.
    
    this.channels.push.sent = true;
    this.channels.push.sentAt = new Date();
    this.channels.push.deliveryStatus = 'sent';
    
    console.log(`Push notification sent to ${this.recipient}: ${this.title}`);
    resolve();
  });
};

// Méthode pour programmer une notification
notificationSchema.methods.schedule = function(scheduledFor, timezone = 'Africa/Tunis') {
  this.scheduling.isScheduled = true;
  this.scheduling.scheduledFor = scheduledFor;
  this.scheduling.timezone = timezone;
  this.status = 'scheduled';
  
  return this.save();
};

// Méthode pour archiver
notificationSchema.methods.archive = function() {
  this.status = 'archived';
  return this.save();
};

// Méthode statique pour obtenir les notifications non lues d'un utilisateur
notificationSchema.statics.getUnreadForUser = function(userId, limit = 50) {
  return this.find({
    recipient: userId,
    isRead: false,
    status: { $in: ['sent', 'delivered'] },
    $or: [
      { 'expiration.expiresAt': { $exists: false } },
      { 'expiration.expiresAt': { $gt: new Date() } }
    ]
  })
  .sort({ createdAt: -1 })
  .limit(limit)
  .populate('sender', 'firstName lastName')
  .populate('relatedTo.entityId');
};

// Méthode statique pour obtenir les notifications programmées à envoyer
notificationSchema.statics.getPendingScheduled = function() {
  return this.find({
    status: 'scheduled',
    'scheduling.isScheduled': true,
    'scheduling.scheduledFor': { $lte: new Date() }
  });
};

// Méthode statique pour nettoyer les notifications expirées
notificationSchema.statics.cleanupExpired = function() {
  return this.updateMany(
    {
      'expiration.expiresAt': { $lt: new Date() },
      status: { $ne: 'archived' }
    },
    {
      $set: { status: 'archived' }
    }
  );
};

// Méthode statique pour créer une notification en masse
notificationSchema.statics.createBulkNotification = function(recipients, notificationData) {
  const notifications = recipients.map(recipientId => ({
    ...notificationData,
    recipient: recipientId,
    system: {
      ...notificationData.system,
      source: 'automated',
      batchId: new mongoose.Types.ObjectId().toString()
    }
  }));
  
  return this.insertMany(notifications);
};

// Méthode pour calculer les métriques d'engagement
notificationSchema.methods.calculateEngagementMetrics = function() {
  const totalActions = this.actions.length;
  const clickActions = this.actions.filter(action => action.actionType === 'clicked').length;
  
  this.metrics.clickRate = totalActions > 0 ? Math.round((clickActions / totalActions) * 100) : 0;
  this.metrics.engagementScore = this.isRead ? 
    Math.min(100, this.metrics.clickRate + (this.isRead ? 50 : 0)) : 0;
  
  return this.save();
};

module.exports = mongoose.model('Notification', notificationSchema);
