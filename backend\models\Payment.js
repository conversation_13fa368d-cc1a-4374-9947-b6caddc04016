const mongoose = require('mongoose');

const paymentSchema = new mongoose.Schema({
  // Références principales
  student: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Student',
    required: true
  },
  enrollment: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Enrollment'
  },
  
  // Numéro de transaction unique
  transactionNumber: {
    type: String,
    unique: true,
    trim: true
  },
  
  // Informations de paiement
  amount: {
    type: Number,
    required: true,
    min: 0
  },
  currency: {
    type: String,
    required: true,
    default: 'TND',
    enum: ['TND', 'USD', 'EUR']
  },
  
  // Type et catégorie de paiement
  paymentType: {
    type: String,
    enum: ['tuition', 'registration', 'exam_fee', 'material_fee', 'late_fee', 'penalty', 'refund', 'scholarship', 'other'],
    required: true
  },
  paymentCategory: {
    type: String,
    enum: ['academic', 'administrative', 'penalty', 'refund'],
    default: 'academic'
  },
  
  // Période académique
  academicYear: {
    type: String,
    required: true,
    match: [/^\d{4}-\d{4}$/, 'Format: YYYY-YYYY']
  },
  semester: {
    type: String,
    enum: ['Fall', 'Spring', 'Summer', 'Year-long'],
    required: true
  },
  
  // Dates importantes
  dueDate: {
    type: Date,
    required: true
  },
  paymentDate: {
    type: Date
  },
  processedDate: {
    type: Date
  },
  
  // Statut du paiement
  status: {
    type: String,
    enum: ['pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded', 'disputed'],
    default: 'pending'
  },
  
  // Méthode de paiement
  paymentMethod: {
    type: String,
    enum: ['cash', 'bank_transfer', 'credit_card', 'debit_card', 'check', 'online', 'mobile_payment', 'installment'],
    required: true
  },
  
  // Détails de la méthode de paiement
  paymentDetails: {
    // Pour les cartes
    cardType: {
      type: String,
      enum: ['visa', 'mastercard', 'amex', 'other']
    },
    lastFourDigits: { type: String, match: /^\d{4}$/ },
    
    // Pour les virements bancaires
    bankName: { type: String, trim: true },
    accountNumber: { type: String, trim: true },
    routingNumber: { type: String, trim: true },
    
    // Pour les chèques
    checkNumber: { type: String, trim: true },
    checkDate: { type: Date },
    
    // Référence externe
    externalTransactionId: { type: String, trim: true },
    gatewayResponse: { type: String, trim: true }
  },
  
  // Plan de paiement (pour les paiements échelonnés)
  installmentPlan: {
    isInstallment: { type: Boolean, default: false },
    totalInstallments: { type: Number, min: 1 },
    currentInstallment: { type: Number, min: 1 },
    installmentAmount: { type: Number, min: 0 },
    nextDueDate: { type: Date },
    remainingAmount: { type: Number, min: 0, default: 0 }
  },
  
  // Frais et taxes
  fees: {
    processingFee: { type: Number, min: 0, default: 0 },
    lateFee: { type: Number, min: 0, default: 0 },
    penaltyFee: { type: Number, min: 0, default: 0 },
    taxAmount: { type: Number, min: 0, default: 0 },
    discountAmount: { type: Number, min: 0, default: 0 },
    netAmount: { type: Number, min: 0 }
  },
  
  // Remises et bourses
  discounts: [{
    type: {
      type: String,
      enum: ['early_payment', 'scholarship', 'employee_discount', 'alumni_discount', 'merit_based', 'need_based', 'other']
    },
    amount: { type: Number, min: 0 },
    percentage: { type: Number, min: 0, max: 100 },
    description: { type: String, trim: true },
    approvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  }],
  
  // Informations de reçu
  receipt: {
    receiptNumber: { type: String, unique: true, sparse: true },
    issuedDate: { type: Date },
    issuedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    receiptPath: { type: String, trim: true },
    emailSent: { type: Boolean, default: false },
    emailSentDate: { type: Date }
  },
  
  // Historique des tentatives de paiement
  attempts: [{
    attemptDate: { type: Date, default: Date.now },
    amount: { type: Number, required: true },
    method: { type: String, required: true },
    status: {
      type: String,
      enum: ['success', 'failed', 'cancelled'],
      required: true
    },
    errorCode: { type: String, trim: true },
    errorMessage: { type: String, trim: true },
    gatewayResponse: { type: String, trim: true }
  }],
  
  // Remboursement
  refund: {
    isRefunded: { type: Boolean, default: false },
    refundAmount: { type: Number, min: 0, default: 0 },
    refundReason: { type: String, trim: true },
    refundDate: { type: Date },
    refundMethod: {
      type: String,
      enum: ['original_method', 'bank_transfer', 'check', 'cash']
    },
    refundTransactionId: { type: String, trim: true },
    processedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  },
  
  // Dispute et contestation
  dispute: {
    isDisputed: { type: Boolean, default: false },
    disputeReason: { type: String, trim: true },
    disputeDate: { type: Date },
    disputeStatus: {
      type: String,
      enum: ['open', 'investigating', 'resolved', 'closed']
    },
    resolution: { type: String, trim: true },
    resolvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    resolvedDate: { type: Date }
  },
  
  // Notifications et rappels
  notifications: {
    remindersSent: { type: Number, default: 0 },
    lastReminderDate: { type: Date },
    overdueNotificationSent: { type: Boolean, default: false },
    overdueNotificationDate: { type: Date },
    confirmationSent: { type: Boolean, default: false },
    confirmationSentDate: { type: Date }
  },
  
  // Informations de traitement
  processing: {
    processedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    processingNotes: { type: String, trim: true },
    verificationRequired: { type: Boolean, default: false },
    verifiedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    verificationDate: { type: Date },
    approvalRequired: { type: Boolean, default: false },
    approvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    approvalDate: { type: Date }
  },
  
  // Réconciliation comptable
  accounting: {
    accountCode: { type: String, trim: true },
    journalEntry: { type: String, trim: true },
    reconciledDate: { type: Date },
    reconciledBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    fiscalYear: { type: String, match: /^\d{4}$/ },
    taxReported: { type: Boolean, default: false }
  },
  
  // Métadonnées
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  // Informations système
  ipAddress: { type: String, trim: true },
  userAgent: { type: String, trim: true },
  sessionId: { type: String, trim: true }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Index pour améliorer les performances
paymentSchema.index({ student: 1, paymentDate: -1 });
paymentSchema.index({ transactionNumber: 1 });
paymentSchema.index({ status: 1 });
paymentSchema.index({ dueDate: 1 });
paymentSchema.index({ academicYear: 1, semester: 1 });
paymentSchema.index({ paymentType: 1 });

// Virtual pour vérifier si le paiement est en retard
paymentSchema.virtual('isOverdue').get(function() {
  return this.status === 'pending' && new Date() > this.dueDate;
});

// Virtual pour calculer les jours de retard
paymentSchema.virtual('daysOverdue').get(function() {
  if (!this.isOverdue) return 0;
  return Math.ceil((new Date() - this.dueDate) / (1000 * 60 * 60 * 24));
});

// Virtual pour calculer le montant net
paymentSchema.virtual('calculatedNetAmount').get(function() {
  const grossAmount = this.amount;
  const totalFees = (this.fees.processingFee || 0) + (this.fees.lateFee || 0) + 
                   (this.fees.penaltyFee || 0) + (this.fees.taxAmount || 0);
  const totalDiscounts = this.fees.discountAmount || 0;
  
  return grossAmount + totalFees - totalDiscounts;
});

// Middleware pour générer automatiquement le numéro de transaction
paymentSchema.pre('save', async function(next) {
  if (!this.transactionNumber) {
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, '0');
    const count = await this.constructor.countDocuments({
      transactionNumber: new RegExp(`^PAY${year}${month}`)
    });
    this.transactionNumber = `PAY${year}${month}${String(count + 1).padStart(6, '0')}`;
  }
  
  // Calculer le montant net
  this.fees.netAmount = this.calculatedNetAmount;
  
  next();
});

// Middleware pour générer le numéro de reçu
paymentSchema.pre('save', function(next) {
  if (this.status === 'completed' && !this.receipt.receiptNumber) {
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, '0');
    this.receipt.receiptNumber = `REC${year}${month}${Date.now().toString().slice(-6)}`;
    this.receipt.issuedDate = new Date();
  }
  
  next();
});

// Méthode pour traiter le paiement
paymentSchema.methods.processPayment = function(paymentData, processedBy) {
  this.attempts.push({
    amount: paymentData.amount,
    method: paymentData.method,
    status: 'success',
    gatewayResponse: paymentData.gatewayResponse
  });
  
  this.status = 'completed';
  this.paymentDate = new Date();
  this.processedDate = new Date();
  this.processing.processedBy = processedBy;
  this.paymentDetails = { ...this.paymentDetails, ...paymentData.details };
  
  return this.save();
};

// Méthode pour échouer le paiement
paymentSchema.methods.failPayment = function(errorCode, errorMessage) {
  this.attempts.push({
    amount: this.amount,
    method: this.paymentMethod,
    status: 'failed',
    errorCode: errorCode,
    errorMessage: errorMessage
  });
  
  this.status = 'failed';
  return this.save();
};

// Méthode pour rembourser
paymentSchema.methods.processRefund = function(refundAmount, reason, processedBy) {
  this.refund = {
    isRefunded: true,
    refundAmount: refundAmount,
    refundReason: reason,
    refundDate: new Date(),
    refundMethod: 'original_method',
    processedBy: processedBy
  };
  
  this.status = 'refunded';
  return this.save();
};

// Méthode pour appliquer une remise
paymentSchema.methods.applyDiscount = function(discountData, approvedBy) {
  this.discounts.push({
    ...discountData,
    approvedBy: approvedBy
  });
  
  // Recalculer le montant avec remise
  const totalDiscountAmount = this.discounts.reduce((sum, discount) => {
    return sum + (discount.amount || (this.amount * discount.percentage / 100));
  }, 0);
  
  this.fees.discountAmount = totalDiscountAmount;
  this.fees.netAmount = this.calculatedNetAmount;
  
  return this.save();
};

// Méthode pour envoyer un rappel
paymentSchema.methods.sendReminder = function() {
  this.notifications.remindersSent += 1;
  this.notifications.lastReminderDate = new Date();
  
  // Logique pour envoyer la notification
  // Cette partie devrait être implémentée avec un service de notification
  
  return this.save();
};

// Méthode pour marquer comme en retard
paymentSchema.methods.markOverdue = function() {
  if (this.isOverdue && !this.notifications.overdueNotificationSent) {
    this.notifications.overdueNotificationSent = true;
    this.notifications.overdueNotificationDate = new Date();
    
    // Appliquer des frais de retard si configuré
    if (this.daysOverdue > 7) {
      this.fees.lateFee = Math.min(this.amount * 0.05, 100); // 5% ou 100 TND max
    }
    
    return this.save();
  }
  return Promise.resolve(this);
};

// Méthode statique pour calculer les statistiques de paiement
paymentSchema.statics.calculatePaymentStatistics = function(filters = {}) {
  const matchConditions = {};
  
  if (filters.studentId) matchConditions.student = filters.studentId;
  if (filters.academicYear) matchConditions.academicYear = filters.academicYear;
  if (filters.semester) matchConditions.semester = filters.semester;
  if (filters.paymentType) matchConditions.paymentType = filters.paymentType;
  if (filters.startDate || filters.endDate) {
    matchConditions.paymentDate = {};
    if (filters.startDate) matchConditions.paymentDate.$gte = filters.startDate;
    if (filters.endDate) matchConditions.paymentDate.$lte = filters.endDate;
  }
  
  return this.aggregate([
    { $match: matchConditions },
    {
      $group: {
        _id: null,
        totalPayments: { $sum: 1 },
        totalAmount: { $sum: '$amount' },
        completedPayments: {
          $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
        },
        pendingPayments: {
          $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] }
        },
        overduePayments: {
          $sum: { 
            $cond: [
              { 
                $and: [
                  { $eq: ['$status', 'pending'] },
                  { $lt: ['$dueDate', new Date()] }
                ]
              }, 
              1, 
              0
            ]
          }
        },
        averagePaymentAmount: { $avg: '$amount' },
        totalRefunds: { $sum: '$refund.refundAmount' }
      }
    },
    {
      $addFields: {
        completionRate: {
          $multiply: [
            { $divide: ['$completedPayments', '$totalPayments'] },
            100
          ]
        }
      }
    }
  ]);
};

// Méthode pour générer un rapport de paiements en retard
paymentSchema.statics.getOverduePayments = function(daysOverdue = 0) {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - daysOverdue);
  
  return this.find({
    status: 'pending',
    dueDate: { $lt: cutoffDate }
  })
  .populate('student', 'user studentNumber')
  .populate('student.user', 'firstName lastName email phone')
  .sort({ dueDate: 1 });
};

module.exports = mongoose.model('Payment', paymentSchema);
