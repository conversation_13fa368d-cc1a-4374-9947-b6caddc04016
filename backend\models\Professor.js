const mongoose = require('mongoose');

const professorSchema = new mongoose.Schema({
  // Référence vers l'utilisateur
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  
  // Numéro d'employé unique
  employeeNumber: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  
  // Informations académiques
  academicTitle: {
    type: String,
    enum: ['Dr.', 'Prof.', 'Mr.', 'Ms.', 'Mrs.'],
    required: true
  },
  academicRank: {
    type: String,
    enum: ['Assistant Professor', 'Associate Professor', 'Full Professor', 'Lecturer', 'Senior Lecturer', 'Visiting Professor', 'Adjunct Professor'],
    required: true
  },
  department: {
    type: String,
    required: false,
    trim: true,
    enum: ['Management', 'Finance', 'Marketing', 'Operations', 'Strategy', 'Leadership', 'Economics', 'Accounting', 'Information Systems', 'Human Resources']
  },
  
  // Formation et qualifications
  education: [{
    degree: {
      type: String,
      required: true,
      enum: ['Bachelor', 'Master', 'MBA', 'PhD', 'DBA', 'Other']
    },
    fieldOfStudy: { type: String, required: true, trim: true },
    institution: { type: String, required: true, trim: true },
    graduationYear: { type: Number, min: 1950, max: new Date().getFullYear() },
    country: { type: String, trim: true },
    gpa: { type: Number, min: 0, max: 4 },
    thesis: {
      title: { type: String, trim: true },
      supervisor: { type: String, trim: true }
    }
  }],
  
  // Expérience professionnelle
  professionalExperience: [{
    company: { type: String, required: true, trim: true },
    position: { type: String, required: true, trim: true },
    industry: { type: String, trim: true },
    startDate: { type: Date, required: true },
    endDate: { type: Date },
    isCurrent: { type: Boolean, default: false },
    description: { type: String, trim: true },
    achievements: [{ type: String, trim: true }]
  }],
  
  // Expérience d'enseignement
  teachingExperience: {
    yearsOfTeaching: { type: Number, min: 0, default: 0 },
    previousInstitutions: [{
      name: { type: String, trim: true },
      position: { type: String, trim: true },
      startDate: { type: Date },
      endDate: { type: Date },
      coursesTeught: [{ type: String, trim: true }]
    }],
    teachingAreas: [{ type: String, trim: true }],
    preferredTeachingMethods: [{
      type: String,
      enum: ['Lecture', 'Case Study', 'Workshop', 'Seminar', 'Online', 'Blended', 'Project-based', 'Experiential']
    }]
  },
  
  // Recherche et publications
  research: {
    researchInterests: [{ type: String, trim: true }],
    currentProjects: [{
      title: { type: String, trim: true },
      description: { type: String, trim: true },
      startDate: { type: Date },
      expectedEndDate: { type: Date },
      fundingSource: { type: String, trim: true },
      collaborators: [{ type: String, trim: true }]
    }],
    publications: [{
      type: {
        type: String,
        enum: ['Journal Article', 'Book', 'Book Chapter', 'Conference Paper', 'Working Paper', 'Case Study', 'Other']
      },
      title: { type: String, required: true, trim: true },
      authors: [{ type: String, trim: true }],
      journal: { type: String, trim: true },
      publisher: { type: String, trim: true },
      publicationDate: { type: Date },
      volume: { type: String, trim: true },
      issue: { type: String, trim: true },
      pages: { type: String, trim: true },
      doi: { type: String, trim: true },
      isbn: { type: String, trim: true },
      url: { type: String, trim: true },
      abstract: { type: String, trim: true }
    }]
  },
  
  // Statut d'emploi
  employmentStatus: {
    type: String,
    enum: ['full_time', 'part_time', 'visiting', 'adjunct', 'emeritus', 'sabbatical', 'inactive'],
    default: 'full_time'
  },
  contractType: {
    type: String,
    enum: ['permanent', 'temporary', 'contract', 'consultant'],
    default: 'permanent'
  },
  startDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  endDate: {
    type: Date
  },
  
  // Informations de rémunération
  salary: {
    amount: { type: Number, min: 0 },
    currency: { type: String, default: 'TND' },
    paymentFrequency: {
      type: String,
      enum: ['monthly', 'annual', 'per_course', 'hourly'],
      default: 'monthly'
    }
  },
  
  // Disponibilité et horaires
  availability: {
    preferredDays: [{
      type: String,
      enum: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
    }],
    preferredTimeSlots: [{
      start: { type: String, match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/ },
      end: { type: String, match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/ }
    }],
    maxHoursPerWeek: { type: Number, min: 1, max: 40, default: 20 },
    canTravelForTeaching: { type: Boolean, default: false }
  },
  
  // Compétences et spécialisations
  skills: {
    technicalSkills: [{ type: String, trim: true }],
    languages: [{
      language: { type: String, required: true, trim: true },
      proficiency: {
        type: String,
        enum: ['Beginner', 'Intermediate', 'Advanced', 'Native'],
        required: true
      }
    }],
    certifications: [{
      name: { type: String, required: true, trim: true },
      issuingOrganization: { type: String, required: true, trim: true },
      issueDate: { type: Date },
      expirationDate: { type: Date },
      credentialId: { type: String, trim: true }
    }]
  },
  
  // Évaluations et feedback
  evaluations: [{
    evaluator: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    evaluationType: {
      type: String,
      enum: ['student_feedback', 'peer_review', 'administrative_review', 'self_assessment']
    },
    course: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Course'
    },
    academicYear: { type: String },
    ratings: {
      teachingEffectiveness: { type: Number, min: 1, max: 5 },
      courseContent: { type: Number, min: 1, max: 5 },
      communication: { type: Number, min: 1, max: 5 },
      availability: { type: Number, min: 1, max: 5 },
      overallRating: { type: Number, min: 1, max: 5 }
    },
    comments: { type: String, trim: true },
    evaluationDate: { type: Date, default: Date.now }
  }],
  
  // Activités de service
  serviceActivities: [{
    type: {
      type: String,
      enum: ['Committee Member', 'Committee Chair', 'Editorial Board', 'Reviewer', 'Conference Organizer', 'External Examiner', 'Consultant']
    },
    organization: { type: String, trim: true },
    role: { type: String, trim: true },
    description: { type: String, trim: true },
    startDate: { type: Date },
    endDate: { type: Date },
    isCurrent: { type: Boolean, default: false }
  }],
  
  // Récompenses et reconnaissances
  awards: [{
    title: { type: String, required: true, trim: true },
    awardingOrganization: { type: String, required: true, trim: true },
    dateReceived: { type: Date },
    description: { type: String, trim: true },
    category: {
      type: String,
      enum: ['Teaching Excellence', 'Research Excellence', 'Service Excellence', 'Lifetime Achievement', 'Other']
    }
  }],
  
  // Informations de contact professionnel
  officeLocation: {
    building: { type: String, trim: true },
    room: { type: String, trim: true },
    floor: { type: String, trim: true }
  },
  officeHours: [{
    day: {
      type: String,
      enum: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
    },
    startTime: { type: String, match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/ },
    endTime: { type: String, match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/ }
  }],
  
  // Profil professionnel
  professionalProfile: {
    bio: { type: String, trim: true, maxlength: 1000 },
    expertise: [{ type: String, trim: true }],
    consultingAreas: [{ type: String, trim: true }],
    linkedinUrl: { type: String, trim: true },
    personalWebsite: { type: String, trim: true },
    orcidId: { type: String, trim: true },
    googleScholarUrl: { type: String, trim: true }
  },
  
  // Métadonnées
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Index pour améliorer les performances
professorSchema.index({ employeeNumber: 1 });
professorSchema.index({ user: 1 });
professorSchema.index({ department: 1 });
professorSchema.index({ employmentStatus: 1 });
professorSchema.index({ academicRank: 1 });

// Virtual pour le nom complet avec titre
professorSchema.virtual('fullNameWithTitle').get(function() {
  if (!this.user) return '';
  return `${this.academicTitle} ${this.user.firstName} ${this.user.lastName}`;
});

// Virtual pour calculer l'expérience totale
professorSchema.virtual('totalExperienceYears').get(function() {
  let totalYears = 0;
  if (this.professionalExperience && Array.isArray(this.professionalExperience)) {
    this.professionalExperience.forEach(exp => {
      if (exp.startDate) {
        const endDate = exp.endDate || new Date();
        const years = (endDate - exp.startDate) / (1000 * 60 * 60 * 24 * 365.25);
        totalYears += years;
      }
    });
  }
  return Math.round(totalYears);
});

// Virtual pour calculer la note moyenne des évaluations
professorSchema.virtual('averageRating').get(function() {
  if (!this.evaluations || !Array.isArray(this.evaluations) || this.evaluations.length === 0) return 0;
  const totalRating = this.evaluations.reduce((sum, eval) => sum + (eval.ratings?.overallRating || 0), 0);
  return Math.round((totalRating / this.evaluations.length) * 10) / 10;
});

// Middleware pour générer automatiquement le numéro d'employé
professorSchema.pre('save', async function(next) {
  console.log('🔧 Professor pre-save middleware executed');
  console.log(`   Current employeeNumber: ${this.employeeNumber}`);

  if (!this.employeeNumber) {
    console.log('🔢 Generating employee number...');
    const year = new Date().getFullYear();
    const count = await this.constructor.countDocuments({
      employeeNumber: new RegExp(`^PROF${year}`)
    });
    this.employeeNumber = `PROF${year}${String(count + 1).padStart(4, '0')}`;
    console.log(`✅ Generated employee number: ${this.employeeNumber}`);
  }
  next();
});

// Méthode pour obtenir les cours actuels
professorSchema.methods.getCurrentCourses = function() {
  return mongoose.model('Course').find({
    instructor: this._id,
    status: 'active'
  });
};

// Méthode pour calculer la charge d'enseignement
professorSchema.methods.getTeachingLoad = function() {
  return mongoose.model('Course').aggregate([
    { $match: { instructor: this._id, status: 'active' } },
    { $group: { _id: null, totalHours: { $sum: '$creditHours' } } }
  ]);
};

// Méthode pour vérifier la disponibilité
professorSchema.methods.isAvailable = function(day, timeSlot) {
  return this.availability.preferredDays.includes(day) &&
         this.availability.preferredTimeSlots.some(slot => 
           timeSlot >= slot.start && timeSlot <= slot.end
         );
};

// Méthode pour obtenir le profil public
professorSchema.methods.getPublicProfile = function() {
  return {
    _id: this._id,
    fullNameWithTitle: this.fullNameWithTitle,
    department: this.department,
    academicRank: this.academicRank,
    bio: this.professionalProfile.bio,
    expertise: this.professionalProfile.expertise,
    averageRating: this.averageRating,
    totalExperienceYears: this.totalExperienceYears,
    researchInterests: this.research.researchInterests
  };
};

module.exports = mongoose.model('Professor', professorSchema);
