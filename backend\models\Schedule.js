const mongoose = require('mongoose');

const scheduleSchema = new mongoose.Schema({
  // Informations de base
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  description: {
    type: String,
    trim: true,
    maxlength: 1000
  },
  
  // Références
  course: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Course'
  },
  module: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Module'
  },
  instructor: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Professor',
    required: true
  },
  
  // Informations temporelles
  academicYear: {
    type: String,
    required: true,
    match: [/^\d{4}-\d{4}$/, 'Format: YYYY-YYYY']
  },
  semester: {
    type: String,
    enum: ['Fall', 'Spring', 'Summer', 'Year-long'],
    required: true
  },
  
  // Type de session
  sessionType: {
    type: String,
    enum: ['lecture', 'seminar', 'workshop', 'lab', 'exam', 'presentation', 'field_trip', 'guest_speaker', 'group_work', 'consultation'],
    required: true
  },
  
  // Planification
  date: {
    type: Date,
    required: true
  },
  startTime: {
    type: String,
    required: true,
    match: [/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Format: HH:MM']
  },
  endTime: {
    type: String,
    required: true,
    match: [/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Format: HH:MM']
  },
  duration: {
    type: Number, // en minutes
    min: 15,
    max: 480
  },
  
  // Récurrence
  recurrence: {
    isRecurring: { type: Boolean, default: false },
    pattern: {
      type: String,
      enum: ['daily', 'weekly', 'biweekly', 'monthly', 'custom']
    },
    frequency: { type: Number, min: 1, default: 1 },
    daysOfWeek: [{
      type: String,
      enum: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
    }],
    endDate: { type: Date },
    occurrences: { type: Number, min: 1 },
    exceptions: [{ type: Date }] // dates à exclure
  },
  
  // Lieu
  location: {
    type: {
      type: String,
      enum: ['physical', 'online', 'hybrid'],
      default: 'physical'
    },
    building: { type: String, trim: true },
    room: { type: String, trim: true },
    floor: { type: String, trim: true },
    capacity: { type: Number, min: 1 },
    equipment: [{ type: String, trim: true }],
    
    // Pour les sessions en ligne
    platform: {
      type: String,
      enum: ['Zoom', 'Teams', 'Google Meet', 'WebEx', 'Other']
    },
    meetingUrl: { type: String, trim: true },
    meetingId: { type: String, trim: true },
    password: { type: String, trim: true },
    
    // Adresse physique complète
    address: {
      street: { type: String, trim: true },
      city: { type: String, trim: true },
      zipCode: { type: String, trim: true },
      country: { type: String, trim: true, default: 'Tunisia' }
    }
  },
  
  // Participants
  participants: {
    expectedStudents: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Student'
    }],
    confirmedStudents: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Student'
    }],
    guestSpeakers: [{
      name: { type: String, required: true, trim: true },
      title: { type: String, trim: true },
      organization: { type: String, trim: true },
      email: { type: String, trim: true },
      phone: { type: String, trim: true },
      bio: { type: String, trim: true },
      topic: { type: String, trim: true }
    }],
    assistants: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }]
  },
  
  // Contenu de la session
  content: {
    topics: [{ type: String, trim: true }],
    learningObjectives: [{ type: String, trim: true }],
    agenda: [{
      time: { type: String, match: [/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Format: HH:MM'] },
      activity: { type: String, required: true, trim: true },
      duration: { type: Number, min: 1 }, // en minutes
      presenter: { type: String, trim: true }
    }],
    materials: [{
      title: { type: String, required: true, trim: true },
      type: {
        type: String,
        enum: ['slides', 'handout', 'reading', 'video', 'software', 'dataset', 'other']
      },
      url: { type: String, trim: true },
      filename: { type: String, trim: true },
      isRequired: { type: Boolean, default: false }
    }],
    assignments: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Assignment'
    }]
  },
  
  // Préparation requise
  preparation: {
    requiredReadings: [{
      title: { type: String, required: true, trim: true },
      author: { type: String, trim: true },
      source: { type: String, trim: true },
      pages: { type: String, trim: true },
      url: { type: String, trim: true }
    }],
    preparationTasks: [{
      task: { type: String, required: true, trim: true },
      estimatedTime: { type: Number, min: 1 }, // en minutes
      dueDate: { type: Date }
    }],
    prerequisites: [{ type: String, trim: true }]
  },
  
  // Statut et gestion
  status: {
    type: String,
    enum: ['scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'postponed', 'rescheduled'],
    default: 'scheduled'
  },
  
  // Changements et historique
  changes: [{
    changeType: {
      type: String,
      enum: ['time_change', 'location_change', 'cancellation', 'postponement', 'content_update', 'instructor_change']
    },
    previousValue: { type: String, trim: true },
    newValue: { type: String, trim: true },
    reason: { type: String, trim: true },
    changedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    changeDate: { type: Date, default: Date.now },
    notificationSent: { type: Boolean, default: false }
  }],
  
  // Présence
  attendance: {
    isAttendanceRequired: { type: Boolean, default: true },
    attendanceMethod: {
      type: String,
      enum: ['manual', 'qr_code', 'digital_signature', 'biometric', 'automatic'],
      default: 'manual'
    },
    attendanceRecords: [{
      student: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Student'
      },
      status: {
        type: String,
        enum: ['present', 'absent', 'late', 'excused', 'partial'],
        default: 'absent'
      },
      arrivalTime: { type: Date },
      departureTime: { type: Date },
      notes: { type: String, trim: true },
      recordedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      recordedAt: { type: Date, default: Date.now }
    }],
    attendanceRate: { type: Number, min: 0, max: 100, default: 0 }
  },
  
  // Évaluation de la session
  evaluation: {
    studentFeedback: [{
      student: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Student'
      },
      ratings: {
        contentQuality: { type: Number, min: 1, max: 5 },
        instructorEffectiveness: { type: Number, min: 1, max: 5 },
        sessionPace: { type: Number, min: 1, max: 5 },
        materialClarity: { type: Number, min: 1, max: 5 },
        overallSatisfaction: { type: Number, min: 1, max: 5 }
      },
      comments: { type: String, trim: true },
      suggestions: { type: String, trim: true },
      submittedAt: { type: Date, default: Date.now }
    }],
    averageRatings: {
      contentQuality: { type: Number, min: 1, max: 5, default: 0 },
      instructorEffectiveness: { type: Number, min: 1, max: 5, default: 0 },
      sessionPace: { type: Number, min: 1, max: 5, default: 0 },
      materialClarity: { type: Number, min: 1, max: 5, default: 0 },
      overallSatisfaction: { type: Number, min: 1, max: 5, default: 0 }
    }
  },
  
  // Enregistrement et ressources
  recording: {
    isRecorded: { type: Boolean, default: false },
    recordingUrl: { type: String, trim: true },
    recordingPassword: { type: String, trim: true },
    recordingDuration: { type: Number, min: 0 }, // en minutes
    isAvailableToStudents: { type: Boolean, default: true },
    expirationDate: { type: Date }
  },
  
  // Notifications
  notifications: {
    reminderSent: { type: Boolean, default: false },
    reminderDate: { type: Date },
    changeNotificationSent: { type: Boolean, default: false },
    followUpSent: { type: Boolean, default: false }
  },
  
  // Métadonnées
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Index pour améliorer les performances
scheduleSchema.index({ date: 1, startTime: 1 });
scheduleSchema.index({ instructor: 1, date: 1 });
scheduleSchema.index({ course: 1, date: 1 });
scheduleSchema.index({ status: 1 });
scheduleSchema.index({ academicYear: 1, semester: 1 });

// Virtual pour calculer la durée en minutes
scheduleSchema.virtual('calculatedDuration').get(function() {
  if (this.startTime && this.endTime) {
    const [startHour, startMin] = this.startTime.split(':').map(Number);
    const [endHour, endMin] = this.endTime.split(':').map(Number);
    const startMinutes = startHour * 60 + startMin;
    const endMinutes = endHour * 60 + endMin;
    return endMinutes - startMinutes;
  }
  return this.duration || 0;
});

// Virtual pour vérifier si la session est en cours
scheduleSchema.virtual('isInProgress').get(function() {
  const now = new Date();
  const sessionDate = new Date(this.date);
  const [startHour, startMin] = this.startTime.split(':').map(Number);
  const [endHour, endMin] = this.endTime.split(':').map(Number);
  
  const startDateTime = new Date(sessionDate);
  startDateTime.setHours(startHour, startMin, 0, 0);
  
  const endDateTime = new Date(sessionDate);
  endDateTime.setHours(endHour, endMin, 0, 0);
  
  return now >= startDateTime && now <= endDateTime;
});

// Virtual pour vérifier si la session est terminée
scheduleSchema.virtual('isCompleted').get(function() {
  const now = new Date();
  const sessionDate = new Date(this.date);
  const [endHour, endMin] = this.endTime.split(':').map(Number);
  
  const endDateTime = new Date(sessionDate);
  endDateTime.setHours(endHour, endMin, 0, 0);
  
  return now > endDateTime;
});

// Middleware pour valider les heures
scheduleSchema.pre('save', function(next) {
  const [startHour, startMin] = this.startTime.split(':').map(Number);
  const [endHour, endMin] = this.endTime.split(':').map(Number);
  const startMinutes = startHour * 60 + startMin;
  const endMinutes = endHour * 60 + endMin;
  
  if (endMinutes <= startMinutes) {
    return next(new Error('End time must be after start time'));
  }
  
  // Calculer automatiquement la durée
  this.duration = endMinutes - startMinutes;
  
  next();
});

// Middleware pour calculer le taux de présence
scheduleSchema.pre('save', function(next) {
  if (this.attendance.attendanceRecords.length > 0) {
    const presentCount = this.attendance.attendanceRecords.filter(
      record => ['present', 'late'].includes(record.status)
    ).length;
    
    this.attendance.attendanceRate = Math.round(
      (presentCount / this.attendance.attendanceRecords.length) * 100
    );
  }
  
  next();
});

// Méthode pour marquer la présence
scheduleSchema.methods.markAttendance = function(studentId, status, recordedBy, notes) {
  const existingRecord = this.attendance.attendanceRecords.find(
    record => record.student.toString() === studentId.toString()
  );
  
  if (existingRecord) {
    existingRecord.status = status;
    existingRecord.notes = notes;
    existingRecord.recordedBy = recordedBy;
    existingRecord.recordedAt = new Date();
  } else {
    this.attendance.attendanceRecords.push({
      student: studentId,
      status: status,
      notes: notes,
      recordedBy: recordedBy,
      arrivalTime: status === 'present' ? new Date() : null
    });
  }
  
  return this.save();
};

// Méthode pour reprogrammer une session
scheduleSchema.methods.reschedule = function(newDate, newStartTime, newEndTime, reason, changedBy) {
  this.changes.push({
    changeType: 'time_change',
    previousValue: `${this.date} ${this.startTime}-${this.endTime}`,
    newValue: `${newDate} ${newStartTime}-${newEndTime}`,
    reason: reason,
    changedBy: changedBy
  });
  
  this.date = newDate;
  this.startTime = newStartTime;
  this.endTime = newEndTime;
  this.status = 'rescheduled';
  
  return this.save();
};

// Méthode pour annuler une session
scheduleSchema.methods.cancel = function(reason, cancelledBy) {
  this.changes.push({
    changeType: 'cancellation',
    reason: reason,
    changedBy: cancelledBy
  });
  
  this.status = 'cancelled';
  return this.save();
};

// Méthode pour calculer les évaluations moyennes
scheduleSchema.methods.calculateAverageRatings = function() {
  if (this.evaluation.studentFeedback.length === 0) return;
  
  const feedback = this.evaluation.studentFeedback;
  const avgRatings = {
    contentQuality: 0,
    instructorEffectiveness: 0,
    sessionPace: 0,
    materialClarity: 0,
    overallSatisfaction: 0
  };
  
  Object.keys(avgRatings).forEach(key => {
    const sum = feedback.reduce((total, fb) => total + (fb.ratings[key] || 0), 0);
    avgRatings[key] = Math.round((sum / feedback.length) * 10) / 10;
  });
  
  this.evaluation.averageRatings = avgRatings;
  return this.save();
};

// Méthode pour générer des sessions récurrentes
scheduleSchema.statics.generateRecurringSessions = function(scheduleData) {
  const sessions = [];
  const { recurrence } = scheduleData;
  
  if (!recurrence.isRecurring) {
    return [scheduleData];
  }
  
  let currentDate = new Date(scheduleData.date);
  const endDate = recurrence.endDate ? new Date(recurrence.endDate) : null;
  const maxOccurrences = recurrence.occurrences || 52; // limite par défaut
  
  for (let i = 0; i < maxOccurrences; i++) {
    if (endDate && currentDate > endDate) break;
    
    // Vérifier si la date n'est pas dans les exceptions
    if (!recurrence.exceptions.some(exception => 
      new Date(exception).toDateString() === currentDate.toDateString()
    )) {
      const sessionData = { ...scheduleData };
      sessionData.date = new Date(currentDate);
      delete sessionData._id; // Supprimer l'ID pour créer une nouvelle session
      sessions.push(sessionData);
    }
    
    // Calculer la prochaine date selon le pattern
    switch (recurrence.pattern) {
      case 'daily':
        currentDate.setDate(currentDate.getDate() + recurrence.frequency);
        break;
      case 'weekly':
        currentDate.setDate(currentDate.getDate() + (7 * recurrence.frequency));
        break;
      case 'biweekly':
        currentDate.setDate(currentDate.getDate() + 14);
        break;
      case 'monthly':
        currentDate.setMonth(currentDate.getMonth() + recurrence.frequency);
        break;
    }
  }
  
  return sessions;
};

// Méthode statique pour créer les entrées de planning d'un cours (TEMPORAIRE - sera remplacée)
scheduleSchema.statics.createCourseSchedule = async function(course) {
  console.log('📅 Création temporaire du planning pour le cours:', course.courseCode);
  console.log('⚠️ Cette méthode sera remplacée par le nouveau système automatique');

  // Pour l'instant, on ne crée rien pour éviter les erreurs de validation
  // Le nouveau système sera implémenté dans CourseAssignment
  return [];
};

// Méthode statique pour supprimer les entrées de planning d'un cours
scheduleSchema.statics.removeCourseSchedule = async function(courseId) {
  console.log('🗑️ Suppression du planning pour le cours:', courseId);

  const result = await this.deleteMany({ course: courseId });
  console.log(`✅ ${result.deletedCount} entrées de planning supprimées`);

  return result;
};

module.exports = mongoose.model('Schedule', scheduleSchema);
