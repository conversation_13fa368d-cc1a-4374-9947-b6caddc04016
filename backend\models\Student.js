const mongoose = require('mongoose');

const studentSchema = new mongoose.Schema({
  // Référence vers l'utilisateur
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  
  // Numéro d'étudiant unique
  studentNumber: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  
  // Informations académiques
  academicYear: {
    type: String,
    required: true,
    match: [/^\d{4}-\d{4}$/, 'Format: YYYY-YYYY']
  },
  cohort: {
    type: String,
    required: true,
    trim: true
  },
  program: {
    type: String,
    enum: ['EMBA', 'Executive MBA', 'Part-time MBA', 'Full-time MBA'],
    default: 'EMBA'
  },
  promotion: {
    type: String,
    enum: ['EMBA1', 'EMBA2'],
    required: true,
    index: true
  },
  class: {
    type: String,
    required: true,
    index: true,
    // Format: EMBA1A, EMBA1B, EMBA2A, etc.
    match: [/^EMBA[12][A-Z]$/, 'Format: EMBA1A, EMBA1B, etc.']
  },
  specialization: {
    type: String,
    enum: ['General Management', 'Finance', 'Marketing', 'Operations', 'Strategy', 'Digital Transformation', 'Entrepreneurship'],
    trim: true
  },
  
  // Statut académique
  enrollmentStatus: {
    type: String,
    enum: ['active', 'inactive', 'suspended', 'graduated', 'dropped', 'deferred'],
    default: 'active'
  },
  enrollmentDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  expectedGraduationDate: {
    type: Date,
    required: true
  },
  actualGraduationDate: {
    type: Date
  },
  
  // Informations professionnelles (spécifiques EMBA)
  currentEmployment: {
    company: { type: String, trim: true },
    position: { type: String, trim: true },
    industry: { type: String, trim: true },
    yearsOfExperience: { type: Number, min: 0 },
    annualSalary: { type: Number, min: 0 },
    managementLevel: {
      type: String,
      enum: ['Entry Level', 'Mid-Level', 'Senior Level', 'Executive', 'C-Level']
    },
    teamSize: { type: Number, min: 0 },
    workLocation: {
      city: { type: String, trim: true },
      country: { type: String, trim: true }
    }
  },
  
  // Formation antérieure
  previousEducation: [{
    degree: { type: String, required: true, trim: true },
    institution: { type: String, required: true, trim: true },
    fieldOfStudy: { type: String, trim: true },
    graduationYear: { type: Number, min: 1950, max: new Date().getFullYear() },
    gpa: { type: Number, min: 0, max: 4 },
    country: { type: String, trim: true }
  }],
  
  // Certifications professionnelles
  certifications: [{
    name: { type: String, required: true, trim: true },
    issuingOrganization: { type: String, required: true, trim: true },
    issueDate: { type: Date },
    expirationDate: { type: Date },
    credentialId: { type: String, trim: true },
    isActive: { type: Boolean, default: true }
  }],
  
  // Informations financières
  tuitionFee: {
    total: { type: Number, required: true, min: 0 },
    paid: { type: Number, default: 0, min: 0 },
    remaining: { type: Number, default: 0, min: 0 },
    currency: { type: String, default: 'TND' },
    paymentPlan: {
      type: String,
      enum: ['full_payment', 'installments', 'employer_sponsored', 'scholarship'],
      default: 'installments'
    }
  },
  
  // Performance académique
  academicRecord: {
    currentGPA: { type: Number, min: 0, max: 4, default: 0 },
    cumulativeGPA: { type: Number, min: 0, max: 4, default: 0 },
    creditsCompleted: { type: Number, default: 0, min: 0 },
    creditsRequired: { type: Number, required: true, min: 0 },
    academicStanding: {
      type: String,
      enum: ['excellent', 'good', 'satisfactory', 'probation', 'warning'],
      default: 'satisfactory'
    }
  },
  
  // Présence et participation
  attendanceRecord: {
    totalSessions: { type: Number, default: 0 },
    attendedSessions: { type: Number, default: 0 },
    attendanceRate: { type: Number, default: 0, min: 0, max: 100 },
    absenceReasons: [{
      date: { type: Date },
      reason: { type: String, trim: true },
      isExcused: { type: Boolean, default: false }
    }]
  },
  
  // Projet final / Thèse
  finalProject: {
    title: { type: String, trim: true },
    description: { type: String, trim: true },
    supervisor: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Professor'
    },
    status: {
      type: String,
      enum: ['not_started', 'proposal_submitted', 'in_progress', 'completed', 'defended'],
      default: 'not_started'
    },
    submissionDate: { type: Date },
    defenseDate: { type: Date },
    grade: { type: Number, min: 0, max: 20 }
  },
  
  // Informations de contact d'urgence
  emergencyContact: {
    name: { type: String, trim: true },
    relationship: { type: String, trim: true },
    phone: { type: String, trim: true },
    email: { type: String, trim: true, lowercase: true },
    address: { type: String, trim: true }
  },
  
  // Préférences d'apprentissage
  learningPreferences: {
    preferredSchedule: {
      type: String,
      enum: ['weekends', 'evenings', 'intensive_blocks', 'flexible']
    },
    learningStyle: {
      type: String,
      enum: ['visual', 'auditory', 'kinesthetic', 'reading_writing']
    },
    groupWorkPreference: {
      type: String,
      enum: ['individual', 'small_groups', 'large_groups', 'mixed']
    }
  },
  
  // Activités extracurriculaires
  extracurricularActivities: [{
    activity: { type: String, trim: true },
    role: { type: String, trim: true },
    startDate: { type: Date },
    endDate: { type: Date },
    description: { type: String, trim: true }
  }],
  
  // Réseautage et alumni
  networkingProfile: {
    linkedinUrl: { type: String, trim: true },
    industryInterests: [{ type: String, trim: true }],
    careerGoals: { type: String, trim: true },
    mentorshipInterest: {
      type: String,
      enum: ['mentor', 'mentee', 'both', 'none'],
      default: 'none'
    }
  },
  
  // Documents et fichiers
  documents: [{
    type: {
      type: String,
      enum: ['transcript', 'diploma', 'cv', 'recommendation_letter', 'id_copy', 'photo', 'other'],
      required: true
    },
    filename: { type: String, required: true },
    originalName: { type: String, required: true },
    path: { type: String, required: true },
    size: { type: Number },
    mimeType: { type: String },
    uploadDate: { type: Date, default: Date.now },
    uploadedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  }],
  
  // Notes et commentaires des professeurs/staff
  notes: [{
    author: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    content: { type: String, required: true, trim: true },
    category: {
      type: String,
      enum: ['academic', 'behavioral', 'administrative', 'personal', 'other'],
      default: 'academic'
    },
    isPrivate: { type: Boolean, default: false },
    createdAt: { type: Date, default: Date.now }
  }],
  
  // Métadonnées
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Index pour améliorer les performances
studentSchema.index({ studentNumber: 1 });
studentSchema.index({ user: 1 });
studentSchema.index({ enrollmentStatus: 1 });
studentSchema.index({ academicYear: 1 });
studentSchema.index({ cohort: 1 });

// Virtual pour calculer l'âge
studentSchema.virtual('age').get(function() {
  if (!this.user || !this.user.dateOfBirth) return null;
  const today = new Date();
  const birthDate = new Date(this.user.dateOfBirth);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  return age;
});

// Virtual pour calculer le taux de présence
studentSchema.virtual('attendancePercentage').get(function() {
  if (this.attendanceRecord.totalSessions === 0) return 0;
  return Math.round((this.attendanceRecord.attendedSessions / this.attendanceRecord.totalSessions) * 100);
});

// Virtual pour calculer le solde restant
studentSchema.virtual('remainingBalance').get(function() {
  return this.tuitionFee.total - this.tuitionFee.paid;
});

// Middleware pour générer automatiquement le numéro d'étudiant
studentSchema.pre('save', async function(next) {
  if (!this.studentNumber) {
    const year = new Date().getFullYear();
    const count = await this.constructor.countDocuments({
      studentNumber: new RegExp(`^EMBA${year}`)
    });
    this.studentNumber = `EMBA${year}${String(count + 1).padStart(4, '0')}`;
  }
  
  // Calculer le solde restant
  this.tuitionFee.remaining = this.tuitionFee.total - this.tuitionFee.paid;
  
  next();
});

// Méthode pour calculer la progression académique
studentSchema.methods.getAcademicProgress = function() {
  const progressPercentage = (this.academicRecord.creditsCompleted / this.academicRecord.creditsRequired) * 100;
  return Math.min(Math.round(progressPercentage), 100);
};

// Méthode pour vérifier l'éligibilité à la graduation
studentSchema.methods.isEligibleForGraduation = function() {
  return this.academicRecord.creditsCompleted >= this.academicRecord.creditsRequired &&
         this.academicRecord.currentGPA >= 2.0 &&
         this.tuitionFee.remaining <= 0 &&
         this.finalProject.status === 'defended';
};

// Méthode pour obtenir le profil complet de l'étudiant
studentSchema.methods.getFullProfile = function() {
  return this.populate('user', '-password -resetPasswordToken -verificationToken');
};

// Méthode statique pour affecter automatiquement à une classe
studentSchema.statics.assignToClass = async function(promotion) {
  const CLASS_CAPACITY = 30;

  // Trouver toutes les classes existantes pour cette promotion
  const existingClasses = await this.aggregate([
    { $match: { promotion: promotion } },
    { $group: { _id: '$class', count: { $sum: 1 } } },
    { $sort: { _id: 1 } }
  ]);

  // Chercher une classe non pleine
  for (let classData of existingClasses) {
    if (classData.count < CLASS_CAPACITY) {
      return classData._id;
    }
  }

  // Toutes les classes sont pleines, créer une nouvelle classe
  const nextClassLetter = String.fromCharCode(65 + existingClasses.length); // A, B, C, D...
  return `${promotion}${nextClassLetter}`;
};

// Méthode statique pour obtenir les statistiques des classes
studentSchema.statics.getClassStats = async function(promotion = null) {
  const matchStage = promotion ? { promotion } : {};

  return await this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: { promotion: '$promotion', class: '$class' },
        count: { $sum: 1 },
        students: { $push: { _id: '$_id', studentNumber: '$studentNumber' } }
      }
    },
    { $sort: { '_id.promotion': 1, '_id.class': 1 } }
  ]);
};

// Méthode statique pour obtenir les étudiants d'une classe
studentSchema.statics.getClassStudents = function(className) {
  return this.find({ class: className })
    .populate('user', 'firstName lastName email')
    .sort({ studentNumber: 1 });
};

// Méthode d'instance pour inscrire automatiquement aux cours de la classe
studentSchema.methods.autoEnrollToClassCourses = async function() {
  const Course = mongoose.model('Course');
  const Enrollment = mongoose.model('Enrollment');
  const Notification = mongoose.model('Notification');

  try {
    // Récupérer tous les cours de la classe de l'étudiant
    const courses = await Course.find({
      class: this.class,
      status: { $in: ['active', 'draft'] },
      registrationStatus: 'open'
    });

    const enrollments = [];
    const currentYear = new Date().getFullYear();

    for (let course of courses) {
      // Vérifier si l'étudiant n'est pas déjà inscrit
      const existingEnrollment = await Enrollment.findOne({
        student: this._id,
        course: course._id
      });

      if (!existingEnrollment) {
        // Mapper les semestres du Course vers les semestres d'Enrollment
        const semesterMapping = {
          'Semestre 1': 'Fall',
          'Semestre 2': 'Spring'
        };
        const enrollmentSemester = semesterMapping[course.semester] || 'Fall';

        const enrollment = new Enrollment({
          student: this._id,
          course: course._id,
          academicYear: `${currentYear}-${currentYear + 1}`,
          semester: enrollmentSemester,
          enrollmentDate: new Date(),
          startDate: course.startDate || new Date(),
          expectedEndDate: course.endDate || new Date(Date.now() + 120 * 24 * 60 * 60 * 1000),
          status: 'enrolled',
          tuitionFee: {
            amount: 0, // Inclus dans les frais généraux
            currency: 'TND',
            dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
            paidAmount: 0,
            remainingAmount: 0
          }
        });

        await enrollment.save();
        enrollments.push(enrollment);

        // Mettre à jour le nombre d'étudiants inscrits dans le cours
        await Course.findByIdAndUpdate(course._id, {
          $inc: { 'capacity.current': 1 }
        });
      }
    }

    // Créer une notification pour l'étudiant s'il y a de nouvelles inscriptions
    if (enrollments.length > 0) {
      await Notification.create({
        type: 'course_enrollment',
        title: 'Inscription automatique aux cours',
        message: `Vous avez été automatiquement inscrit(e) à ${enrollments.length} cours de votre classe ${this.class}`,
        recipients: {
          users: [this.user]
        },
        data: {
          studentId: this._id,
          className: this.class,
          coursesCount: enrollments.length,
          courseIds: enrollments.map(e => e.course)
        }
      });
    }

    console.log(`✅ Étudiant ${this.studentNumber} inscrit automatiquement à ${enrollments.length} cours`);
    return enrollments;
  } catch (error) {
    console.error('❌ Erreur lors de l\'inscription automatique:', error);
    throw error;
  }
};

module.exports = mongoose.model('Student', studentSchema);
