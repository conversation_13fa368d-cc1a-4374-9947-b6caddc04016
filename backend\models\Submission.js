const mongoose = require('mongoose');

const submissionSchema = new mongoose.Schema({
  // Références principales
  assignment: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Assignment',
    required: true
  },
  student: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Student',
    required: true
  },
  enrollment: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Enrollment',
    required: true
  },
  
  // Informations de soumission
  submissionNumber: {
    type: Number,
    default: 1
  },
  submittedAt: {
    type: Date,
    default: Date.now
  },
  isLate: {
    type: Boolean,
    default: false
  },
  daysLate: {
    type: Number,
    default: 0
  },
  
  // Contenu de la soumission
  textContent: {
    type: String,
    trim: true
  },
  files: [{
    filename: String,
    originalName: String,
    path: String,
    size: Number,
    mimeType: String,
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  // Statut de la soumission
  status: {
    type: String,
    enum: ['submitted', 'graded', 'returned', 'resubmitted'],
    default: 'submitted'
  },
  
  // Notation
  grade: {
    type: Number,
    min: 0
  },
  maxPoints: {
    type: Number,
    min: 0
  },
  percentage: {
    type: Number,
    min: 0,
    max: 100
  },
  letterGrade: {
    type: String,
    enum: ['A+', 'A', 'A-', 'B+', 'B', 'B-', 'C+', 'C', 'C-', 'D+', 'D', 'F']
  },
  
  // Feedback
  feedback: {
    general: {
      type: String,
      trim: true
    },
    rubricScores: [{
      criterion: String,
      score: Number,
      maxScore: Number,
      feedback: String
    }],
    strengths: [{
      type: String,
      trim: true
    }],
    improvements: [{
      type: String,
      trim: true
    }]
  },
  
  // Dates importantes
  gradedAt: {
    type: Date
  },
  returnedAt: {
    type: Date
  },
  viewedAt: {
    type: Date
  },
  
  // Informations de groupe (si applicable)
  groupMembers: [{
    student: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Student'
    },
    contribution: {
      type: Number,
      min: 0,
      max: 100
    }
  }],
  
  // Historique des versions
  versions: [{
    versionNumber: Number,
    submittedAt: Date,
    textContent: String,
    files: [{
      filename: String,
      originalName: String,
      path: String,
      size: Number,
      mimeType: String
    }],
    changes: String
  }],
  
  // Métadonnées
  gradedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  lastModifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Index pour améliorer les performances
submissionSchema.index({ assignment: 1, student: 1 }, { unique: true });
submissionSchema.index({ student: 1, submittedAt: -1 });
submissionSchema.index({ assignment: 1, submittedAt: -1 });
submissionSchema.index({ status: 1 });

// Virtual pour vérifier si la soumission a été notée
submissionSchema.virtual('isGraded').get(function() {
  return this.grade !== undefined && this.grade !== null;
});

// Virtual pour vérifier si la soumission a été vue par l'étudiant
submissionSchema.virtual('isViewed').get(function() {
  return this.viewedAt !== undefined && this.viewedAt !== null;
});

// Virtual pour calculer le score en pourcentage
submissionSchema.virtual('scorePercentage').get(function() {
  if (!this.grade || !this.maxPoints) return 0;
  return Math.round((this.grade / this.maxPoints) * 100);
});

// Middleware pour calculer automatiquement certains champs
submissionSchema.pre('save', async function(next) {
  // Calculer si la soumission est en retard
  if (this.assignment) {
    const Assignment = mongoose.model('Assignment');
    const assignment = await Assignment.findById(this.assignment);
    
    if (assignment) {
      this.isLate = this.submittedAt > assignment.dueDate;
      
      if (this.isLate) {
        const diffTime = this.submittedAt - assignment.dueDate;
        this.daysLate = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      }
      
      // Définir maxPoints depuis l'assignment
      if (!this.maxPoints) {
        this.maxPoints = assignment.maxPoints;
      }
    }
  }
  
  // Calculer le pourcentage si la note est définie
  if (this.grade !== undefined && this.maxPoints) {
    this.percentage = Math.round((this.grade / this.maxPoints) * 100);
    
    // Calculer la note lettre
    if (this.percentage >= 97) this.letterGrade = 'A+';
    else if (this.percentage >= 93) this.letterGrade = 'A';
    else if (this.percentage >= 90) this.letterGrade = 'A-';
    else if (this.percentage >= 87) this.letterGrade = 'B+';
    else if (this.percentage >= 83) this.letterGrade = 'B';
    else if (this.percentage >= 80) this.letterGrade = 'B-';
    else if (this.percentage >= 77) this.letterGrade = 'C+';
    else if (this.percentage >= 73) this.letterGrade = 'C';
    else if (this.percentage >= 70) this.letterGrade = 'C-';
    else if (this.percentage >= 67) this.letterGrade = 'D+';
    else if (this.percentage >= 60) this.letterGrade = 'D';
    else this.letterGrade = 'F';
  }
  
  next();
});

// Méthode pour marquer comme vue par l'étudiant
submissionSchema.methods.markAsViewed = function() {
  this.viewedAt = new Date();
  return this.save();
};

// Méthode pour ajouter une nouvelle version
submissionSchema.methods.addVersion = function(textContent, files, changes) {
  const versionNumber = this.versions.length + 1;
  
  this.versions.push({
    versionNumber,
    submittedAt: new Date(),
    textContent,
    files,
    changes
  });
  
  // Mettre à jour le contenu principal
  this.textContent = textContent;
  this.files = files;
  this.submittedAt = new Date();
  this.submissionNumber = versionNumber;
  
  return this.save();
};

// Méthode pour calculer la note finale avec pénalités
submissionSchema.methods.calculateFinalGrade = async function() {
  if (!this.assignment) return this.grade;
  
  const Assignment = mongoose.model('Assignment');
  const assignment = await Assignment.findById(this.assignment);
  
  if (!assignment || !this.grade) return this.grade;
  
  return assignment.calculateScoreWithPenalty(this.grade, this.submittedAt);
};

module.exports = mongoose.model('Submission', submissionSchema);
