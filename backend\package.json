{"name": "emba-backend", "version": "1.0.0", "description": "EMBA Management System Backend API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "seed": "node scripts/seedDatabase.js", "check": "node scripts/checkSystem.js"}, "keywords": ["emba", "education", "management", "api", "nodejs", "mongodb"], "author": "EMBA Team", "license": "MIT", "dependencies": {"axios": "^1.10.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.10.0", "express-validator": "^7.0.1", "form-data": "^4.0.3", "hellosign-sdk": "^2.1.0", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "mammoth": "^1.9.1", "moment": "^2.29.4", "mongoose": "^7.5.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.10.1", "pdf-parse": "^1.1.1", "uuid": "^9.0.0"}, "devDependencies": {"@types/jest": "^29.5.5", "jest": "^29.6.4", "nodemon": "^3.0.1", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}