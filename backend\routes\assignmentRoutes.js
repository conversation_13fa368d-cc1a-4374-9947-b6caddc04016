const express = require('express');
const router = express.Router();
const Assignment = require('../models/Assignment');

router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 10, course, instructor, type, status } = req.query;
    const query = {};
    if (course) query.course = course;
    if (instructor) query.instructor = instructor;
    if (type) query.type = type;
    if (status) query.status = status;
    
    const assignments = await Assignment.find(query)
      .populate('course', 'courseCode title')
      .populate('instructor', 'user academicTitle')
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ dueDate: 1 });
    
    const total = await Assignment.countDocuments(query);
    res.json({ assignments, totalPages: Math.ceil(total / limit), currentPage: page, total });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

router.get('/:id', async (req, res) => {
  try {
    const assignment = await Assignment.findById(req.params.id)
      .populate('course', 'courseCode title')
      .populate('instructor', 'user academicTitle')
      .populate('submissions.student', 'studentNumber user');
    
    if (!assignment) return res.status(404).json({ message: 'Assignment not found' });
    res.json(assignment);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

router.post('/', async (req, res) => {
  try {
    const assignment = new Assignment(req.body);
    await assignment.save();
    res.status(201).json(assignment);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.put('/:id', async (req, res) => {
  try {
    const assignment = await Assignment.findByIdAndUpdate(req.params.id, req.body, { new: true });
    if (!assignment) return res.status(404).json({ message: 'Assignment not found' });
    res.json(assignment);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.post('/:id/submit', async (req, res) => {
  try {
    const { studentId, submissionData } = req.body;
    const assignment = await Assignment.findById(req.params.id);
    
    if (!assignment) return res.status(404).json({ message: 'Assignment not found' });
    
    await assignment.submitWork(studentId, submissionData);
    res.json({ message: 'Assignment submitted successfully' });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.post('/:id/grade', async (req, res) => {
  try {
    const { studentId, gradeData } = req.body;
    const assignment = await Assignment.findById(req.params.id);
    
    if (!assignment) return res.status(404).json({ message: 'Assignment not found' });
    
    await assignment.gradeSubmission(studentId, gradeData);
    res.json({ message: 'Assignment graded successfully' });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

module.exports = router;
