const express = require('express');
const router = express.Router();
const Attendance = require('../models/Attendance');

router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 10, student, course, date, status } = req.query;
    const query = {};
    if (student) query.student = student;
    if (course) query.course = course;
    if (date) query.date = { $gte: new Date(date) };
    if (status) query.status = status;
    
    const attendance = await Attendance.find(query)
      .populate('student', 'studentNumber user')
      .populate('course', 'courseCode title')
      .populate('schedule', 'title date startTime endTime')
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ date: -1 });
    
    const total = await Attendance.countDocuments(query);
    res.json({ attendance, totalPages: Math.ceil(total / limit), currentPage: page, total });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

router.get('/:id', async (req, res) => {
  try {
    const attendance = await Attendance.findById(req.params.id)
      .populate('student', 'studentNumber user')
      .populate('course', 'courseCode title')
      .populate('schedule', 'title date startTime endTime');
    
    if (!attendance) return res.status(404).json({ message: 'Attendance record not found' });
    res.json(attendance);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

router.post('/', async (req, res) => {
  try {
    const attendance = new Attendance(req.body);
    await attendance.save();
    res.status(201).json(attendance);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.put('/:id', async (req, res) => {
  try {
    const attendance = await Attendance.findByIdAndUpdate(req.params.id, req.body, { new: true });
    if (!attendance) return res.status(404).json({ message: 'Attendance record not found' });
    res.json(attendance);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.get('/student/:studentId/stats', async (req, res) => {
  try {
    const { studentId } = req.params;
    const { courseId, startDate, endDate } = req.query;
    
    const stats = await Attendance.calculateStudentAttendanceStats(
      studentId, 
      courseId, 
      startDate ? new Date(startDate) : null, 
      endDate ? new Date(endDate) : null
    );
    
    res.json(stats[0] || {});
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

module.exports = router;
