const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const CourseDocument = require('../models/CourseDocument');
const Course = require('../models/Course');
const { authenticate, authorize } = require('../middleware/auth');

// Configuration de multer pour l'upload de fichiers
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadPath = path.join(__dirname, '../uploads/course-documents');
    
    // C<PERSON>er le dossier s'il n'existe pas
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    
    cb(null, uploadPath);
  },
  filename: function (req, file, cb) {
    // Générer un nom de fichier unique
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const extension = path.extname(file.originalname);
    cb(null, `course-${req.params.courseId}-${uniqueSuffix}${extension}`);
  }
});

// Filtrer les types de fichiers autorisés
const fileFilter = (req, file, cb) => {
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain',
    'image/jpeg',
    'image/jpg',
    'image/png'
  ];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Type de fichier non autorisé. Seuls les PDF, Word, PowerPoint, images et fichiers texte sont acceptés.'), false);
  }
};

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 50 * 1024 * 1024 // 50MB max
  }
});

// GET /api/courses/:courseId/documents - Récupérer tous les documents d'un cours
router.get('/:courseId/documents', authenticate, async (req, res) => {
  try {
    const { courseId } = req.params;
    const { type, search } = req.query;
    
    // Vérifier que le cours existe
    const course = await Course.findById(courseId);
    if (!course) {
      return res.status(404).json({
        success: false,
        message: 'Cours non trouvé'
      });
    }

    // Construire la requête
    let query = { course: courseId, status: 'active' };
    
    // Filtrer par type si spécifié
    if (type) {
      query.documentType = type;
    }

    // Recherche textuelle si spécifiée
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } }
      ];
    }

    // Pour les étudiants, ne montrer que les documents visibles
    if (req.user.role === 'student') {
      query.visibleToStudents = true;
      query.isVisible = true;
    }

    const documents = await CourseDocument.find(query)
      .populate('uploadedBy', 'firstName lastName')
      .sort({ displayOrder: 1, createdAt: -1 });

    res.json({
      success: true,
      documents
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des documents:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération des documents'
    });
  }
});

// POST /api/courses/:courseId/documents - Upload d'un nouveau document
router.post('/:courseId/documents', authenticate, authorize('professor', 'admin'), upload.single('document'), async (req, res) => {
  try {
    const { courseId } = req.params;
    
    // Vérifier que le cours existe
    const course = await Course.findById(courseId);
    if (!course) {
      return res.status(404).json({
        success: false,
        message: 'Cours non trouvé'
      });
    }

    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'Aucun fichier fourni'
      });
    }

    // Créer le document
    const documentData = {
      course: courseId,
      title: req.body.title || req.file.originalname,
      description: req.body.description || '',
      documentType: req.body.documentType || 'course_material',
      filename: req.file.filename,
      originalName: req.file.originalname,
      filePath: req.file.path,
      fileSize: req.file.size,
      mimeType: req.file.mimetype,
      fileExtension: path.extname(req.file.originalname).toLowerCase(),
      uploadedBy: req.user._id,
      visibleToStudents: req.body.visibleToStudents !== 'false',
      downloadable: req.body.downloadable !== 'false',
      displayOrder: parseInt(req.body.displayOrder) || 0,
      tags: req.body.tags ? req.body.tags.split(',').map(tag => tag.trim()) : []
    };

    const document = new CourseDocument(documentData);
    await document.save();

    // Récupérer le document avec les données populées
    const populatedDocument = await CourseDocument.findById(document._id)
      .populate('uploadedBy', 'firstName lastName');

    // Créer une notification pour les étudiants de la classe
    try {
      const Course = require('../models/Course');
      const Notification = require('../models/Notification');

      const course = await Course.findById(courseId);
      if (course && course.class) {
        await Notification.create({
          type: 'document_added',
          title: 'Nouveau document ajouté',
          message: `Un nouveau document "${document.title}" a été ajouté au cours ${course.title}`,
          recipients: {
            roles: ['student', 'admin'],
            classes: [course.class]
          },
          data: {
            courseId: course._id,
            documentId: document._id,
            documentTitle: document.title,
            documentType: document.documentType,
            courseName: course.title,
            className: course.class
          }
        });
        console.log(`📢 Notification créée pour le nouveau document dans la classe ${course.class}`);
      }
    } catch (notificationError) {
      console.error('❌ Erreur lors de la création de la notification:', notificationError);
      // Ne pas faire échouer l'upload pour cette erreur
    }

    res.status(201).json({
      success: true,
      message: 'Document uploadé avec succès',
      document: populatedDocument
    });
  } catch (error) {
    console.error('Erreur lors de l\'upload du document:', error);
    
    // Supprimer le fichier en cas d'erreur
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    if (error.message.includes('Type de fichier non autorisé')) {
      return res.status(400).json({
        success: false,
        message: error.message
      });
    }

    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de l\'upload du document'
    });
  }
});

// GET /api/courses/:courseId/documents/:documentId - Récupérer un document spécifique
router.get('/:courseId/documents/:documentId', authenticate, async (req, res) => {
  try {
    const { courseId, documentId } = req.params;
    
    let query = { _id: documentId, course: courseId, status: 'active' };
    
    // Pour les étudiants, vérifier la visibilité
    if (req.user.role === 'student') {
      query.visibleToStudents = true;
      query.isVisible = true;
    }

    const document = await CourseDocument.findOne(query)
      .populate('uploadedBy', 'firstName lastName');

    if (!document) {
      return res.status(404).json({
        success: false,
        message: 'Document non trouvé'
      });
    }

    res.json({
      success: true,
      document
    });
  } catch (error) {
    console.error('Erreur lors de la récupération du document:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération du document'
    });
  }
});

// GET /api/courses/:courseId/documents/:documentId/download - Télécharger un document
router.get('/:courseId/documents/:documentId/download', authenticate, async (req, res) => {
  try {
    const { courseId, documentId } = req.params;
    
    let query = { _id: documentId, course: courseId, status: 'active' };
    
    // Pour les étudiants, vérifier la visibilité et la possibilité de téléchargement
    if (req.user.role === 'student') {
      query.visibleToStudents = true;
      query.isVisible = true;
      query.downloadable = true;
    }

    const document = await CourseDocument.findOne(query);

    if (!document) {
      return res.status(404).json({
        success: false,
        message: 'Document non trouvé ou non accessible'
      });
    }

    // Vérifier que le fichier existe
    if (!fs.existsSync(document.filePath)) {
      return res.status(404).json({
        success: false,
        message: 'Fichier non trouvé sur le serveur'
      });
    }

    // Incrémenter le compteur de téléchargements
    await document.incrementDownloadCount();

    // Définir les headers pour le téléchargement
    res.setHeader('Content-Disposition', `attachment; filename="${document.originalName}"`);
    res.setHeader('Content-Type', document.mimeType);

    // Envoyer le fichier
    res.sendFile(path.resolve(document.filePath));
  } catch (error) {
    console.error('Erreur lors du téléchargement du document:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors du téléchargement du document'
    });
  }
});

// PUT /api/courses/:courseId/documents/:documentId - Modifier un document
router.put('/:courseId/documents/:documentId', authenticate, authorize('professor', 'admin'), async (req, res) => {
  try {
    const { courseId, documentId } = req.params;

    const document = await CourseDocument.findOne({
      _id: documentId,
      course: courseId,
      status: 'active'
    });

    if (!document) {
      return res.status(404).json({
        success: false,
        message: 'Document non trouvé'
      });
    }

    // Mettre à jour les champs modifiables
    const updateFields = {
      title: req.body.title,
      description: req.body.description,
      documentType: req.body.documentType,
      visibleToStudents: req.body.visibleToStudents !== 'false',
      downloadable: req.body.downloadable !== 'false',
      displayOrder: parseInt(req.body.displayOrder) || document.displayOrder,
      tags: req.body.tags ? req.body.tags.split(',').map(tag => tag.trim()) : document.tags,
      updatedBy: req.user._id
    };

    Object.assign(document, updateFields);
    await document.save();

    const populatedDocument = await CourseDocument.findById(document._id)
      .populate('uploadedBy', 'firstName lastName')
      .populate('updatedBy', 'firstName lastName');

    res.json({
      success: true,
      message: 'Document mis à jour avec succès',
      document: populatedDocument
    });
  } catch (error) {
    console.error('Erreur lors de la mise à jour du document:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la mise à jour du document'
    });
  }
});

// DELETE /api/courses/:courseId/documents/:documentId - Supprimer un document
router.delete('/:courseId/documents/:documentId', authenticate, authorize('professor', 'admin'), async (req, res) => {
  try {
    const { courseId, documentId } = req.params;

    const document = await CourseDocument.findOne({
      _id: documentId,
      course: courseId,
      status: 'active'
    });

    if (!document) {
      return res.status(404).json({
        success: false,
        message: 'Document non trouvé'
      });
    }

    // Marquer comme supprimé au lieu de supprimer physiquement
    document.status = 'deleted';
    document.updatedBy = req.user._id;
    await document.save();

    // Optionnel: supprimer le fichier physique
    if (fs.existsSync(document.filePath)) {
      try {
        fs.unlinkSync(document.filePath);
      } catch (fileError) {
        console.error('Erreur lors de la suppression du fichier:', fileError);
      }
    }

    res.json({
      success: true,
      message: 'Document supprimé avec succès'
    });
  } catch (error) {
    console.error('Erreur lors de la suppression du document:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la suppression du document'
    });
  }
});

// POST /api/courses/:courseId/documents/reorder - Réorganiser l'ordre des documents
router.post('/:courseId/documents/reorder', authenticate, authorize('professor', 'admin'), async (req, res) => {
  try {
    const { courseId } = req.params;
    const { documentIds } = req.body; // Array d'IDs dans le nouvel ordre

    if (!Array.isArray(documentIds)) {
      return res.status(400).json({
        success: false,
        message: 'documentIds doit être un tableau'
      });
    }

    // Mettre à jour l'ordre d'affichage
    const updatePromises = documentIds.map((docId, index) =>
      CourseDocument.findOneAndUpdate(
        { _id: docId, course: courseId, status: 'active' },
        { displayOrder: index, updatedBy: req.user._id }
      )
    );

    await Promise.all(updatePromises);

    res.json({
      success: true,
      message: 'Ordre des documents mis à jour avec succès'
    });
  } catch (error) {
    console.error('Erreur lors de la réorganisation des documents:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la réorganisation des documents'
    });
  }
});

module.exports = router;
