const express = require('express');
const router = express.Router();
const Course = require('../models/Course');
const Professor = require('../models/Professor');
const { authenticate, authorize } = require('../middleware/auth');

// GET /api/courses - Obtenir tous les cours avec filtres pour l'admin
router.get('/', authenticate, async (req, res) => {
  try {
    console.log('📚 Récupération des cours avec filtres:', req.query);

    const { page = 1, limit = 50, academicYear, semester, status, search } = req.query;
    const query = {};

    // Appliquer les filtres
    if (academicYear) query.academicYear = academicYear;
    if (semester) query.semester = semester;
    if (status) query.status = status;

    // Recherche textuelle
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { courseCode: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    const courses = await Course.find(query)
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });

    const total = await Course.countDocuments(query);

    // Récupérer les affectations pour enrichir les données
    const CourseAssignment = require('../models/CourseAssignment');

    // Enrichir les données pour l'interface admin
    const enrichedCourses = await Promise.all(courses.map(async (course) => {
      // Récupérer les affectations pour ce cours
      const assignments = await CourseAssignment.find({ course: course._id })
        .populate({
          path: 'instructors.professor',
          populate: {
            path: 'user',
            select: 'firstName lastName email'
          },
          select: 'user academicTitle department'
        });

      console.log(`📋 Cours ${course.courseCode}: ${assignments.length} affectations trouvées`);

      // Prendre la première affectation active pour compatibilité
      const primaryAssignment = assignments.find(a => a.status === 'active') || assignments[0];
      const primaryInstructor = primaryAssignment?.instructors?.find(i => i.role === 'primary');
      const primaryClass = primaryAssignment?.classes?.[0];

      // Debug des données du professeur
      if (primaryInstructor) {
        console.log(`👨‍🏫 Professeur principal trouvé:`, {
          professorId: primaryInstructor.professor?._id,
          user: primaryInstructor.professor?.user,
          role: primaryInstructor.role
        });
      }

      return {
        id: course._id,
        _id: course._id,
        code: course.courseCode,
        courseCode: course.courseCode,
        title: course.title,
        description: course.description,
        credits: course.creditHours,
        creditHours: course.creditHours,
        contactHours: course.contactHours,
        hours: course.contactHours,
        semester: course.semester,
        status: primaryAssignment ? 'active' : 'draft',
        promotion: course.promotion,
        class: primaryClass?.className || null,
        professor: primaryInstructor?.professor?.user ? {
          id: primaryInstructor.professor._id,
          name: `${primaryInstructor.professor.user.firstName} ${primaryInstructor.professor.user.lastName}`,
          email: primaryInstructor.professor.user.email
        } : null,
        instructor: primaryInstructor?.professor || null,
        enrolledStudents: 0, // TODO: Implémenter la logique d'inscription
        maxStudents: course.capacity?.maximum || 20,
        capacity: course.capacity,
        startDate: course.startDate,
        endDate: course.endDate,
        schedule: primaryAssignment?.schedule?.timeSlots || [],
        assignments: assignments.length,
        allInstructors: assignments.flatMap(a =>
          a.instructors?.map(i => ({
            name: i.professor?.user ?
              `${i.professor.user.firstName} ${i.professor.user.lastName}` :
              'Nom non disponible',
            role: i.role
          })) || []
        )
      };
    }));

    console.log(`✅ ${enrichedCourses.length} cours récupérés`);

    res.json({
      success: true,
      courses: enrichedCourses,
      totalPages: Math.ceil(total / limit),
      currentPage: parseInt(page),
      total
    });
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des cours:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des cours',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// GET /api/courses/:id
router.get('/:id', async (req, res) => {
  try {
    const course = await Course.findById(req.params.id)
      .populate('instructor', 'user academicTitle department')
      .populate('coInstructors', 'user academicTitle department')
      .populate('prerequisites.course', 'courseCode title');
    
    if (!course) {
      return res.status(404).json({ message: 'Course not found' });
    }
    
    res.json(course);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// POST /api/courses - Créer un nouveau cours pour toutes les classes d'une promotion
router.post('/', authenticate, authorize('admin'), async (req, res) => {
  try {
    console.log('📚 Création de cours pour toutes les classes:', req.body);

    // Validation des données requises
    const { courseCode, title, description, promotion } = req.body;

    if (!courseCode || !title || !description || !promotion) {
      return res.status(400).json({
        success: false,
        message: 'Données manquantes: courseCode, title, description et promotion sont requis'
      });
    }

    // Vérifier qu'aucun cours avec ce code de base n'existe déjà
    const existingCourse = await Course.findOne({
      courseCode: { $regex: `^${courseCode}` }
    });
    if (existingCourse) {
      return res.status(400).json({
        success: false,
        message: 'Un cours avec ce code existe déjà'
      });
    }

    // Préparer les données de base du cours
    const baseCourseData = {
      ...req.body,
      status: 'draft', // Draft jusqu'à affectation de professeur
      registrationStatus: 'open', // Ouvert pour inscription automatique
      capacity: {
        maximum: req.body.capacity?.maximum || 30,
        minimum: req.body.capacity?.minimum || 10,
        current: 0,
        waitingList: 0
      },
      startDate: req.body.startDate || new Date(),
      endDate: req.body.endDate || new Date(Date.now() + 90 * 24 * 60 * 60 * 1000) // 3 mois
    };

    // Utiliser la méthode statique pour créer des cours pour toutes les classes
    const createdCourses = await Course.createCourseForAllClasses(baseCourseData);

    console.log(`✅ ${createdCourses.length} cours créé(s) avec succès`);
    res.status(201).json({
      success: true,
      message: `${createdCourses.length} cours créé(s) avec succès`,
      courses: createdCourses,
      count: createdCourses.length
    });
  } catch (error) {
    console.error('❌ Erreur lors de la création des cours:', error);
    res.status(400).json({
      success: false,
      message: error.message
    });
  }
});

// PUT /api/courses/:id - Modifier un cours
router.put('/:id', async (req, res) => {
  try {
    console.log('📝 Modification du cours:', req.params.id, req.body);

    // Vérifier que le cours existe
    const existingCourse = await Course.findById(req.params.id);
    if (!existingCourse) {
      return res.status(404).json({ message: 'Cours non trouvé' });
    }

    // Si le code de cours change, vérifier l'unicité
    if (req.body.courseCode && req.body.courseCode !== existingCourse.courseCode) {
      const duplicateCourse = await Course.findOne({
        courseCode: req.body.courseCode,
        _id: { $ne: req.params.id }
      });
      if (duplicateCourse) {
        return res.status(400).json({ message: 'Un cours avec ce code existe déjà' });
      }
    }

    // Si le professeur change, vérifier qu'il existe
    if (req.body.instructor) {
      const Professor = require('../models/Professor');
      const professorExists = await Professor.findById(req.body.instructor);
      if (!professorExists) {
        return res.status(400).json({ message: 'Professeur non trouvé' });
      }
    }

    const course = await Course.findByIdAndUpdate(
      req.params.id,
      {
        ...req.body,
        updatedBy: req.body.updatedBy,
        lastModified: new Date()
      },
      { new: true, runValidators: true }
    ).populate('instructor', 'user academicTitle department employeeNumber')
     .populate('instructor.user', 'firstName lastName email');

    console.log('✅ Cours modifié avec succès:', course.courseCode);
    res.json(course);
  } catch (error) {
    console.error('❌ Erreur lors de la modification du cours:', error);
    res.status(400).json({ message: error.message });
  }
});

// GET /api/courses/:id/students
router.get('/:id/students', async (req, res) => {
  try {
    const course = await Course.findById(req.params.id);

    if (!course) {
      return res.status(404).json({ message: 'Course not found' });
    }

    const students = await course.getEnrolledStudents();
    res.json(students);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// GET /api/courses/student/:studentId - Récupérer les cours d'un étudiant
router.get('/student/:studentId', authenticate, async (req, res) => {
  try {
    const { studentId } = req.params;
    const Enrollment = require('../models/Enrollment');
    const Student = require('../models/Student');

    // Récupérer l'étudiant pour obtenir sa classe
    const student = await Student.findById(studentId);
    if (!student) {
      return res.status(404).json({
        success: false,
        message: 'Étudiant non trouvé'
      });
    }

    // Récupérer les cours via les inscriptions
    const enrollments = await Enrollment.find({ student: studentId })
      .populate({
        path: 'course',
        populate: {
          path: 'instructor',
          populate: {
            path: 'user',
            select: 'firstName lastName email'
          }
        }
      })
      .sort({ enrollmentDate: -1 });

    const courses = enrollments.map(enrollment => ({
      ...enrollment.course.toObject(),
      enrollmentStatus: enrollment.status,
      enrollmentDate: enrollment.enrollmentDate,
      grade: enrollment.finalGrade
    }));

    res.json({
      success: true,
      courses,
      studentClass: student.class,
      count: courses.length
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des cours de l\'étudiant:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur'
    });
  }
});

// GET /api/courses/class/:className - Récupérer les cours d'une classe
router.get('/class/:className', authenticate, async (req, res) => {
  try {
    const { className } = req.params;

    const courses = await Course.find({ class: className })
      .populate('instructor', 'user academicTitle department')
      .populate('instructor.user', 'firstName lastName email')
      .sort({ createdAt: -1 });

    res.json({
      success: true,
      courses,
      className,
      count: courses.length
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des cours de la classe:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur'
    });
  }
});

// DELETE /api/courses/:id - Supprimer un cours
router.delete('/:id', async (req, res) => {
  try {
    console.log('🗑️ Suppression du cours:', req.params.id);

    const course = await Course.findById(req.params.id);
    if (!course) {
      return res.status(404).json({ message: 'Cours non trouvé' });
    }

    // Vérifier s'il y a des étudiants inscrits
    if (course.capacity && course.capacity.current > 0) {
      return res.status(400).json({
        message: 'Impossible de supprimer un cours avec des étudiants inscrits'
      });
    }

    // Supprimer le cours
    await Course.findByIdAndDelete(req.params.id);

    console.log('✅ Cours supprimé avec succès:', course.courseCode);
    res.json({ message: 'Cours supprimé avec succès' });
  } catch (error) {
    console.error('❌ Erreur lors de la suppression du cours:', error);
    res.status(500).json({ message: error.message });
  }
});

// POST /api/courses/bulk-create - Créer plusieurs cours pour un module
router.post('/bulk-create', async (req, res) => {
  try {
    console.log('📚 Création en masse de cours:', req.body);

    const { moduleId, promotion, professorAssignments } = req.body;

    if (!moduleId || !promotion) {
      return res.status(400).json({
        message: 'moduleId et promotion sont requis'
      });
    }

    // Utiliser la méthode statique du modèle Course
    const createdCourses = await Course.createModuleCourses(moduleId, promotion);

    // Affecter les professeurs si fournis
    if (professorAssignments && Array.isArray(professorAssignments)) {
      for (let assignment of professorAssignments) {
        const { className, professorId } = assignment;
        if (className && professorId) {
          await Course.findOneAndUpdate(
            { module: moduleId, class: className },
            {
              instructor: professorId,
              status: 'active',
              registrationStatus: 'open'
            }
          );
        }
      }
    }

    console.log('✅ Cours créés en masse:', createdCourses.length);
    res.status(201).json({
      message: `${createdCourses.length} cours créés avec succès`,
      courses: createdCourses
    });
  } catch (error) {
    console.error('❌ Erreur lors de la création en masse:', error);
    res.status(400).json({ message: error.message });
  }
});

// GET /api/courses/:id/assignments - Récupérer toutes les affectations d'un cours
router.get('/:id/assignments', async (req, res) => {
  try {
    console.log('📋 Récupération des affectations pour le cours:', req.params.id);

    const CourseAssignment = require('../models/CourseAssignment');
    const assignments = await CourseAssignment.find({ course: req.params.id })
      .populate('course', 'courseCode title')
      .populate({
        path: 'instructors.professor',
        populate: {
          path: 'user',
          select: 'firstName lastName email'
        },
        select: 'user academicTitle department'
      })
      .sort({ createdAt: -1 });

    // Debug pour voir la structure des données
    assignments.forEach((assignment, index) => {
      console.log(`📋 Affectation ${index + 1}:`);
      assignment.instructors.forEach((instructor, idx) => {
        console.log(`  👨‍🏫 Professeur ${idx + 1}:`, {
          professorId: instructor.professor?._id,
          user: instructor.professor?.user,
          role: instructor.role
        });
      });
    });

    console.log(`✅ ${assignments.length} affectations trouvées`);

    res.json({
      success: true,
      assignments: assignments
    });
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des affectations:', error);
    res.status(500).json({ message: error.message });
  }
});

// DELETE /api/courses/assignments/:id - Supprimer une affectation
router.delete('/assignments/:id', async (req, res) => {
  try {
    console.log('🗑️ Suppression de l\'affectation:', req.params.id);

    const CourseAssignment = require('../models/CourseAssignment');
    const assignment = await CourseAssignment.findByIdAndDelete(req.params.id);

    if (!assignment) {
      return res.status(404).json({ message: 'Affectation non trouvée' });
    }

    console.log('✅ Affectation supprimée avec succès');

    res.json({
      success: true,
      message: 'Affectation supprimée avec succès'
    });
  } catch (error) {
    console.error('❌ Erreur lors de la suppression de l\'affectation:', error);
    res.status(500).json({ message: error.message });
  }
});

// POST /api/courses/:id/assign - Créer une nouvelle affectation pour un cours
router.post('/:id/assign', async (req, res) => {
  try {
    console.log('👨‍🏫 Nouvelle affectation pour le cours:', req.params.id, req.body);

    const { instructors, classes, startDate, timeSlot } = req.body;

    // Validation des données
    if (!instructors || !Array.isArray(instructors) || instructors.length === 0) {
      return res.status(400).json({
        message: 'Au moins un professeur est requis'
      });
    }

    if (!classes || !Array.isArray(classes) || classes.length === 0) {
      return res.status(400).json({
        message: 'Au moins une classe est requise'
      });
    }

    if (!startDate) {
      return res.status(400).json({
        message: 'Date de début est requise'
      });
    }

    if (!timeSlot) {
      return res.status(400).json({
        message: 'Créneau horaire est requis'
      });
    }

    // Vérifier que le cours existe
    const course = await Course.findById(req.params.id);
    if (!course) {
      return res.status(404).json({ message: 'Cours non trouvé' });
    }

    // Vérifier que tous les professeurs existent
    const Professor = require('../models/Professor');
    const professorIds = instructors.map(i => i.professorId || i);
    const professors = await Professor.find({ _id: { $in: professorIds } }).populate('user', 'firstName lastName email');

    if (professors.length !== professorIds.length) {
      return res.status(400).json({ message: 'Un ou plusieurs professeurs non trouvés' });
    }

    // Vérifier que toutes les classes correspondent à la promotion du cours
    for (const classInfo of classes) {
      const className = classInfo.className || classInfo;
      const classPromotion = className.substring(0, 5); // EMBA1 ou EMBA2
      if (classPromotion !== course.promotion) {
        return res.status(400).json({
          message: `La classe ${className} ne correspond pas à la promotion ${course.promotion} du cours`
        });
      }
    }

    // Créer l'affectation
    const CourseAssignment = require('../models/CourseAssignment');
    const assignment = new CourseAssignment({
      course: course._id,
      instructors: instructors.map(instructor => ({
        professor: instructor.professorId || instructor,
        role: instructor.role || 'primary',
        hoursAssigned: instructor.hoursAssigned || 0
      })),
      classes: classes.map(classInfo => ({
        className: classInfo.className || classInfo,
        promotion: course.promotion,
        maxStudents: classInfo.maxStudents || 30
      })),
      schedule: {
        startDate: new Date(startDate),
        durationWeeks: 15, // Par défaut
        hoursPerWeek: 3,   // Par défaut
        timeSlots: [{
          dayOfWeek: timeSlot.dayOfWeek,
          startTime: timeSlot.startTime,
          endTime: timeSlot.endTime,
          location: timeSlot.location || {}
        }]
      },
      status: 'active',
      createdBy: req.user?.id
    });

    // Calculer les dates automatiquement
    assignment.calculateScheduleDates();

    // Détecter les conflits
    const conflicts = await assignment.detectConflicts();

    // Séparer les conflits critiques des avertissements
    const criticalConflicts = conflicts.filter(c => c.severity === 'critical');
    const warnings = conflicts.filter(c => c.severity === 'warning');

    if (criticalConflicts.length > 0) {
      // Générer des suggestions d'horaires alternatifs
      const suggestions = await generateAlternativeTimeSlots(timeSlot, classes, instructors);

      return res.status(409).json({
        message: 'Conflits critiques détectés',
        conflicts: criticalConflicts,
        warnings: warnings,
        suggestions: suggestions
      });
    }

    // Sauvegarder l'affectation avec avertissements s'il y en a
    await assignment.save();

    // Créer des notifications pour les nouvelles affectations
    try {
      const Notification = require('../models/Notification');

      for (const classInfo of classes) {
        const className = classInfo.className || classInfo;

        await Notification.create({
          type: 'course_assignment',
          title: 'Nouveau professeur assigné',
          message: `Un professeur a été assigné au cours "${course.title}" pour votre classe`,
          recipients: {
            roles: ['student'],
            classes: [className]
          },
          data: {
            courseId: course._id,
            courseName: course.title,
            className: className,
            professors: professors.map(p => `${p.user.firstName} ${p.user.lastName}`)
          }
        });
      }
    } catch (notificationError) {
      console.error('❌ Erreur lors de la création des notifications:', notificationError);
    }

    // Populer les données pour la réponse
    const populatedAssignment = await CourseAssignment.findById(assignment._id)
      .populate('course', 'courseCode title description')
      .populate('instructors.professor', 'user academicTitle department')
      .populate('instructors.professor.user', 'firstName lastName email');

    console.log('✅ Affectation créée avec succès pour le cours:', course.courseCode);

    // Préparer la réponse avec les avertissements s'il y en a
    const response = {
      message: warnings.length > 0
        ? `Affectation créée avec ${warnings.length} avertissement(s)`
        : 'Affectation créée avec succès',
      assignment: populatedAssignment
    };

    if (warnings.length > 0) {
      response.warnings = warnings;
    }

    res.status(201).json(response);
  } catch (error) {
    console.error('❌ Erreur lors de la création de l\'affectation:', error);
    res.status(400).json({ message: error.message });
  }
});

// Fonction utilitaire pour vérifier les conflits d'horaires
async function checkScheduleConflicts(instructorId, className, newSchedule, excludeCourseId = null) {
  const conflicts = [];
  const Schedule = require('../models/Schedule');

  for (const newSlot of newSchedule) {
    // Vérifier les conflits avec les autres cours du professeur
    const professorSchedules = await Schedule.find({
      instructor: instructorId,
      dayOfWeek: newSlot.dayOfWeek,
      status: 'scheduled',
      course: { $ne: excludeCourseId }
    }).populate('course', 'courseCode title');

    for (const schedule of professorSchedules) {
      if (isTimeConflict(newSlot, { startTime: schedule.startTime, endTime: schedule.endTime })) {
        conflicts.push({
          type: 'professor',
          conflictWith: schedule.course.courseCode,
          message: `Conflit avec le cours ${schedule.course.courseCode} du professeur le ${newSlot.dayOfWeek} de ${schedule.startTime} à ${schedule.endTime}`
        });
      }
    }

    // Vérifier les conflits avec les autres cours de la classe
    const classSchedules = await Schedule.find({
      'targetAudience.classes': className,
      dayOfWeek: newSlot.dayOfWeek,
      status: 'scheduled',
      course: { $ne: excludeCourseId }
    }).populate('course', 'courseCode title');

    for (const schedule of classSchedules) {
      if (isTimeConflict(newSlot, { startTime: schedule.startTime, endTime: schedule.endTime })) {
        conflicts.push({
          type: 'class',
          conflictWith: schedule.course.courseCode,
          message: `Conflit avec le cours ${schedule.course.courseCode} de la classe ${className} le ${newSlot.dayOfWeek} de ${schedule.startTime} à ${schedule.endTime}`
        });
      }
    }

    // Vérifier aussi les conflits dans les cours (pour compatibilité)
    const professorCourses = await Course.find({
      instructor: instructorId,
      _id: { $ne: excludeCourseId },
      status: { $in: ['active', 'draft'] }
    });

    const classCourses = await Course.find({
      class: className,
      _id: { $ne: excludeCourseId },
      status: { $in: ['active', 'draft'] }
    });

    // Vérifier conflits professeur dans les cours
    for (const course of professorCourses) {
      for (const existingSlot of course.schedule || []) {
        if (isTimeConflict(newSlot, existingSlot)) {
          conflicts.push({
            type: 'professor',
            conflictWith: course.courseCode,
            message: `Conflit avec le cours ${course.courseCode} du professeur le ${newSlot.dayOfWeek} de ${newSlot.startTime} à ${newSlot.endTime}`
          });
        }
      }
    }

    // Vérifier conflits classe dans les cours
    for (const course of classCourses) {
      for (const existingSlot of course.schedule || []) {
        if (isTimeConflict(newSlot, existingSlot)) {
          conflicts.push({
            type: 'class',
            conflictWith: course.courseCode,
            message: `Conflit avec le cours ${course.courseCode} de la classe ${className} le ${newSlot.dayOfWeek} de ${newSlot.startTime} à ${newSlot.endTime}`
          });
        }
      }
    }
  }

  return conflicts;
}

// Fonction utilitaire pour vérifier si deux créneaux sont en conflit
function isTimeConflict(slot1, slot2) {
  if (slot1.dayOfWeek !== slot2.dayOfWeek) return false;

  const start1 = timeToMinutes(slot1.startTime);
  const end1 = timeToMinutes(slot1.endTime);
  const start2 = timeToMinutes(slot2.startTime);
  const end2 = timeToMinutes(slot2.endTime);

  return (start1 < end2 && end1 > start2);
}

// Fonction utilitaire pour convertir HH:MM en minutes
function timeToMinutes(timeStr) {
  const [hours, minutes] = timeStr.split(':').map(Number);
  return hours * 60 + minutes;
}

// Fonction utilitaire pour créer les entrées de planning
async function createClassScheduleEntries(course) {
  try {
    console.log('📅 Création des entrées de planning pour:', course.courseCode, 'classe:', course.class);

    const Schedule = require('../models/Schedule');
    const createdEntries = await Schedule.createCourseSchedule(course);

    console.log(`✅ ${createdEntries.length} entrées de planning créées pour le cours ${course.courseCode}`);
    return createdEntries;
  } catch (error) {
    console.error('❌ Erreur lors de la création du planning:', error);
    throw error;
  }
}

// GET /api/courses/schedule/class/:className - Obtenir le planning d'une classe
router.get('/schedule/class/:className', async (req, res) => {
  try {
    const { className } = req.params;
    const { startDate, endDate } = req.query;

    const Schedule = require('../models/Schedule');

    // Dates par défaut (semaine courante)
    const start = startDate ? new Date(startDate) : new Date();
    const end = endDate ? new Date(endDate) : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);

    const schedule = await Schedule.find({
      'targetAudience.classes': className,
      status: 'scheduled',
      startDate: { $lte: end },
      endDate: { $gte: start }
    })
    .populate('course', 'courseCode title description')
    .populate('instructor', 'user academicTitle department')
    .populate('instructor.user', 'firstName lastName')
    .sort({ dayOfWeek: 1, startTime: 1 });

    console.log(`📅 Planning récupéré pour la classe ${className}: ${schedule.length} entrées`);

    res.json({
      success: true,
      schedule: schedule,
      class: className,
      period: { startDate: start, endDate: end }
    });
  } catch (error) {
    console.error('❌ Erreur lors de la récupération du planning de classe:', error);
    res.status(500).json({ message: error.message });
  }
});

// GET /api/courses/schedule/professor/:professorId - Obtenir le planning d'un professeur
router.get('/schedule/professor/:professorId', async (req, res) => {
  try {
    const { professorId } = req.params;
    const { startDate, endDate } = req.query;

    const Schedule = require('../models/Schedule');

    // Dates par défaut (semaine courante)
    const start = startDate ? new Date(startDate) : new Date();
    const end = endDate ? new Date(endDate) : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);

    const schedule = await Schedule.find({
      instructor: professorId,
      status: 'scheduled',
      startDate: { $lte: end },
      endDate: { $gte: start }
    })
    .populate('course', 'courseCode title description')
    .sort({ dayOfWeek: 1, startTime: 1 });

    console.log(`📅 Planning récupéré pour le professeur ${professorId}: ${schedule.length} entrées`);

    res.json({
      success: true,
      schedule: schedule,
      professorId: professorId,
      period: { startDate: start, endDate: end }
    });
  } catch (error) {
    console.error('❌ Erreur lors de la récupération du planning du professeur:', error);
    res.status(500).json({ message: error.message });
  }
});

// Fonction pour générer des suggestions d'horaires alternatifs
async function generateAlternativeTimeSlots(originalTimeSlot, classes, instructors) {
  const suggestions = [];
  const CourseAssignment = require('../models/CourseAssignment');

  // Créneaux horaires possibles
  const timeSlots = [
    { start: '08:00', end: '11:15', label: 'Matin (8h-11h15)' },
    { start: '09:00', end: '12:15', label: 'Matinée (9h-12h15)' },
    { start: '13:30', end: '16:45', label: 'Après-midi (13h30-16h45)' },
    { start: '14:00', end: '17:15', label: 'Après-midi tardif (14h-17h15)' },
    { start: '17:30', end: '20:45', label: 'Soirée (17h30-20h45)' }
  ];

  const daysOfWeek = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

  for (const day of daysOfWeek) {
    for (const slot of timeSlots) {
      // Éviter le créneau original
      if (day === originalTimeSlot.dayOfWeek &&
          slot.start === originalTimeSlot.startTime) {
        continue;
      }

      // Vérifier si ce créneau est libre pour tous les professeurs et classes
      let isAvailable = true;

      // Vérifier les conflits professeurs
      for (const instructor of instructors) {
        const professorId = instructor.professorId || instructor;
        const conflicts = await CourseAssignment.find({
          'instructors.professor': professorId,
          'schedule.timeSlots': {
            $elemMatch: {
              dayOfWeek: day,
              startTime: { $lt: slot.end },
              endTime: { $gt: slot.start }
            }
          },
          status: { $in: ['active', 'draft'] }
        });

        if (conflicts.length > 0) {
          isAvailable = false;
          break;
        }
      }

      // Vérifier les conflits classes
      if (isAvailable) {
        for (const classInfo of classes) {
          const className = classInfo.className || classInfo;
          const conflicts = await CourseAssignment.find({
            'classes.className': className,
            'schedule.timeSlots': {
              $elemMatch: {
                dayOfWeek: day,
                startTime: { $lt: slot.end },
                endTime: { $gt: slot.start }
              }
            },
            status: { $in: ['active', 'draft'] }
          });

          if (conflicts.length > 0) {
            isAvailable = false;
            break;
          }
        }
      }

      if (isAvailable) {
        suggestions.push({
          dayOfWeek: day,
          startTime: slot.start,
          endTime: slot.end,
          label: `${getDayLabel(day)} ${slot.label}`,
          location: originalTimeSlot.location // Garder la même salle si possible
        });

        // Limiter à 5 suggestions
        if (suggestions.length >= 5) {
          return suggestions;
        }
      }
    }
  }

  return suggestions;
}

// Fonction utilitaire pour les labels des jours
function getDayLabel(day) {
  const labels = {
    'Monday': 'Lundi',
    'Tuesday': 'Mardi',
    'Wednesday': 'Mercredi',
    'Thursday': 'Jeudi',
    'Friday': 'Vendredi',
    'Saturday': 'Samedi'
  };
  return labels[day] || day;
}

module.exports = router;
