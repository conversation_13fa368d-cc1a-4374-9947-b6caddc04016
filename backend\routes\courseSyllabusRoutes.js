const express = require('express');
const router = express.Router();
const CourseSyllabus = require('../models/CourseSyllabus');
const Course = require('../models/Course');
const { authenticate, authorize } = require('../middleware/auth');

// GET /api/courses/:courseId/syllabus - Récupérer le syllabus d'un cours
router.get('/:courseId/syllabus', authenticate, async (req, res) => {
  try {
    const { courseId } = req.params;
    
    // Vérifier que le cours existe
    const course = await Course.findById(courseId);
    if (!course) {
      return res.status(404).json({
        success: false,
        message: 'Cours non trouvé'
      });
    }

    // Récupérer le syllabus
    const syllabus = await CourseSyllabus.findOne({ course: courseId })
      .populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email')
      .populate('course', 'title courseCode');

    if (!syllabus) {
      return res.status(404).json({
        success: false,
        message: 'Aucun syllabus trouvé pour ce cours'
      });
    }

    res.json({
      success: true,
      syllabus
    });
  } catch (error) {
    console.error('Erreur lors de la récupération du syllabus:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération du syllabus'
    });
  }
});

// POST /api/courses/:courseId/syllabus - Créer un syllabus pour un cours
router.post('/:courseId/syllabus', authenticate, authorize('professor', 'admin'), async (req, res) => {
  try {
    const { courseId } = req.params;
    
    // Vérifier que le cours existe
    const course = await Course.findById(courseId);
    if (!course) {
      return res.status(404).json({
        success: false,
        message: 'Cours non trouvé'
      });
    }

    // Vérifier si un syllabus existe déjà
    const existingSyllabus = await CourseSyllabus.findOne({ course: courseId });
    if (existingSyllabus) {
      return res.status(400).json({
        success: false,
        message: 'Un syllabus existe déjà pour ce cours. Utilisez PUT pour le modifier.'
      });
    }

    // Créer le nouveau syllabus
    const syllabusData = {
      ...req.body,
      course: courseId,
      createdBy: req.user._id
    };

    const syllabus = new CourseSyllabus(syllabusData);
    
    // Initialiser l'échelle de notation par défaut si non fournie
    if (!syllabus.evaluations?.gradingScale || syllabus.evaluations.gradingScale.length === 0) {
      syllabus.initializeDefaultGradingScale();
    }

    await syllabus.save();

    // Récupérer le syllabus avec les données populées
    const populatedSyllabus = await CourseSyllabus.findById(syllabus._id)
      .populate('createdBy', 'firstName lastName email')
      .populate('course', 'title courseCode class');

    // Créer une notification pour les étudiants de la classe
    try {
      const Notification = require('../models/Notification');

      if (populatedSyllabus.course && populatedSyllabus.course.class) {
        await Notification.create({
          type: 'syllabus_published',
          title: 'Nouveau syllabus publié',
          message: `Le syllabus du cours "${populatedSyllabus.course.title}" est maintenant disponible`,
          recipients: {
            roles: ['student', 'admin'],
            classes: [populatedSyllabus.course.class]
          },
          data: {
            courseId: populatedSyllabus.course._id,
            syllabusId: populatedSyllabus._id,
            courseName: populatedSyllabus.course.title,
            courseCode: populatedSyllabus.course.courseCode,
            className: populatedSyllabus.course.class
          }
        });
        console.log(`📢 Notification créée pour le nouveau syllabus dans la classe ${populatedSyllabus.course.class}`);
      }
    } catch (notificationError) {
      console.error('❌ Erreur lors de la création de la notification:', notificationError);
      // Ne pas faire échouer la création pour cette erreur
    }

    res.status(201).json({
      success: true,
      message: 'Syllabus créé avec succès',
      syllabus: populatedSyllabus
    });
  } catch (error) {
    console.error('Erreur lors de la création du syllabus:', error);
    
    if (error.message.includes('poids d\'évaluation')) {
      return res.status(400).json({
        success: false,
        message: error.message
      });
    }

    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la création du syllabus'
    });
  }
});

// PUT /api/courses/:courseId/syllabus - Modifier le syllabus d'un cours
router.put('/:courseId/syllabus', authenticate, authorize('professor', 'admin'), async (req, res) => {
  try {
    const { courseId } = req.params;
    
    // Vérifier que le cours existe
    const course = await Course.findById(courseId);
    if (!course) {
      return res.status(404).json({
        success: false,
        message: 'Cours non trouvé'
      });
    }

    // Trouver le syllabus existant
    const syllabus = await CourseSyllabus.findOne({ course: courseId });
    if (!syllabus) {
      return res.status(404).json({
        success: false,
        message: 'Aucun syllabus trouvé pour ce cours'
      });
    }

    // Mettre à jour les données
    Object.assign(syllabus, req.body);
    syllabus.updatedBy = req.user._id;
    syllabus.version += 1;

    await syllabus.save();

    // Récupérer le syllabus avec les données populées
    const populatedSyllabus = await CourseSyllabus.findById(syllabus._id)
      .populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email')
      .populate('course', 'title courseCode');

    res.json({
      success: true,
      message: 'Syllabus mis à jour avec succès',
      syllabus: populatedSyllabus
    });
  } catch (error) {
    console.error('Erreur lors de la mise à jour du syllabus:', error);
    
    if (error.message.includes('poids d\'évaluation')) {
      return res.status(400).json({
        success: false,
        message: error.message
      });
    }

    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la mise à jour du syllabus'
    });
  }
});

// DELETE /api/courses/:courseId/syllabus - Supprimer le syllabus d'un cours
router.delete('/:courseId/syllabus', authenticate, authorize('professor', 'admin'), async (req, res) => {
  try {
    const { courseId } = req.params;
    
    // Trouver et supprimer le syllabus
    const syllabus = await CourseSyllabus.findOneAndDelete({ course: courseId });
    if (!syllabus) {
      return res.status(404).json({
        success: false,
        message: 'Aucun syllabus trouvé pour ce cours'
      });
    }

    res.json({
      success: true,
      message: 'Syllabus supprimé avec succès'
    });
  } catch (error) {
    console.error('Erreur lors de la suppression du syllabus:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la suppression du syllabus'
    });
  }
});

// GET /api/courses/:courseId/syllabus/preview - Aperçu du syllabus pour les étudiants
router.get('/:courseId/syllabus/preview', authenticate, async (req, res) => {
  try {
    const { courseId } = req.params;
    
    const syllabus = await CourseSyllabus.findOne({ course: courseId, isActive: true })
      .populate('course', 'title courseCode')
      .select('-createdBy -updatedBy -version -__v');

    if (!syllabus) {
      return res.status(404).json({
        success: false,
        message: 'Aucun syllabus disponible pour ce cours'
      });
    }

    res.json({
      success: true,
      syllabus
    });
  } catch (error) {
    console.error('Erreur lors de la récupération de l\'aperçu du syllabus:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération de l\'aperçu du syllabus'
    });
  }
});

module.exports = router;
