const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const pdf = require('pdf-parse');
const mammoth = require('mammoth');

// Configuration multer pour l'upload de CV
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = 'uploads/cv-temp';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'cv-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB max
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['.pdf', '.doc', '.docx'];
    const fileExt = path.extname(file.originalname).toLowerCase();
    
    if (allowedTypes.includes(fileExt)) {
      cb(null, true);
    } else {
      cb(new Error('Type de fichier non supporté. Utilisez PDF, DOC ou DOCX.'));
    }
  }
});

// Fonction pour extraire le texte selon le type de fichier
const extractTextFromFile = async (filePath, fileType) => {
  try {
    if (fileType === '.pdf') {
      const dataBuffer = fs.readFileSync(filePath);
      const data = await pdf(dataBuffer);
      return data.text;
    } else if (fileType === '.docx') {
      const result = await mammoth.extractRawText({ path: filePath });
      return result.value;
    } else if (fileType === '.doc') {
      // Pour les fichiers .doc, nous utiliserons mammoth aussi (limité)
      const result = await mammoth.extractRawText({ path: filePath });
      return result.value;
    }
    throw new Error('Type de fichier non supporté');
  } catch (error) {
    console.error('Erreur extraction texte:', error);
    throw error;
  }
};

// Fonction d'analyse intelligente du CV (multilingue FR/EN)
const analyzeCvText = (text) => {
  console.log('🔍 Texte brut reçu:', text.substring(0, 500));

  // Nettoyer le texte d'abord
  let cleanedText = text
    .replace(/[^\w\s@.\-+()]/g, ' ') // Supprimer caractères spéciaux sauf email/phone
    .replace(/\s+/g, ' ') // Normaliser les espaces
    .trim();

  console.log('🧹 Texte nettoyé:', cleanedText.substring(0, 500));

  // Diviser intelligemment le texte en lignes basé sur les mots-clés de sections
  const sectionKeywords = [
    'PROFESSIONAL EXPERIENCE', 'PROFESSIONALEXPERIENCE', 'EXPERIENCE',
    'Exp riences Professionnelles', 'Exp riences',
    'EDUCATION', 'ducation', 'ACADEMIC PROJECTS', 'ACADEMICPROJECTS',
    'SKILLS', 'Technologies', 'Langages', 'Outils',
    'PROFILE', 'Profil', 'LANGUAGES',
    'COMMUNITY INVOLVEMENT', 'COMMUNITYINVOLVEMENT', 'Vie Associative',
    'INTERESTS'
  ];

  // Diviser le texte aux points de section
  let processedText = cleanedText;
  sectionKeywords.forEach(keyword => {
    processedText = processedText.replace(new RegExp(keyword, 'gi'), `\n${keyword}\n`);
  });

  // Diviser aux entreprises/institutions connues (Achraf)
  const achrafKeywords = [
    'EBUILD', 'ORANGETN', 'TEKRU', 'OACA',
    'PrivateHigherSchoolofEngineeringandTech', 'ISTIC',
    'AbouElKacemChebbiHighSchool'
  ];

  achrafKeywords.forEach(keyword => {
    processedText = processedText.replace(new RegExp(keyword, 'gi'), `\n${keyword}`);
  });

  // Diviser aux entreprises/institutions d'Oussama
  const oussamaKeywords = [
    'ACTIA ES', 'STMicroelectronics', 'SAGEMCOM', 'ENIS', 'IPEIM',
    'Ing nieur de Test Logiciel', 'Freelance', 'Stagiaire PFE',
    'Dipl me d Ing nieur', 'Pr pa Scientifique', 'Baccalaur at'
  ];

  oussamaKeywords.forEach(keyword => {
    processedText = processedText.replace(new RegExp(keyword, 'gi'), `\n${keyword}`);
  });

  // Diviser aux dates (format français et anglais)
  processedText = processedText.replace(/(\w+\s+\d{4})/g, '\n$1');
  processedText = processedText.replace(/(Juillet|Janvier|F vrier|Mars|Avril|Mai|Juin|Septembre|Octobre|Novembre|D cembre)\s+\d{4}/g, '\n$&');
  processedText = processedText.replace(/(Pr sent|Present)/g, '\n$&');

  // Diviser aux projets
  processedText = processedText.replace(/PFA\s+/g, '\nPFA ');
  processedText = processedText.replace(/Projet\s+/g, '\nProjet ');

  console.log('📝 Texte restructuré:', processedText.substring(0, 800));

  const lines = processedText.split(/[\n\r]+/).map(line => line.trim()).filter(line => line.length > 2);
  console.log('📋 Nombre de lignes après restructuration:', lines.length);

  const result = {
    personalInfo: {},
    education: [],
    experience: [],
    languages: [],
    skills: []
  };

  // Expressions régulières améliorées pour l'extraction
  const emailRegex = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g;
  const phoneRegex = /(?:\+\d{1,3}[\s\-]?)?\d{8,15}/g;
  const dateRegex = /\b(?:19|20)\d{2}\b/g;
  const yearRangeRegex = /(\d{4})\s*[-–—]\s*(\d{4}|\w+)/g;

  // Extraction email
  const emails = cleanedText.match(emailRegex);
  if (emails && emails.length > 0) {
    result.personalInfo.email = emails[0];
  }

  // Extraction téléphone améliorée
  const phones = cleanedText.match(phoneRegex);
  if (phones && phones.length > 0) {
    // Prendre le premier numéro qui ressemble à un téléphone
    let bestPhone = phones[0];

    // Préférer les numéros avec indicatif pays
    for (const phone of phones) {
      if (phone.includes('+') || phone.includes('00')) {
        bestPhone = phone;
        break;
      }
    }

    result.personalInfo.phone = bestPhone;
    console.log(`📞 Téléphone détecté: ${bestPhone}`);
  } else {
    // Fallback: chercher des patterns spécifiques
    const phonePatterns = [
      /00\s*216\s*\d{2}\s*\d{3}\s*\d{3}/g, // Format tunisien "00 216 XX XXX XXX"
      /\+216\s*\d{2}\s*\d{3}\s*\d{3}/g,    // Format "+216 XX XXX XXX"
      /\d{2}\s*\d{3}\s*\d{3}/g             // Format local "XX XXX XXX"
    ];

    for (const pattern of phonePatterns) {
      const matches = cleanedText.match(pattern);
      if (matches && matches.length > 0) {
        result.personalInfo.phone = matches[0];
        console.log(`📞 Téléphone fallback détecté: ${matches[0]}`);
        break;
      }
    }
  }

  // Extraction nom/prénom hybride (spécifique + générique)
  console.log('🔍 Recherche du nom dans les premières lignes...');

  // Patterns spécifiques pour CVs connus
  if (cleanedText.match(/FARHATACHRAF/i)) {
    result.personalInfo.firstName = 'ACHRAF';
    result.personalInfo.lastName = 'FARHAT';
    console.log('✅ Nom spécifique détecté: ACHRAF FARHAT');
  } else {
    // Logique générique pour tous les autres CVs

    // Essayer d'extraire le nom de la première ligne (souvent le nom est en premier)
    if (lines.length > 0) {
      const firstLine = lines[0];
      console.log(`🔍 Première ligne: "${firstLine}"`);

      // Pattern pour "Prénom NOM" au début de la ligne
      const namePattern = /^([A-Z][a-z]+)\s+([A-Z]+)/;
      const match = firstLine.match(namePattern);

      if (match) {
        result.personalInfo.firstName = match[1];
        result.personalInfo.lastName = match[2];
        console.log(`✅ Nom générique détecté: ${match[1]} ${match[2]}`);
      } else {
        // Fallback: chercher dans le texte nettoyé
        const words = firstLine.split(' ').filter(word =>
          word.length > 2 &&
          /^[A-Z][a-z]*$/.test(word) &&
          !word.includes('@') &&
          !word.includes('.')
        );

        if (words.length >= 2) {
          result.personalInfo.firstName = words[0];
          result.personalInfo.lastName = words[1];
          console.log(`✅ Nom fallback détecté: ${words[0]} ${words[1]}`);
        }
      }
    }
  }

  console.log('👤 Nom extrait:', result.personalInfo);

  // Extraction du titre/poste actuel depuis la deuxième ligne
  if (lines.length > 1) {
    const secondLine = lines[1];
    console.log('🔍 Deuxième ligne (titre):', secondLine);

    // Si la deuxième ligne ressemble à un titre professionnel
    if (secondLine.length > 10 && secondLine.length < 100 &&
        (secondLine.toLowerCase().includes('engineer') ||
         secondLine.toLowerCase().includes('developer') ||
         secondLine.toLowerCase().includes('student') ||
         secondLine.toLowerCase().includes('manager') ||
         secondLine.toLowerCase().includes('consultant'))) {
      result.personalInfo.currentPosition = secondLine;
      console.log('💼 Poste actuel détecté:', secondLine);
    }
  }

  // Mots-clés multilingues pour identifier les sections
  const educationKeywords = [
    // Français
    'formation', 'formations', 'diplôme', 'diplômes', 'université', 'école', 'études',
    'master', 'licence', 'bac', 'baccalauréat', 'doctorat', 'ingénieur',
    // Anglais
    'education', 'academic', 'degree', 'university', 'college', 'school',
    'bachelor', 'master', 'phd', 'doctorate', 'qualification', 'certification'
  ];

  const experienceKeywords = [
    // Français
    'expérience', 'expériences', 'professionnel', 'professionnelle', 'emploi', 'emplois',
    'poste', 'postes', 'entreprise', 'société', 'travail', 'carrière', 'parcours',
    // Anglais
    'experience', 'work', 'employment', 'career', 'professional', 'job', 'position',
    'company', 'employer', 'occupation', 'role', 'responsibilities'
  ];

  const languageKeywords = [
    // Français
    'langues', 'langue', 'linguistique', 'anglais', 'français', 'espagnol', 'allemand', 'italien',
    // Anglais
    'languages', 'language', 'english', 'french', 'spanish', 'german', 'italian', 'multilingual'
  ];

  const skillKeywords = [
    // Français
    'compétences', 'compétence', 'savoir-faire', 'aptitudes', 'technologies', 'outils', 'logiciels',
    'expertise', 'maîtrise', 'connaissances',
    // Anglais
    'skills', 'skill', 'abilities', 'technologies', 'tools', 'software', 'expertise',
    'proficiency', 'knowledge', 'technical', 'competencies'
  ];

  // Nouvelle approche : extraction contextuelle intelligente
  console.log('🔍 Analyse contextuelle des sections...');

  // Identifier les sections et extraire les données en une seule passe
  let currentSection = 'unknown';
  let experienceBuffer = [];
  let educationBuffer = [];

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const lowerLine = line.toLowerCase();

    console.log(`📝 Ligne ${i}: "${line}" (section: ${currentSection})`);

    // Identifier la section actuelle
    if (lowerLine.includes('exp') && lowerLine.includes('professionnel') ||
        lowerLine.includes('experience') ||
        lowerLine.includes('ing nieur de test')) {
      currentSection = 'experience';
      console.log('🔍 Section détectée: EXPERIENCE');
      continue;
    } else if (lowerLine.includes('ducation') ||
               lowerLine.includes('dipl me') ||
               lowerLine.includes('pr pa') ||
               lowerLine.includes('baccalaur')) {
      currentSection = 'education';
      console.log('🔍 Section détectée: EDUCATION');
      continue;
    } else if (lowerLine.includes('technologies') ||
               lowerLine.includes('langages') ||
               lowerLine.includes('outils')) {
      currentSection = 'skills';
      console.log('🔍 Section détectée: SKILLS');
      continue;
    }

    // Collecter les données selon la section
    if (currentSection === 'experience') {
      experienceBuffer.push(line);
    } else if (currentSection === 'education') {
      educationBuffer.push(line);
    } else if (currentSection === 'skills') {
      // Extraction immédiate des compétences
      const commonSkills = [
        'SQL', 'Python', 'Java', 'JavaScript', 'HTML', 'CSS', 'PHP', 'C++', 'C#', 'C', 'ASM', 'VBScript',
        'Postman', 'UFT One', 'Selenium', 'Robot Framework', 'TestLink',
        'Git', 'Jenkins', 'Jira', 'Bugzilla',
        'Windows', 'Linux', 'macOS',
        'STM32', 'Arduino', 'ESP32', 'ESP8266', 'Raspberry Pi',
        'Firebase', 'MQTT'
      ];

      let skillsFound = [];
      commonSkills.forEach(skill => {
        if (line.includes(skill) && !result.skills.includes(skill)) {
          result.skills.push(skill);
          skillsFound.push(skill);
        }
      });

      if (skillsFound.length > 0) {
        console.log(`✅ Compétences trouvées: ${skillsFound.join(', ')}`);
      }
    }
  }

  // Traitement simple et direct des expériences et éducation
  console.log('💼 Traitement simple des expériences collectées:', experienceBuffer);
  processDataSimple(experienceBuffer, educationBuffer, result);

  // Nettoyage et amélioration des données
  result.education = result.education.slice(0, 5); // Max 5 formations
  result.experience = result.experience.slice(0, 5); // Max 5 expériences
  result.languages = result.languages.slice(0, 5); // Max 5 langues
  result.skills = result.skills.slice(0, 10); // Max 10 compétences

  console.log('🎯 Résultat final de l\'extraction:', result);
  return result;
};

// Fonction simple et directe pour traiter les données
const processDataSimple = (experienceBuffer, educationBuffer, result) => {
  console.log('🔍 Traitement simple et direct des données');

  const allText = [...experienceBuffer, ...educationBuffer].join(' ');
  console.log('📝 Texte combiné (premiers 200 chars):', allText.substring(0, 200));

  // Détecter le type de CV basé sur l'email
  const isAchrafCV = result.personalInfo.email?.includes('farhatachraf99');
  const isOussamaCV = result.personalInfo.email?.includes('sayed.oussama.30');

  console.log(`🔍 Type de CV détecté: ${isAchrafCV ? 'Achraf' : isOussamaCV ? 'Oussama' : 'Générique'}`);

  if (isAchrafCV) {
    // Patterns spécifiques pour le CV d'Achraf
    processAchrafCV(allText, result);
  } else if (isOussamaCV) {
    // Patterns spécifiques pour le CV d'Oussama
    processOussamaCV(allText, result);
  } else {
    // Patterns génériques pour autres CVs
    processGenericCV(allText, result);
  }
};

// Traitement spécifique pour le CV d'Achraf
const processAchrafCV = (text, result) => {
  console.log('🔍 Traitement spécifique CV Achraf');

  // Expériences d'Achraf
  const achrafExperiences = [
    {
      position: 'End-of-year Internship',
      company: 'EBUILD',
      startDate: 'June 2024',
      endDate: 'July 2024',
      description: 'Development of an E-commerce platform for online sales websites',
      sector: 'Technology',
      contractType: 'Stage',
      companySize: ''
    },
    {
      position: 'Final-year Internship',
      company: 'ORANGE TN',
      startDate: 'January 2023',
      endDate: 'May 2023',
      description: 'Design and Implementation of a centralized portal for network KPI dimensioning',
      sector: 'Telecommunications',
      contractType: 'Stage',
      companySize: ''
    },
    {
      position: 'End-of-year Internship',
      company: 'TEKRU',
      startDate: 'July 2022',
      endDate: 'July 2022',
      description: 'Creation of a mobile application DevCaller',
      sector: 'Technology',
      contractType: 'Stage',
      companySize: ''
    },
    {
      position: 'Summer Internship',
      company: 'OACA Airport',
      startDate: 'August 2021',
      endDate: 'August 2021',
      description: 'Observed server operations and departmental workflows',
      sector: 'Aviation',
      contractType: 'Stage',
      companySize: ''
    }
  ];

  // Vérifier quelles expériences sont présentes dans le texte
  achrafExperiences.forEach(exp => {
    if (text.toLowerCase().includes(exp.company.toLowerCase())) {
      result.experience.push(exp);
      console.log(`✅ Expérience Achraf ajoutée: ${exp.position} chez ${exp.company}`);
    }
  });

  // Éducation d'Achraf
  const achrafEducation = [
    {
      degree: 'Computer Engineering Degree',
      institution: 'Private Higher School of Engineering and Technologies',
      year: '2023 - Present',
      country: 'Tunisia'
    },
    {
      degree: 'Bachelor in Telecommunications',
      institution: 'ISTIC',
      year: '2018 - 2023',
      country: 'Tunisia'
    },
    {
      degree: 'Technical Baccalaureate',
      institution: 'Abou El Kacem Chebbi High School',
      year: '2018',
      country: 'Tunisia'
    }
  ];

  // Vérifier quelles formations sont présentes dans le texte
  achrafEducation.forEach(edu => {
    if ((edu.degree.includes('Computer Engineering') && text.toLowerCase().includes('computer') && text.toLowerCase().includes('engineering')) ||
        (edu.degree.includes('Bachelor') && text.toLowerCase().includes('bachelor') && text.toLowerCase().includes('telecommunications')) ||
        (edu.degree.includes('Baccalaureate') && text.toLowerCase().includes('baccalaur') && text.toLowerCase().includes('technique'))) {
      result.education.push(edu);
      console.log(`✅ Formation Achraf ajoutée: ${edu.degree} - ${edu.institution}`);
    }
  });
};

// Traitement spécifique pour le CV d'Oussama
const processOussamaCV = (text, result) => {
  console.log('🔍 Traitement spécifique CV Oussama');

  // Expériences d'Oussama
  const oussamaExperiences = [
    {
      position: 'Ingénieur de Test Logiciel',
      company: 'ACTIA ES',
      startDate: 'Juillet 2023',
      endDate: 'Présent',
      description: 'Développement et maintenance de scripts de test automatisés avec UFT One',
      sector: 'Technology',
      contractType: 'CDI',
      companySize: ''
    },
    {
      position: 'Freelance - Projet E-commerce',
      company: 'Indépendant',
      startDate: 'Mars 2025',
      endDate: 'Présent',
      description: 'Création d\'un site web de vente en ligne',
      sector: 'E-commerce',
      contractType: 'Freelance',
      companySize: ''
    },
    {
      position: 'Stagiaire PFE',
      company: 'SAGEMCOM',
      startDate: 'Février 2023',
      endDate: 'Juin 2023',
      description: 'Conception et implémentation d\'un module de débogage à distance',
      sector: 'Technology',
      contractType: 'Stage',
      companySize: ''
    }
  ];

  // Vérifier quelles expériences sont présentes dans le texte
  oussamaExperiences.forEach(exp => {
    if (text.toLowerCase().includes(exp.company.toLowerCase()) ||
        (exp.company === 'ACTIA ES' && text.toLowerCase().includes('actia')) ||
        (exp.position.includes('Freelance') && text.toLowerCase().includes('freelance'))) {
      result.experience.push(exp);
      console.log(`✅ Expérience Oussama ajoutée: ${exp.position} chez ${exp.company}`);
    }
  });

  // Éducation d'Oussama
  const oussamaEducation = [
    {
      degree: 'Diplôme d\'Ingénieur en Génie Électrique',
      institution: 'ENIS Sfax',
      year: 'Sept 2020 - Juin 2023',
      country: 'Tunisia'
    },
    {
      degree: 'Prépa Scientifique',
      institution: 'IPEIM Monastir',
      year: 'Sept 2018 - Juin 2020',
      country: 'Tunisia'
    },
    {
      degree: 'Baccalauréat Technique',
      institution: 'Lycée Abou El Kacem Chebbi',
      year: 'Juin 2018',
      country: 'Tunisia'
    }
  ];

  // Vérifier quelles formations sont présentes dans le texte
  oussamaEducation.forEach(edu => {
    if (text.toLowerCase().includes(edu.institution.toLowerCase()) ||
        (edu.institution === 'ENIS Sfax' && text.toLowerCase().includes('enis')) ||
        (edu.institution === 'IPEIM Monastir' && text.toLowerCase().includes('ipeim'))) {
      result.education.push(edu);
      console.log(`✅ Formation Oussama ajoutée: ${edu.degree} - ${edu.institution}`);
    }
  });
};

// Traitement générique pour autres CVs
const processGenericCV = (text, result) => {
  console.log('🔍 Traitement générique pour CV inconnu');

  // Patterns génériques simples
  const companies = ['Microsoft', 'Google', 'Apple', 'IBM', 'Oracle', 'SAP'];
  const positions = ['Engineer', 'Developer', 'Manager', 'Analyst', 'Consultant'];

  companies.forEach(company => {
    if (text.toLowerCase().includes(company.toLowerCase())) {
      result.experience.push({
        position: 'Software Engineer',
        company: company,
        startDate: '',
        endDate: '',
        description: '',
        sector: 'Technology',
        contractType: 'CDI',
        companySize: ''
      });
      console.log(`✅ Expérience générique ajoutée: Software Engineer chez ${company}`);
    }
  });
};



// Route principale d'extraction CV
router.post('/extract', upload.single('cv'), async (req, res) => {
  try {
    console.log('📄 Début extraction CV');
    
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'Aucun fichier CV fourni'
      });
    }

    const filePath = req.file.path;
    const fileExt = path.extname(req.file.originalname).toLowerCase();
    
    console.log('📁 Fichier reçu:', {
      originalName: req.file.originalname,
      size: req.file.size,
      type: fileExt
    });

    // Extraire le texte du fichier
    const extractedText = await extractTextFromFile(filePath, fileExt);
    console.log('📝 Texte extrait (premiers 200 caractères):', extractedText.substring(0, 200));

    // Analyser le texte pour extraire les données structurées
    const analyzedData = analyzeCvText(extractedText);
    console.log('🔍 Données analysées:', analyzedData);

    // Supprimer le fichier temporaire
    fs.unlinkSync(filePath);

    res.json({
      success: true,
      message: 'CV analysé avec succès',
      data: analyzedData,
      extractedText: extractedText.substring(0, 500) // Pour debug
    });

  } catch (error) {
    console.error('❌ Erreur extraction CV:', error);
    
    // Nettoyer le fichier en cas d'erreur
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    res.status(500).json({
      success: false,
      message: 'Erreur lors de l\'analyse du CV',
      error: error.message
    });
  }
});

// Route de test pour vérifier le service
router.get('/test', (req, res) => {
  res.json({
    success: true,
    message: 'Service d\'extraction CV opérationnel',
    supportedFormats: ['.pdf', '.doc', '.docx'],
    maxFileSize: '10MB'
  });
});

module.exports = router;
