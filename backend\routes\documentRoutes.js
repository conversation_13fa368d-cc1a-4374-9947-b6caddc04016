const express = require('express');
const router = express.Router();
const path = require('path');
const fs = require('fs');
const Document = require('../models/Document');
const { authenticate, authorize } = require('../middleware/auth');

// GET /api/documents/folders - Obtenir les dossiers pour l'admin
router.get('/folders', authenticate, async (req, res) => {
  try {
    console.log('📁 Récupération des dossiers de documents');

    // Créer des dossiers par défaut basés sur les catégories
    const categories = await Document.distinct('category');

    const folders = [
      {
        id: 1,
        name: 'Candidatures',
        description: 'Documents des candidatures étudiantes',
        documentCount: await Document.countDocuments({ category: 'application' }),
        color: 'blue'
      },
      {
        id: 2,
        name: 'Cours',
        description: 'Supports de cours et matériel pédagogique',
        documentCount: await Document.countDocuments({ category: 'course' }),
        color: 'green'
      },
      {
        id: 3,
        name: 'Administration',
        description: 'Documents administratifs et réglementaires',
        documentCount: await Document.countDocuments({ category: 'administrative' }),
        color: 'purple'
      },
      {
        id: 4,
        name: 'Évaluations',
        description: 'Examens, devoirs et évaluations',
        documentCount: await Document.countDocuments({ category: 'evaluation' }),
        color: 'orange'
      }
    ];

    console.log(`✅ ${folders.length} dossiers récupérés`);

    res.json({
      success: true,
      folders
    });
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des dossiers:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des dossiers',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// GET /api/documents - Obtenir tous les documents avec filtres pour l'admin
router.get('/', authenticate, async (req, res) => {
  try {
    console.log('📄 Récupération des documents avec filtres:', req.query);

    const { page = 1, limit = 50, owner, category, status, folderId, search, type } = req.query;
    const query = {};

    // Appliquer les filtres
    if (owner) query.owner = owner;
    if (category) query.category = category;
    if (status) query.status = status;
    if (type) query.fileType = type;

    // Filtrer par dossier (basé sur la catégorie)
    if (folderId) {
      const folderMapping = {
        '1': 'application',
        '2': 'course',
        '3': 'administrative',
        '4': 'evaluation'
      };
      if (folderMapping[folderId]) {
        query.category = folderMapping[folderId];
      }
    }

    // Recherche textuelle
    if (search) {
      query.$or = [
        { fileName: { $regex: search, $options: 'i' } },
        { originalName: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } }
      ];
    }

    const documents = await Document.find(query)
      .populate('owner', 'firstName lastName email')
      .populate('uploadedBy', 'firstName lastName')
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });

    const total = await Document.countDocuments(query);

    // Enrichir les données pour l'interface admin
    const enrichedDocuments = documents.map(doc => ({
      id: doc._id,
      name: doc.fileName,
      originalName: doc.originalName || doc.fileName,
      type: doc.fileType,
      size: doc.fileSize,
      category: doc.category,
      folderId: getFolderIdFromCategory(doc.category),
      uploadedBy: doc.uploadedBy ?
        `${doc.uploadedBy.firstName} ${doc.uploadedBy.lastName}` :
        'Utilisateur inconnu',
      uploadedAt: doc.createdAt,
      lastModified: doc.updatedAt,
      status: doc.status || 'active',
      tags: doc.tags || [],
      description: doc.description || ''
    }));

    console.log(`✅ ${enrichedDocuments.length} documents récupérés`);

    res.json({
      success: true,
      documents: enrichedDocuments,
      totalPages: Math.ceil(total / limit),
      currentPage: parseInt(page),
      total
    });
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des documents:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des documents',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Fonction utilitaire pour mapper les catégories aux IDs de dossiers
function getFolderIdFromCategory(category) {
  const mapping = {
    'application': 1,
    'course': 2,
    'administrative': 3,
    'evaluation': 4
  };
  return mapping[category] || 1;
}

router.get('/:id', async (req, res) => {
  try {
    const document = await Document.findById(req.params.id)
      .populate('owner', 'firstName lastName email')
      .populate('uploadedBy', 'firstName lastName');
    
    if (!document) return res.status(404).json({ message: 'Document not found' });
    res.json(document);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

router.post('/', async (req, res) => {
  try {
    const document = new Document(req.body);
    await document.save();
    res.status(201).json(document);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.put('/:id', async (req, res) => {
  try {
    const document = await Document.findByIdAndUpdate(req.params.id, req.body, { new: true });
    if (!document) return res.status(404).json({ message: 'Document not found' });
    res.json(document);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.post('/:id/approve', async (req, res) => {
  try {
    const { approvedBy, comments } = req.body;
    const document = await Document.findById(req.params.id);
    
    if (!document) return res.status(404).json({ message: 'Document not found' });
    
    await document.approve(approvedBy, comments);
    res.json({ message: 'Document approved successfully' });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.get('/search', async (req, res) => {
  try {
    const { q, userId, category, status, dateFrom, dateTo } = req.query;
    
    const filters = {};
    if (category) filters.category = category;
    if (status) filters.status = status;
    if (dateFrom) filters.dateFrom = dateFrom;
    if (dateTo) filters.dateTo = dateTo;
    
    const documents = await Document.search(q, userId, filters);
    res.json(documents);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Route publique pour télécharger la lettre de recommandation template
router.get('/download/recommendation-letter', (req, res) => {
  try {
    // Chemin vers le fichier template de lettre de recommandation
    const filePath = path.join(__dirname, '../templates/Lettre_de_recommendation.pdf');

    // Vérifier si le fichier existe
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        message: 'Fichier de lettre de recommandation non trouvé'
      });
    }

    // Définir les headers pour le téléchargement
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', 'attachment; filename="Lettre_de_recommendation.pdf"');

    // Envoyer le fichier
    res.sendFile(filePath);
  } catch (error) {
    console.error('❌ Erreur lors du téléchargement de la lettre de recommandation:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors du téléchargement du fichier'
    });
  }
});

module.exports = router;
