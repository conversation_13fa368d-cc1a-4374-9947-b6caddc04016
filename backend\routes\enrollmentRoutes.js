const express = require('express');
const router = express.Router();
const Enrollment = require('../models/Enrollment');

router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 10, status, academicYear } = req.query;
    const query = {};
    if (status) query.status = status;
    if (academicYear) query.academicYear = academicYear;
    
    const enrollments = await Enrollment.find(query)
      .populate('student', 'studentNumber user')
      .populate('course', 'courseCode title')
      .populate('module', 'moduleCode title')
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });
    
    const total = await Enrollment.countDocuments(query);
    res.json({ enrollments, totalPages: Math.ceil(total / limit), currentPage: page, total });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

router.get('/:id', async (req, res) => {
  try {
    const enrollment = await Enrollment.findById(req.params.id)
      .populate('student', 'studentNumber user')
      .populate('course', 'courseCode title')
      .populate('module', 'moduleCode title');
    
    if (!enrollment) return res.status(404).json({ message: 'Enrollment not found' });
    res.json(enrollment);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

router.post('/', async (req, res) => {
  try {
    const enrollment = new Enrollment(req.body);
    await enrollment.save();
    res.status(201).json(enrollment);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.put('/:id', async (req, res) => {
  try {
    const enrollment = await Enrollment.findByIdAndUpdate(req.params.id, req.body, { new: true });
    if (!enrollment) return res.status(404).json({ message: 'Enrollment not found' });
    res.json(enrollment);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

module.exports = router;
