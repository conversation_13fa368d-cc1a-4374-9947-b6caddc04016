const express = require('express');
const router = express.Router();
const Grade = require('../models/Grade');
const Course = require('../models/Course');
const Student = require('../models/Student');
const { authenticate, authorize } = require('../middleware/auth');

// GET /api/grades - Obtenir toutes les notes avec filtres pour l'admin
router.get('/', authenticate, async (req, res) => {
  try {
    console.log('📊 Récupération des notes avec filtres:', req.query);

    const { page = 1, limit = 50, student, courseId, semester, status, search } = req.query;
    const query = {};

    // Appliquer les filtres
    if (student) query.student = student;
    if (courseId) query.course = courseId;
    if (semester) query.semester = semester;
    if (status) query.status = status;

    const grades = await Grade.find(query)
      .populate('student', 'studentNumber user')
      .populate('course', 'courseCode title')
      .populate('assignment', 'title')
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });

    const total = await Grade.countDocuments(query);

    // Enrichir les données pour l'interface admin
    const enrichedGrades = grades.map(grade => {
      // Calculer la note finale basée sur les évaluations
      const evaluations = grade.evaluations || [
        { type: 'Devoir 1', grade: grade.score || 0, maxGrade: 20, weight: 20, date: grade.createdAt },
        { type: 'Examen Partiel', grade: grade.midtermScore || 0, maxGrade: 20, weight: 40, date: grade.createdAt },
        { type: 'Projet', grade: grade.finalScore || 0, maxGrade: 20, weight: 40, date: grade.createdAt }
      ];

      const finalGrade = calculateFinalGrade(evaluations);
      const gradeStatus = getGradeStatus(finalGrade);

      return {
        id: grade._id,
        student: {
          id: grade.student?._id,
          name: grade.student?.user ?
            `${grade.student.user.firstName} ${grade.student.user.lastName}` :
            'Étudiant inconnu',
          email: grade.student?.user?.email || 'Email non disponible',
          studentId: grade.student?.studentNumber || 'ID non disponible'
        },
        course: {
          id: grade.course?._id,
          name: grade.course?.title || 'Cours inconnu',
          code: grade.course?.courseCode || 'CODE-XXX'
        },
        evaluations,
        finalGrade: finalGrade.toFixed(1),
        status: gradeStatus,
        semester: grade.semester || 'Fall 2024',
        lastUpdated: grade.updatedAt || grade.createdAt
      };
    });

    console.log(`✅ ${enrichedGrades.length} notes récupérées`);

    res.json({
      success: true,
      grades: enrichedGrades,
      totalPages: Math.ceil(total / limit),
      currentPage: parseInt(page),
      total
    });
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des notes:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des notes',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Fonction utilitaire pour calculer la note finale
function calculateFinalGrade(evaluations) {
  if (!evaluations || evaluations.length === 0) return 0;

  let totalWeightedScore = 0;
  let totalWeight = 0;

  evaluations.forEach(evaluation => {
    const weight = evaluation.weight || 0;
    const score = evaluation.grade || 0;
    const maxScore = evaluation.maxGrade || 20;

    // Normaliser la note sur 20
    const normalizedScore = (score / maxScore) * 20;

    totalWeightedScore += normalizedScore * (weight / 100);
    totalWeight += weight / 100;
  });

  return totalWeight > 0 ? totalWeightedScore / totalWeight : 0;
}

// Fonction utilitaire pour déterminer le statut de la note
function getGradeStatus(finalGrade) {
  if (finalGrade >= 16) return 'excellent';
  if (finalGrade >= 12) return 'passed';
  if (finalGrade >= 10) return 'passed';
  return 'failed';
}

router.get('/:id', async (req, res) => {
  try {
    const grade = await Grade.findById(req.params.id)
      .populate('student', 'studentNumber user')
      .populate('course', 'courseCode title')
      .populate('assignment', 'title type')
      .populate('gradingMetadata.gradedBy', 'firstName lastName');
    
    if (!grade) return res.status(404).json({ message: 'Grade not found' });
    res.json(grade);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

router.post('/', async (req, res) => {
  try {
    const grade = new Grade(req.body);
    await grade.save();
    res.status(201).json(grade);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.put('/:id', async (req, res) => {
  try {
    const grade = await Grade.findByIdAndUpdate(req.params.id, req.body, { new: true });
    if (!grade) return res.status(404).json({ message: 'Grade not found' });
    res.json(grade);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.post('/:id/publish', async (req, res) => {
  try {
    const { publishedBy } = req.body;
    const grade = await Grade.findById(req.params.id);
    
    if (!grade) return res.status(404).json({ message: 'Grade not found' });
    
    await grade.publish(publishedBy);
    res.json({ message: 'Grade published successfully' });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

module.exports = router;
