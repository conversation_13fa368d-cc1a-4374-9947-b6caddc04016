const express = require('express');
const router = express.Router();
const Module = require('../models/Module');
const Professor = require('../models/Professor');
const { authenticate, authorize } = require('../middleware/auth');

// GET /api/modules - Obtenir tous les modules avec filtres
router.get('/', authenticate, async (req, res) => {
  try {
    console.log('📚 Récupération des modules avec filtres:', req.query);

    const { page = 1, limit = 50, promotion, status, search } = req.query;
    const query = {};

    // Appliquer les filtres
    if (promotion) query.promotion = promotion;
    if (status) query.status = status;

    // Recherche textuelle
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { moduleCode: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    const modules = await Module.find(query)
      .populate('coordinator', 'user academicTitle department')
      .populate('coordinator.user', 'firstName lastName email')
      .populate('instructors', 'user academicTitle department')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Module.countDocuments(query);

    console.log(`✅ ${modules.length} modules trouvés`);

    res.json({
      modules,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des modules:', error);
    res.status(500).json({ message: error.message });
  }
});

// GET /api/modules/:id - Obtenir un module spécifique
router.get('/:id', authenticate, async (req, res) => {
  try {
    const module = await Module.findById(req.params.id)
      .populate('coordinator', 'user academicTitle department')
      .populate('coordinator.user', 'firstName lastName email')
      .populate('instructors', 'user academicTitle department')
      .populate('prerequisites.module', 'moduleCode title');

    if (!module) {
      return res.status(404).json({ message: 'Module non trouvé' });
    }

    res.json(module);
  } catch (error) {
    console.error('❌ Erreur lors de la récupération du module:', error);
    res.status(500).json({ message: error.message });
  }
});

// POST /api/modules - Créer un nouveau module
router.post('/', authenticate, authorize(['admin', 'staff']), async (req, res) => {
  try {
    console.log('📚 Création d\'un nouveau module:', req.body);

    // Validation des données requises
    const { moduleCode, title, description, coordinator, promotion } = req.body;

    if (!moduleCode || !title || !description || !coordinator || !promotion) {
      return res.status(400).json({
        message: 'Données manquantes: moduleCode, title, description, coordinator et promotion sont requis'
      });
    }

    // Vérifier que le coordinateur existe
    const coordinatorExists = await Professor.findById(coordinator);
    if (!coordinatorExists) {
      return res.status(400).json({ message: 'Coordinateur non trouvé' });
    }

    // Vérifier l'unicité du code de module
    const existingModule = await Module.findOne({ moduleCode });
    if (existingModule) {
      return res.status(400).json({ message: 'Un module avec ce code existe déjà' });
    }

    // Créer le module avec les données complètes
    const moduleData = {
      ...req.body,
      createdBy: req.user?.id
    };

    const module = new Module(moduleData);
    await module.save();

    const populatedModule = await Module.findById(module._id)
      .populate('coordinator', 'user academicTitle department')
      .populate('coordinator.user', 'firstName lastName email');

    console.log('✅ Module créé avec succès:', populatedModule.moduleCode);
    res.status(201).json(populatedModule);
  } catch (error) {
    console.error('❌ Erreur lors de la création du module:', error);
    res.status(400).json({ message: error.message });
  }
});

// PUT /api/modules/:id - Modifier un module
router.put('/:id', authenticate, authorize(['admin', 'staff']), async (req, res) => {
  try {
    console.log('📝 Modification du module:', req.params.id, req.body);

    // Vérifier que le module existe
    const existingModule = await Module.findById(req.params.id);
    if (!existingModule) {
      return res.status(404).json({ message: 'Module non trouvé' });
    }

    // Si le code de module change, vérifier l'unicité
    if (req.body.moduleCode && req.body.moduleCode !== existingModule.moduleCode) {
      const duplicateModule = await Module.findOne({
        moduleCode: req.body.moduleCode,
        _id: { $ne: req.params.id }
      });
      if (duplicateModule) {
        return res.status(400).json({ message: 'Un module avec ce code existe déjà' });
      }
    }

    // Si le coordinateur change, vérifier qu'il existe
    if (req.body.coordinator) {
      const coordinatorExists = await Professor.findById(req.body.coordinator);
      if (!coordinatorExists) {
        return res.status(400).json({ message: 'Coordinateur non trouvé' });
      }
    }

    const module = await Module.findByIdAndUpdate(
      req.params.id,
      {
        ...req.body,
        updatedBy: req.user?.id
      },
      { new: true, runValidators: true }
    ).populate('coordinator', 'user academicTitle department')
     .populate('coordinator.user', 'firstName lastName email');

    console.log('✅ Module modifié avec succès:', module.moduleCode);
    res.json(module);
  } catch (error) {
    console.error('❌ Erreur lors de la modification du module:', error);
    res.status(400).json({ message: error.message });
  }
});

// DELETE /api/modules/:id - Supprimer un module
router.delete('/:id', authenticate, authorize(['admin']), async (req, res) => {
  try {
    console.log('🗑️ Suppression du module:', req.params.id);

    const module = await Module.findById(req.params.id);
    if (!module) {
      return res.status(404).json({ message: 'Module non trouvé' });
    }

    // Vérifier s'il y a des cours associés
    const Course = require('../models/Course');
    const associatedCourses = await Course.find({ module: req.params.id });
    if (associatedCourses.length > 0) {
      return res.status(400).json({
        message: `Impossible de supprimer un module avec ${associatedCourses.length} cours associé(s)`
      });
    }

    // Supprimer le module
    await Module.findByIdAndDelete(req.params.id);

    console.log('✅ Module supprimé avec succès:', module.moduleCode);
    res.json({ message: 'Module supprimé avec succès' });
  } catch (error) {
    console.error('❌ Erreur lors de la suppression du module:', error);
    res.status(500).json({ message: error.message });
  }
});

// POST /api/modules/:id/generate-courses - Générer automatiquement les cours pour un module
router.post('/:id/generate-courses', authenticate, authorize(['admin', 'staff']), async (req, res) => {
  try {
    console.log('🔄 Génération automatique des cours pour le module:', req.params.id);

    const module = await Module.findById(req.params.id);
    if (!module) {
      return res.status(404).json({ message: 'Module non trouvé' });
    }

    const Course = require('../models/Course');

    // Utiliser la méthode statique du modèle Course
    const createdCourses = await Course.createModuleCourses(req.params.id, module.promotion);

    console.log('✅ Cours générés automatiquement:', createdCourses.length);
    res.status(201).json({
      message: `${createdCourses.length} cours créés avec succès pour le module ${module.title}`,
      courses: createdCourses
    });
  } catch (error) {
    console.error('❌ Erreur lors de la génération des cours:', error);
    res.status(400).json({ message: error.message });
  }
});

module.exports = router;
