const express = require('express');
const bcrypt = require('bcryptjs');
const { authenticate } = require('../middleware/auth');
const User = require('../models/User');
const {
  sendPasswordResetConfirmation,
  verifyConfirmationCode,
  changePasswordWithCode
} = require('../services/passwordResetService');

const router = express.Router();

// @desc    Demander un code de confirmation pour changer le mot de passe
// @route   POST /api/password/request-code
// @access  Private
router.post('/request-code', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;
    const userEmail = req.user.email;

    console.log(`📧 Demande de code de confirmation pour l'utilisateur ${userId} (${userEmail})`);

    // Envoyer le code de confirmation
    const result = await sendPasswordResetConfirmation(userId, userEmail);

    if (result.success) {
      res.json({
        success: true,
        message: result.message,
        expiresIn: result.expiresIn
      });
    } else {
      res.status(500).json({
        success: false,
        message: result.error
      });
    }
  } catch (error) {
    console.error('❌ Erreur lors de la demande de code:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la demande de code'
    });
  }
});

// @desc    Vérifier le code de confirmation
// @route   POST /api/password/verify-code
// @access  Private
router.post('/verify-code', authenticate, async (req, res) => {
  try {
    const { confirmationCode } = req.body;
    const userId = req.user.id;

    if (!confirmationCode) {
      return res.status(400).json({
        success: false,
        message: 'Code de confirmation requis'
      });
    }

    console.log(`🔍 Vérification du code pour l'utilisateur ${userId}`);

    // Vérifier le code
    const result = await verifyConfirmationCode(userId, confirmationCode);

    if (result.success) {
      res.json({
        success: true,
        message: result.message
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.error
      });
    }
  } catch (error) {
    console.error('❌ Erreur lors de la vérification du code:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la vérification du code'
    });
  }
});

// @desc    Changer le mot de passe avec le code vérifié
// @route   POST /api/password/change
// @access  Private
router.post('/change', authenticate, async (req, res) => {
  try {
    const { newPassword, confirmPassword } = req.body;
    const userId = req.user.id;

    // Validation des données
    if (!newPassword || !confirmPassword) {
      return res.status(400).json({
        success: false,
        message: 'Nouveau mot de passe et confirmation requis'
      });
    }

    if (newPassword !== confirmPassword) {
      return res.status(400).json({
        success: false,
        message: 'Les mots de passe ne correspondent pas'
      });
    }

    if (newPassword.length < 8) {
      return res.status(400).json({
        success: false,
        message: 'Le mot de passe doit contenir au moins 8 caractères'
      });
    }

    console.log(`🔐 Changement de mot de passe pour l'utilisateur ${userId}`);

    // Changer le mot de passe
    const result = await changePasswordWithCode(userId, newPassword);

    if (result.success) {
      res.json({
        success: true,
        message: result.message
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.error
      });
    }
  } catch (error) {
    console.error('❌ Erreur lors du changement de mot de passe:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors du changement de mot de passe'
    });
  }
});

// @desc    Changer le mot de passe avec l'ancien mot de passe (sans code email)
// @route   POST /api/password/change-with-current
// @access  Private
router.post('/change-with-current', authenticate, async (req, res) => {
  try {
    const { currentPassword, newPassword, confirmPassword } = req.body;
    const userId = req.user.id;

    // Validation des données
    if (!currentPassword || !newPassword || !confirmPassword) {
      return res.status(400).json({
        success: false,
        message: 'Tous les champs sont requis'
      });
    }

    if (newPassword !== confirmPassword) {
      return res.status(400).json({
        success: false,
        message: 'Les nouveaux mots de passe ne correspondent pas'
      });
    }

    if (newPassword.length < 8) {
      return res.status(400).json({
        success: false,
        message: 'Le nouveau mot de passe doit contenir au moins 8 caractères'
      });
    }

    // Récupérer l'utilisateur avec le mot de passe
    const user = await User.findById(userId).select('+password');
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Utilisateur non trouvé'
      });
    }

    // Vérifier le mot de passe actuel
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);

    if (!isCurrentPasswordValid) {
      return res.status(400).json({
        success: false,
        message: 'Mot de passe actuel incorrect'
      });
    }

    // Hasher le nouveau mot de passe
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
    const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

    // Mettre à jour le mot de passe
    await User.findByIdAndUpdate(userId, {
      password: hashedPassword,
      passwordChangedAt: new Date()
    });

    console.log(`✅ Mot de passe changé avec succès pour l'utilisateur ${userId}`);

    res.json({
      success: true,
      message: 'Mot de passe modifié avec succès'
    });
  } catch (error) {
    console.error('❌ Erreur lors du changement de mot de passe:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors du changement de mot de passe'
    });
  }
});

module.exports = router;
