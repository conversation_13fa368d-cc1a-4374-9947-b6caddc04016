const express = require('express');
const router = express.Router();
const Payment = require('../models/Payment');
const Student = require('../models/Student');
const { authenticate, authorize } = require('../middleware/auth');

// GET /api/payments - Obtenir tous les paiements avec filtres pour l'admin
router.get('/', authenticate, async (req, res) => {
  try {
    console.log('💰 Récupération des paiements avec filtres:', req.query);

    const { page = 1, limit = 50, student, status, method, search } = req.query;
    const query = {};

    // Appliquer les filtres
    if (student) query.student = student;
    if (status) query.status = status;
    if (method) query.paymentMethod = method;

    const payments = await Payment.find(query)
      .populate('student', 'studentNumber user')
      .populate('enrollment', 'enrollmentNumber')
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });

    const total = await Payment.countDocuments(query);

    // Enrichir les données pour l'interface admin
    const enrichedPayments = payments.map(payment => ({
      id: payment._id,
      student: {
        name: payment.student?.user ?
          `${payment.student.user.firstName} ${payment.student.user.lastName}` :
          'Nom non disponible',
        email: payment.student?.user?.email || 'Email non disponible',
        studentId: payment.student?.studentNumber || 'ID non disponible'
      },
      amount: payment.amount,
      currency: payment.currency || 'TND',
      status: payment.status,
      method: payment.paymentMethod,
      description: payment.description || 'Frais de scolarité',
      dueDate: payment.dueDate,
      paidDate: payment.paidDate,
      transactionId: payment.transactionId,
      semester: payment.semester || 'Fall 2024',
      createdAt: payment.createdAt,
      updatedAt: payment.updatedAt
    }));

    console.log(`✅ ${enrichedPayments.length} paiements récupérés`);

    res.json({
      success: true,
      payments: enrichedPayments,
      totalPages: Math.ceil(total / limit),
      currentPage: parseInt(page),
      total
    });
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des paiements:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des paiements',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

router.get('/:id', async (req, res) => {
  try {
    const payment = await Payment.findById(req.params.id)
      .populate('student', 'studentNumber user')
      .populate('enrollment', 'enrollmentNumber');
    
    if (!payment) return res.status(404).json({ message: 'Payment not found' });
    res.json(payment);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

router.post('/', async (req, res) => {
  try {
    const payment = new Payment(req.body);
    await payment.save();
    res.status(201).json(payment);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.put('/:id', async (req, res) => {
  try {
    const payment = await Payment.findByIdAndUpdate(req.params.id, req.body, { new: true });
    if (!payment) return res.status(404).json({ message: 'Payment not found' });
    res.json(payment);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.post('/:id/process', async (req, res) => {
  try {
    const { paymentData, processedBy } = req.body;
    const payment = await Payment.findById(req.params.id);
    
    if (!payment) return res.status(404).json({ message: 'Payment not found' });
    
    await payment.processPayment(paymentData, processedBy);
    res.json({ message: 'Payment processed successfully' });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.get('/overdue', async (req, res) => {
  try {
    const { daysOverdue = 0 } = req.query;
    const overduePayments = await Payment.getOverduePayments(parseInt(daysOverdue));
    res.json(overduePayments);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

module.exports = router;
