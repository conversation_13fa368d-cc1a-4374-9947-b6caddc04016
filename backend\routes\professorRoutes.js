const express = require('express');
const router = express.Router();
const Professor = require('../models/Professor');
const { authenticate, authorize } = require('../middleware/auth');
const { createProfessorWithCredentials, updateProfessorSalary } = require('../services/professorService');

// GET /api/professors/me - Obtenir les informations du professeur connecté
router.get('/me', authenticate, authorize('professor'), async (req, res) => {
  try {
    const professor = await Professor.findOne({ user: req.user._id })
      .populate('user', 'firstName lastName email phone profilePicture')
      .populate('department', 'name code');

    if (!professor) {
      return res.status(404).json({
        success: false,
        message: 'Profil professeur non trouvé'
      });
    }

    res.json({
      success: true,
      professor
    });
  } catch (error) {
    console.error('Erreur lors de la récupération du profil professeur:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération du profil'
    });
  }
});

// GET /api/professors - Obtenir tous les professeurs (Temporairement accessible à tous)
router.get('/', authenticate, async (req, res) => {
  try {
    const { page = 1, limit = 10, department, employmentStatus } = req.query;
    const query = {};
    
    if (department) query.department = department;
    if (employmentStatus) query.employmentStatus = employmentStatus;
    
    const professors = await Professor.find(query)
      .populate('user', 'firstName lastName email phone profilePicture')
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });
    
    const total = await Professor.countDocuments(query);
    
    res.json({
      professors,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// GET /api/professors/profile - Obtenir le profil du professeur connecté
router.get('/profile', authenticate, async (req, res) => {
  try {
    console.log(`🔍 Recherche du profil professeur pour l'utilisateur: ${req.user.id}`);
    console.log(`👤 Utilisateur connecté: ${req.user.email} (${req.user.role})`);

    const professor = await Professor.findOne({ user: req.user.id })
      .populate('user', '-password -resetPasswordToken -verificationToken');

    if (!professor) {
      console.log(`❌ Aucun profil professeur trouvé pour l'utilisateur ${req.user.id}`);

      // Vérifier si l'utilisateur a le bon rôle
      if (req.user.role !== 'professor') {
        return res.status(403).json({
          success: false,
          message: `Accès refusé. Rôle requis: professor, rôle actuel: ${req.user.role}`
        });
      }

      return res.status(404).json({
        success: false,
        message: 'Profil professeur non trouvé. Contactez l\'administration pour créer votre profil.'
      });
    }

    console.log(`✅ Profil professeur trouvé: ${professor.employeeNumber}`);

    res.json({
      success: true,
      professor
    });
  } catch (error) {
    console.error('❌ Erreur lors de la récupération du profil:', error);
    console.error('Stack trace:', error.stack);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération du profil',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// GET /api/professors/courses - Obtenir les cours du professeur connecté
router.get('/courses', authenticate, async (req, res) => {
  try {
    console.log('📚 Récupération des cours pour le professeur:', req.user?.email);

    // Vérifier que l'utilisateur est un professeur
    if (req.user?.role !== 'professor') {
      return res.status(403).json({
        success: false,
        message: 'Accès refusé. Seuls les professeurs peuvent accéder à cette ressource.'
      });
    }

    // Trouver le professeur dans la base de données
    const professor = await Professor.findOne({ user: req.user.id }).populate('user', 'firstName lastName email');

    if (!professor) {
      return res.status(404).json({
        success: false,
        message: 'Profil professeur non trouvé'
      });
    }

    // Récupérer les affectations où ce professeur est impliqué
    const CourseAssignment = require('../models/CourseAssignment');
    const assignments = await CourseAssignment.find({
      'instructors.professor': professor._id,
      status: 'active'
    })
    .populate('course', 'courseCode title description creditHours semester promotion capacity')
    .populate('instructors.professor', 'user academicTitle department')
    .populate('instructors.professor.user', 'firstName lastName email')
    .sort({ createdAt: -1 });

    console.log(`✅ ${assignments.length} affectations trouvées pour le professeur ${professor.employeeNumber}`);
    // Transformer les affectations pour l'interface professeur
    const transformedCourses = await Promise.all(assignments.map(async (assignment) => {
      const course = assignment.course;
      const instructorInfo = assignment.instructors.find(i =>
        i.professor._id.toString() === professor._id.toString()
      );

      // Calculer le nombre d'étudiants inscrits (pour l'instant 0, à implémenter plus tard)
      const enrolledStudents = 0; // TODO: Implémenter la logique d'inscription des étudiants

      // Calculer le progrès du cours (basé sur les dates du planning)
      const now = new Date();
      const start = new Date(assignment.schedule?.startDate);
      const durationWeeks = assignment.schedule?.durationWeeks || 15;
      const end = new Date(start.getTime() + (durationWeeks * 7 * 24 * 60 * 60 * 1000));
      const totalDuration = end - start;
      const elapsed = now - start;
      const progress = totalDuration > 0 ? Math.max(0, Math.min(100, Math.round((elapsed / totalDuration) * 100))) : 0;

      // Calculer les heures hebdomadaires (basé sur le planning de l'affectation)
      const weeklyHours = assignment.schedule?.hoursPerWeek || 3;

      // Trouver la prochaine classe
      const nextClass = assignment.schedule?.timeSlots?.length > 0 ?
        new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() : // Demain par défaut
        null;

      return {
        _id: course._id,
        assignmentId: assignment._id,
        name: course.title,
        code: course.courseCode,
        semester: course.semester,
        promotion: course.promotion,
        credits: course.creditHours,
        enrolledStudents: enrolledStudents,
        maxStudents: course.capacity?.maximum || 30,
        weeklyHours: weeklyHours,
        role: instructorInfo?.role || 'primary',
        classes: assignment.classes.map(c => c.className),
        schedule: assignment.schedule?.timeSlots ? assignment.schedule.timeSlots.map(slot => ({
          day: slot.dayOfWeek,
          time: `${slot.startTime}-${slot.endTime}`,
          room: slot.location?.room || 'Salle à définir'
        })) : [],
        status: assignment.status,
        progress: progress,
        nextClass: nextClass,
        description: course.description,
        class: course.class,
        promotion: course.promotion
      };
    }));

    console.log(`✅ ${transformedCourses.length} cours récupérés pour le professeur ${professor.user.firstName} ${professor.user.lastName}`);

    res.json({
      success: true,
      courses: transformedCourses,
      professorInfo: {
        employeeNumber: professor.employeeNumber,
        name: `${professor.user.firstName} ${professor.user.lastName}`,
        department: professor.department
      }
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des cours professeur:', error);
    console.error('❌ Stack trace:', error.stack);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des cours',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// GET /api/professors/:id - Obtenir un professeur par ID
router.get('/:id', async (req, res) => {
  try {
    const professor = await Professor.findById(req.params.id)
      .populate('user', '-password -resetPasswordToken -verificationToken');
    
    if (!professor) {
      return res.status(404).json({ message: 'Professor not found' });
    }
    
    res.json(professor);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// POST /api/professors - Créer un nouveau professeur
router.post('/', async (req, res) => {
  try {
    const professor = new Professor(req.body);
    await professor.save();
    
    const populatedProfessor = await Professor.findById(professor._id)
      .populate('user', 'firstName lastName email');
    
    res.status(201).json(populatedProfessor);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// PUT /api/professors/:id - Mettre à jour un professeur
router.put('/:id', async (req, res) => {
  try {
    const professor = await Professor.findByIdAndUpdate(
      req.params.id,
      { ...req.body, updatedBy: req.body.updatedBy },
      { new: true, runValidators: true }
    ).populate('user', 'firstName lastName email');
    
    if (!professor) {
      return res.status(404).json({ message: 'Professor not found' });
    }
    
    res.json(professor);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// GET /api/professors/:id/courses - Obtenir les cours d'un professeur
router.get('/:id/courses', async (req, res) => {
  try {
    const professor = await Professor.findById(req.params.id);
    
    if (!professor) {
      return res.status(404).json({ message: 'Professor not found' });
    }
    
    const courses = await professor.getCurrentCourses();
    res.json(courses);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// GET /api/professors/:id/teaching-load - Obtenir la charge d'enseignement
router.get('/:id/teaching-load', async (req, res) => {
  try {
    const professor = await Professor.findById(req.params.id);
    
    if (!professor) {
      return res.status(404).json({ message: 'Professor not found' });
    }
    
    const teachingLoad = await professor.getTeachingLoad();
    res.json(teachingLoad);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// GET /api/professors/:id/profile - Obtenir le profil public
router.get('/:id/profile', async (req, res) => {
  try {
    const professor = await Professor.findById(req.params.id);
    
    if (!professor) {
      return res.status(404).json({ message: 'Professor not found' });
    }
    
    res.json(professor.getPublicProfile());
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// POST /api/professors/admin/create - Créer un professeur avec credentials (Admin seulement)
router.post('/admin/create', authenticate, authorize(['admin']), async (req, res) => {
  try {
    console.log('🎓 Admin crée un nouveau professeur');
    console.log('Données reçues:', req.body);

    const {
      firstName,
      lastName,
      email,
      phone,
      academicTitle,
      academicRank,
      department,
      employmentStatus,
      contractType,
      startDate,
      salary,
      yearsOfTeaching,
      teachingAreas,
      preferredTeachingMethods,
      maxHoursPerWeek,
      canTravelForTeaching,
      languages,
      technicalSkills,
      expertise,
      bio
    } = req.body;

    // Validation des champs requis
    if (!firstName || !lastName || !email || !academicRank || !department) {
      return res.status(400).json({
        success: false,
        message: 'Les champs firstName, lastName, email, academicRank et department sont requis'
      });
    }

    // Préparer les données du professeur
    const professorData = {
      firstName,
      lastName,
      email,
      phone,
      academicTitle,
      academicRank,
      department,
      employmentStatus,
      contractType,
      startDate,
      salary,
      yearsOfTeaching,
      teachingAreas,
      preferredTeachingMethods,
      maxHoursPerWeek,
      canTravelForTeaching,
      languages,
      technicalSkills,
      expertise,
      bio
    };

    // Créer le professeur avec credentials
    const result = await createProfessorWithCredentials(professorData, req.user.id);

    res.status(201).json(result);
  } catch (error) {
    console.error('❌ Erreur lors de la création du professeur:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Erreur lors de la création du professeur'
    });
  }
});

// PUT /api/professors/admin/:id/salary - Mettre à jour le salaire (Admin seulement)
router.put('/admin/:id/salary', authenticate, authorize(['admin']), async (req, res) => {
  try {
    const { amount, currency, paymentFrequency } = req.body;

    if (!amount || amount < 0) {
      return res.status(400).json({
        success: false,
        message: 'Le montant du salaire est requis et doit être positif'
      });
    }

    const professor = await updateProfessorSalary(
      req.params.id,
      { amount, currency, paymentFrequency },
      req.user.id
    );

    res.json({
      success: true,
      message: 'Salaire mis à jour avec succès',
      professor
    });
  } catch (error) {
    console.error('❌ Erreur lors de la mise à jour du salaire:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Erreur lors de la mise à jour du salaire'
    });
  }
});

// PUT /api/professors/admin/:id/status - Changer le statut d'emploi (Admin seulement)
router.put('/admin/:id/status', authenticate, authorize(['admin']), async (req, res) => {
  try {
    const { employmentStatus, contractType, endDate } = req.body;

    const updateData = {
      updatedBy: req.user.id
    };

    if (employmentStatus) updateData.employmentStatus = employmentStatus;
    if (contractType) updateData.contractType = contractType;
    if (endDate) updateData.endDate = endDate;

    const professor = await Professor.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true }
    ).populate('user', '-password');

    if (!professor) {
      return res.status(404).json({
        success: false,
        message: 'Professeur non trouvé'
      });
    }

    console.log(`📋 Statut mis à jour pour le professeur ${professor.employeeNumber}`);

    res.json({
      success: true,
      message: 'Statut mis à jour avec succès',
      professor
    });
  } catch (error) {
    console.error('❌ Erreur lors de la mise à jour du statut:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Erreur lors de la mise à jour du statut'
    });
  }
});

// GET /api/professors/admin/stats - Statistiques des professeurs (Admin seulement)
router.get('/admin/stats', authenticate, authorize(['admin']), async (req, res) => {
  try {
    const stats = await Professor.aggregate([
      {
        $group: {
          _id: null,
          total: { $sum: 1 },
          byDepartment: {
            $push: {
              department: '$department',
              employmentStatus: '$employmentStatus'
            }
          },
          byStatus: {
            $push: '$employmentStatus'
          },
          totalSalaryBudget: {
            $sum: '$salary.amount'
          }
        }
      }
    ]);

    const departmentStats = await Professor.aggregate([
      {
        $group: {
          _id: '$department',
          count: { $sum: 1 },
          avgSalary: { $avg: '$salary.amount' }
        }
      }
    ]);

    const statusStats = await Professor.aggregate([
      {
        $group: {
          _id: '$employmentStatus',
          count: { $sum: 1 }
        }
      }
    ]);

    res.json({
      success: true,
      stats: {
        total: stats[0]?.total || 0,
        totalSalaryBudget: stats[0]?.totalSalaryBudget || 0,
        byDepartment: departmentStats,
        byStatus: statusStats
      }
    });
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des statistiques:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des statistiques'
    });
  }
});

// GET /api/professors/dashboard/stats - Statistiques du dashboard professeur
router.get('/dashboard/stats', authenticate, authorize(['professor']), async (req, res) => {
  try {
    console.log('📊 PROFESSOR DASHBOARD - Utilisateur connecté:', {
      id: req.user._id,
      email: req.user.email,
      role: req.user.role
    });
    const professor = await Professor.findOne({ user: req.user._id });

    if (!professor) {
      return res.status(404).json({
        success: false,
        message: 'Profil professeur non trouvé'
      });
    }

    // Calculer les statistiques basées sur les cours d'exemple
    // TODO: Remplacer par de vraies données quand les modèles seront connectés
    const exampleCourses = [
      { enrolledStudents: 25, weeklyHours: 4 },
      { enrolledStudents: 30, weeklyHours: 3 },
      { enrolledStudents: 22, weeklyHours: 4 }
    ];

    const stats = {
      totalCourses: exampleCourses.length,
      totalStudents: exampleCourses.reduce((sum, course) => sum + course.enrolledStudents, 0),
      weeklyHours: exampleCourses.reduce((sum, course) => sum + course.weeklyHours, 0),
      averageRating: 4.2
    };

    res.json({
      success: true,
      stats
    });
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des statistiques:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des statistiques'
    });
  }
});

// GET /api/professors/dashboard/upcoming-classes - Cours à venir
router.get('/dashboard/upcoming-classes', authenticate, async (req, res) => {
  try {
    const professor = await Professor.findOne({ user: req.user.id });

    if (!professor) {
      return res.status(404).json({
        success: false,
        message: 'Profil professeur non trouvé'
      });
    }

    // Pour l'instant, retourner des données d'exemple
    // TODO: Implémenter les vraies données quand les modèles Course, Schedule seront prêts
    const upcomingClasses = [
      {
        courseName: 'Management Stratégique',
        time: 'Aujourd\'hui 14:00 - 16:00',
        room: 'A101',
        studentCount: 25,
        status: 'Confirmé'
      },
      {
        courseName: 'Leadership et Innovation',
        time: 'Demain 09:00 - 11:00',
        room: 'B205',
        studentCount: 30,
        status: 'Confirmé'
      }
    ];

    res.json({
      success: true,
      classes: upcomingClasses
    });
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des cours:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des cours'
    });
  }
});

// PUT /api/professors/profile - Mettre à jour le profil du professeur
router.put('/profile', authenticate, async (req, res) => {
  try {
    const professor = await Professor.findOne({ user: req.user.id });

    if (!professor) {
      return res.status(404).json({
        success: false,
        message: 'Profil professeur non trouvé'
      });
    }

    const allowedUpdates = [
      'professionalProfile.bio',
      'professionalProfile.expertise',
      'professionalProfile.consultingAreas',
      'professionalProfile.linkedinUrl',
      'professionalProfile.personalWebsite',
      'skills.technicalSkills',
      'skills.languages',
      'teachingExperience.teachingAreas',
      'teachingExperience.preferredTeachingMethods',
      'availability.preferredDays',
      'availability.preferredTimeSlots',
      'availability.canTravelForTeaching',
      'officeHours'
    ];

    // Mettre à jour seulement les champs autorisés
    Object.keys(req.body).forEach(key => {
      if (allowedUpdates.includes(key)) {
        if (key.includes('.')) {
          const [parent, child] = key.split('.');
          if (!professor[parent]) professor[parent] = {};
          professor[parent][child] = req.body[key];
        } else {
          professor[key] = req.body[key];
        }
      }
    });

    professor.updatedBy = req.user.id;
    await professor.save();

    const updatedProfessor = await Professor.findById(professor._id)
      .populate('user', '-password -resetPasswordToken -verificationToken');

    res.json({
      success: true,
      message: 'Profil mis à jour avec succès',
      professor: updatedProfessor
    });
  } catch (error) {
    console.error('❌ Erreur lors de la mise à jour du profil:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour du profil'
    });
  }
});



// GET /api/professors/courses/:courseId/students - Obtenir les étudiants d'un cours
router.get('/courses/:courseId/students', authenticate, authorize('professor'), async (req, res) => {
  try {
    const { courseId } = req.params;
    console.log('👥 Récupération des étudiants pour le cours:', courseId);

    // Pour l'instant, retourner des données d'exemple
    const exampleStudents = [
      {
        _id: '674d1234567890abcdef0001',
        studentId: 'EMBA2024001',
        firstName: 'Ahmed',
        lastName: 'Ben Ali',
        email: '<EMAIL>',
        phone: '+216 20 123 456',
        enrollmentDate: '2024-09-15',
        status: 'active',
        averageGrade: 15.2,
        attendanceRate: 95
      },
      {
        _id: '674d1234567890abcdef0002',
        studentId: 'EMBA2024002',
        firstName: 'Fatma',
        lastName: 'Trabelsi',
        email: '<EMAIL>',
        phone: '+216 22 987 654',
        enrollmentDate: '2024-09-15',
        status: 'active',
        averageGrade: 14.8,
        attendanceRate: 88
      },
      {
        _id: '674d1234567890abcdef0003',
        studentId: 'EMBA2024003',
        firstName: 'Mohamed',
        lastName: 'Gharbi',
        email: '<EMAIL>',
        phone: '+216 25 456 789',
        enrollmentDate: '2024-09-15',
        status: 'active',
        averageGrade: 16.5,
        attendanceRate: 92
      }
    ];

    res.json({
      success: true,
      students: exampleStudents,
      courseId: courseId
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des étudiants:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des étudiants',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// POST /api/professors/courses/:courseId/grades - Sauvegarder les notes
router.post('/courses/:courseId/grades', authenticate, authorize('professor'), async (req, res) => {
  try {
    const { courseId } = req.params;
    const { studentId, assignmentId, score, feedback } = req.body;

    console.log('📝 Sauvegarde de note:', { courseId, studentId, assignmentId, score });

    // TODO: Implémenter la vraie logique de sauvegarde des notes

    res.json({
      success: true,
      message: 'Note sauvegardée avec succès',
      grade: {
        studentId,
        assignmentId,
        score,
        feedback,
        gradedAt: new Date(),
        gradedBy: req.user._id
      }
    });

  } catch (error) {
    console.error('❌ Erreur lors de la sauvegarde de la note:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la sauvegarde de la note',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// GET /api/professors/courses/:courseId/grades - Obtenir les notes d'un cours
router.get('/courses/:courseId/grades', authenticate, authorize('professor'), async (req, res) => {
  try {
    const { courseId } = req.params;
    console.log('📊 Récupération des notes pour le cours:', courseId);

    // Pour l'instant, retourner des données d'exemple
    const exampleGrades = [
      {
        _id: '674d1234567890abcdef1001',
        student: {
          _id: '674d1234567890abcdef0001',
          firstName: 'Ahmed',
          lastName: 'Ben Ali',
          email: '<EMAIL>'
        },
        type: 'Examen',
        score: 16,
        maxScore: 20,
        date: '2024-12-10T10:00:00Z',
        feedback: 'Excellent travail, bonne maîtrise des concepts'
      },
      {
        _id: '674d1234567890abcdef1002',
        student: {
          _id: '674d1234567890abcdef0002',
          firstName: 'Fatma',
          lastName: 'Trabelsi',
          email: '<EMAIL>'
        },
        type: 'Projet',
        score: 14,
        maxScore: 20,
        date: '2024-12-08T14:00:00Z',
        feedback: 'Bon travail, quelques améliorations possibles'
      },
      {
        _id: '674d1234567890abcdef1003',
        student: {
          _id: '674d1234567890abcdef0003',
          firstName: 'Mohamed',
          lastName: 'Gharbi',
          email: '<EMAIL>'
        },
        type: 'Contrôle',
        score: 18,
        maxScore: 20,
        date: '2024-12-05T09:00:00Z',
        feedback: 'Très bon niveau, continuez ainsi'
      }
    ];

    res.json({
      success: true,
      grades: exampleGrades,
      courseId: courseId
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des notes:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des notes',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// GET /api/professors/courses/:courseId/attendance - Obtenir les présences d'un cours
router.get('/courses/:courseId/attendance', authenticate, authorize('professor'), async (req, res) => {
  try {
    const { courseId } = req.params;
    console.log('📅 Récupération des présences pour le cours:', courseId);

    // Pour l'instant, retourner des données d'exemple
    const exampleAttendance = [
      {
        _id: '674d1234567890abcdef2001',
        date: '2024-12-16T14:00:00Z',
        students: [
          {
            studentId: '674d1234567890abcdef0001',
            firstName: 'Ahmed',
            lastName: 'Ben Ali',
            status: 'present'
          },
          {
            studentId: '674d1234567890abcdef0002',
            firstName: 'Fatma',
            lastName: 'Trabelsi',
            status: 'absent'
          },
          {
            studentId: '674d1234567890abcdef0003',
            firstName: 'Mohamed',
            lastName: 'Gharbi',
            status: 'present'
          }
        ]
      },
      {
        _id: '674d1234567890abcdef2002',
        date: '2024-12-14T14:00:00Z',
        students: [
          {
            studentId: '674d1234567890abcdef0001',
            firstName: 'Ahmed',
            lastName: 'Ben Ali',
            status: 'present'
          },
          {
            studentId: '674d1234567890abcdef0002',
            firstName: 'Fatma',
            lastName: 'Trabelsi',
            status: 'present'
          },
          {
            studentId: '674d1234567890abcdef0003',
            firstName: 'Mohamed',
            lastName: 'Gharbi',
            status: 'present'
          }
        ]
      }
    ];

    res.json({
      success: true,
      attendance: exampleAttendance,
      courseId: courseId
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des présences:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des présences',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// POST /api/professors/courses/:courseId/attendance - Sauvegarder la présence
router.post('/courses/:courseId/attendance', authenticate, authorize('professor'), async (req, res) => {
  try {
    const { courseId } = req.params;
    const { date, attendance, notes } = req.body;

    console.log('✅ Sauvegarde de présence:', { courseId, date, attendance: Object.keys(attendance).length });

    // TODO: Implémenter la vraie logique de sauvegarde de présence

    res.json({
      success: true,
      message: 'Présence sauvegardée avec succès',
      attendance: {
        courseId,
        date,
        attendance,
        notes,
        recordedBy: req.user._id,
        recordedAt: new Date()
      }
    });

  } catch (error) {
    console.error('❌ Erreur lors de la sauvegarde de la présence:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la sauvegarde de la présence',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// GET /api/professors/my-students - Obtenir tous les étudiants des classes assignées au professeur
router.get('/my-students', authenticate, authorize('professor'), async (req, res) => {
  try {
    console.log('👥 Récupération des étudiants pour le professeur:', req.user?.email);

    // Trouver le professeur dans la base de données
    const professor = await Professor.findOne({ user: req.user.id });

    if (!professor) {
      return res.status(404).json({
        success: false,
        message: 'Profil professeur non trouvé'
      });
    }

    // Récupérer les affectations où ce professeur est impliqué
    const CourseAssignment = require('../models/CourseAssignment');
    const assignments = await CourseAssignment.find({
      'instructors.professor': professor._id,
      status: 'active'
    })
    .populate('course', 'courseCode title description')
    .sort({ createdAt: -1 });

    console.log('📚 Affectations trouvées:', assignments.length);

    // Extraire toutes les classes uniques
    const classesSet = new Set();
    const coursesByClass = {};

    assignments.forEach(assignment => {
      assignment.classes.forEach(classInfo => {
        classesSet.add(classInfo.className);

        if (!coursesByClass[classInfo.className]) {
          coursesByClass[classInfo.className] = [];
        }

        coursesByClass[classInfo.className].push({
          id: assignment.course._id,
          code: assignment.course.courseCode,
          title: assignment.course.title,
          description: assignment.course.description
        });
      });
    });

    const uniqueClasses = Array.from(classesSet);
    console.log('🎓 Classes uniques:', uniqueClasses);

    // Récupérer les étudiants pour chaque classe
    const Student = require('../models/Student');
    const classesList = [];

    for (const className of uniqueClasses) {
      const students = await Student.find({ class: className })
        .populate('user', 'firstName lastName email phone profilePicture')
        .sort({ studentNumber: 1 })
        .lean();

      classesList.push({
        className: className,
        promotion: className.substring(0, 5), // EMBA1 ou EMBA2
        studentCount: students.length,
        students: students.map(student => ({
          id: student._id,
          studentId: student.studentNumber || student.studentId,
          firstName: student.user?.firstName || 'N/A',
          lastName: student.user?.lastName || 'N/A',
          email: student.user?.email || 'N/A',
          phone: student.user?.phone || 'N/A',
          profilePicture: student.user?.profilePicture,
          enrollmentDate: student.enrollmentDate,
          status: student.status || 'active',
          averageGrade: student.averageGrade || 0,
          attendanceRate: student.attendanceRate || 0
        })),
        courses: coursesByClass[className] || []
      });
    }

    console.log('✅ Classes avec étudiants préparées:', classesList.length);

    res.json({
      success: true,
      professor: {
        id: professor._id,
        name: `${professor.user?.firstName || ''} ${professor.user?.lastName || ''}`.trim(),
        email: professor.user?.email
      },
      classes: classesList,
      totalClasses: classesList.length,
      totalStudents: classesList.reduce((sum, cls) => sum + cls.studentCount, 0)
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des étudiants:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des étudiants',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;
