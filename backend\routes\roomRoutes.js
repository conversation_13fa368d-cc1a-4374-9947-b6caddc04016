const express = require('express');
const router = express.Router();
const { authenticate, authorize } = require('../middleware/auth');

// GET /api/rooms - Obtenir toutes les salles pour l'admin
router.get('/', authenticate, async (req, res) => {
  try {
    console.log('🏢 Récupération des salles');

    // Pour l'instant, retourner des salles par défaut
    // À remplacer par une vraie base de données de salles
    const rooms = [
      { 
        id: 1, 
        name: 'A101', 
        capacity: 30, 
        type: 'classroom', 
        equipment: ['Projecteur', 'Tableau'],
        building: 'Bâtiment A',
        floor: 1,
        status: 'available'
      },
      { 
        id: 2, 
        name: 'A102', 
        capacity: 25, 
        type: 'classroom', 
        equipment: ['Projecteur', 'Ordinateurs'],
        building: 'Bâtiment A',
        floor: 1,
        status: 'available'
      },
      { 
        id: 3, 
        name: 'B201', 
        capacity: 50, 
        type: 'amphitheater', 
        equipment: ['Projecteur', 'Micro', 'Caméra'],
        building: 'Bâtiment B',
        floor: 2,
        status: 'available'
      },
      { 
        id: 4, 
        name: 'Lab1', 
        capacity: 20, 
        type: 'laboratory', 
        equipment: ['Ordinateurs', 'Logiciels'],
        building: 'Bâtiment C',
        floor: 1,
        status: 'available'
      },
      { 
        id: 5, 
        name: 'Conf1', 
        capacity: 15, 
        type: 'meeting_room', 
        equipment: ['Projecteur', 'Tableau blanc', 'Visioconférence'],
        building: 'Bâtiment A',
        floor: 2,
        status: 'available'
      }
    ];

    console.log(`✅ ${rooms.length} salles récupérées`);

    res.json({
      success: true,
      rooms
    });
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des salles:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des salles',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// GET /api/rooms/:id - Obtenir une salle par ID
router.get('/:id', authenticate, async (req, res) => {
  try {
    const roomId = parseInt(req.params.id);
    
    // Simuler la recherche d'une salle
    const rooms = [
      { id: 1, name: 'A101', capacity: 30, type: 'classroom', equipment: ['Projecteur', 'Tableau'] },
      { id: 2, name: 'A102', capacity: 25, type: 'classroom', equipment: ['Projecteur', 'Ordinateurs'] },
      { id: 3, name: 'B201', capacity: 50, type: 'amphitheater', equipment: ['Projecteur', 'Micro', 'Caméra'] },
      { id: 4, name: 'Lab1', capacity: 20, type: 'laboratory', equipment: ['Ordinateurs', 'Logiciels'] }
    ];

    const room = rooms.find(r => r.id === roomId);

    if (!room) {
      return res.status(404).json({
        success: false,
        message: 'Salle non trouvée'
      });
    }

    res.json({
      success: true,
      room
    });
  } catch (error) {
    console.error('❌ Erreur lors de la récupération de la salle:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de la salle',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// POST /api/rooms - Créer une nouvelle salle
router.post('/', authenticate, authorize(['admin']), async (req, res) => {
  try {
    console.log('➕ Création d\'une nouvelle salle:', req.body);

    const { name, capacity, type, equipment, building, floor } = req.body;

    // Simuler la création d'une salle
    const newRoom = {
      id: Date.now(), // ID temporaire
      name,
      capacity: capacity || 20,
      type: type || 'classroom',
      equipment: equipment || [],
      building: building || 'Bâtiment A',
      floor: floor || 1,
      status: 'available',
      createdAt: new Date()
    };

    console.log('✅ Salle créée avec succès:', newRoom.name);

    res.status(201).json({
      success: true,
      message: 'Salle créée avec succès',
      room: newRoom
    });
  } catch (error) {
    console.error('❌ Erreur lors de la création de la salle:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la création de la salle',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// PUT /api/rooms/:id - Mettre à jour une salle
router.put('/:id', authenticate, authorize(['admin']), async (req, res) => {
  try {
    console.log(`📝 Mise à jour de la salle ${req.params.id}:`, req.body);

    const roomId = parseInt(req.params.id);

    // Simuler la mise à jour
    const updatedRoom = {
      id: roomId,
      ...req.body,
      updatedAt: new Date()
    };

    console.log('✅ Salle mise à jour avec succès');

    res.json({
      success: true,
      message: 'Salle mise à jour avec succès',
      room: updatedRoom
    });
  } catch (error) {
    console.error('❌ Erreur lors de la mise à jour de la salle:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour de la salle',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// DELETE /api/rooms/:id - Supprimer une salle
router.delete('/:id', authenticate, authorize(['admin']), async (req, res) => {
  try {
    console.log(`🗑️ Suppression de la salle ${req.params.id}`);

    // Simuler la suppression
    console.log('✅ Salle supprimée avec succès');

    res.json({
      success: true,
      message: 'Salle supprimée avec succès'
    });
  } catch (error) {
    console.error('❌ Erreur lors de la suppression de la salle:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la suppression de la salle',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;
