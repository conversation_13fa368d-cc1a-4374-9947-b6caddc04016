const express = require('express');
const router = express.Router();
const Schedule = require('../models/Schedule');
const CourseAssignment = require('../models/CourseAssignment');
const { authenticate, authorize } = require('../middleware/auth');

// GET /api/schedule/debug - Route de debug pour vérifier les affectations
router.get('/debug', authenticate, async (req, res) => {
  try {
    const assignments = await CourseAssignment.find()
      .populate('course', 'courseCode title')
      .populate('instructors.professor', 'user')
      .populate('instructors.professor.user', 'firstName lastName');

    const debug = assignments.map(assignment => ({
      id: assignment._id,
      courseCode: assignment.course?.courseCode,
      courseTitle: assignment.course?.title,
      status: assignment.status,
      hasSchedule: !!assignment.schedule,
      schedule: assignment.schedule,
      instructorsCount: assignment.instructors?.length || 0,
      classesCount: assignment.classes?.length || 0,
      createdAt: assignment.createdAt
    }));

    res.json({
      success: true,
      totalAssignments: assignments.length,
      assignments: debug
    });
  } catch (error) {
    console.error('❌ Erreur debug:', error);
    res.status(500).json({ message: error.message });
  }
});

// GET /api/schedule/events - Obtenir les événements pour l'interface admin (nouveau système)
router.get('/events', authenticate, async (req, res) => {
  try {
    console.log('📅 Récupération des événements avec filtres:', req.query);

    const { date, view = 'week', professorId, classId } = req.query;

    // Debug: Compter toutes les affectations
    const totalAssignments = await CourseAssignment.countDocuments();
    console.log(`📊 Total des affectations dans la DB: ${totalAssignments}`);

    // Calculer la plage de dates selon la vue
    const targetDate = date ? new Date(date) : new Date();
    let startDate, endDate;

    switch (view) {
      case 'day':
        startDate = new Date(targetDate);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(targetDate);
        endDate.setHours(23, 59, 59, 999);
        break;
      case 'month':
        // Pour la vue mensuelle, récupérer une plage plus large pour inclure tous les cours
        startDate = new Date(targetDate.getFullYear(), targetDate.getMonth(), 1);
        endDate = new Date(targetDate.getFullYear(), targetDate.getMonth() + 1, 0, 23, 59, 59, 999);

        // Étendre la plage pour inclure les cours qui pourraient commencer plus tard
        const extendedStart = new Date(startDate);
        extendedStart.setMonth(extendedStart.getMonth() - 2); // 2 mois avant
        const extendedEnd = new Date(endDate);
        extendedEnd.setMonth(extendedEnd.getMonth() + 6); // 6 mois après

        // Utiliser la plage étendue pour la requête, mais filtrer les événements pour la vue
        startDate = extendedStart;
        endDate = extendedEnd;
        break;
      case 'week':
      default:
        // Début de la semaine (lundi)
        const dayOfWeek = targetDate.getDay();
        const diff = targetDate.getDate() - dayOfWeek + (dayOfWeek === 0 ? -6 : 1);
        startDate = new Date(targetDate.setDate(diff));
        startDate.setHours(0, 0, 0, 0);

        // Fin de la semaine (dimanche)
        endDate = new Date(startDate);
        endDate.setDate(startDate.getDate() + 6);
        endDate.setHours(23, 59, 59, 999);
        break;
    }

    console.log('📅 Plage de dates:', { startDate, endDate });

    // Pour debug : récupérer tous les événements sans filtrage de date
    const debugMode = true;
    if (debugMode) {
      startDate = new Date('2025-01-01');
      endDate = new Date('2026-12-31');
      console.log('🔍 Mode debug: plage étendue', { startDate, endDate });
    }

    // Construire la requête de base
    let query = { status: 'active' };

    // Filtrer par professeur si spécifié
    if (professorId) {
      query['instructors.professor'] = professorId;
    }

    // Filtrer par classe si spécifié
    if (classId) {
      query['classes.className'] = classId;
    }

    // Récupérer les affectations
    const assignments = await CourseAssignment.find(query)
      .populate('course', 'courseCode title description creditHours')
      .populate({
        path: 'instructors.professor',
        populate: {
          path: 'user',
          select: 'firstName lastName email'
        },
        select: 'user academicTitle department'
      });

    console.log(`✅ ${assignments.length} affectations trouvées`);

    // Debug détaillé des affectations
    assignments.forEach((assignment, index) => {
      console.log(`📋 Affectation ${index + 1}:`, {
        courseCode: assignment.course?.courseCode,
        courseTitle: assignment.course?.title,
        status: assignment.status,
        schedule: assignment.schedule,
        instructors: assignment.instructors?.length,
        classes: assignment.classes?.length
      });
    });

    // Générer les événements pour la plage de dates
    const events = [];

    assignments.forEach(assignment => {
      const course = assignment.course;
      const schedule = assignment.schedule;

      console.log(`🔍 Traitement affectation ${course?.courseCode}:`, {
        hasSchedule: !!schedule,
        hasTimeSlots: !!schedule?.timeSlots,
        timeSlotsCount: schedule?.timeSlots?.length || 0,
        startDate: schedule?.startDate,
        durationWeeks: schedule?.durationWeeks
      });

      if (!schedule || !schedule.timeSlots || schedule.timeSlots.length === 0) {
        console.log(`⚠️ Pas de planning pour ${course?.courseCode}`);
        return;
      }

      // Pour chaque créneau horaire de l'affectation
      schedule.timeSlots.forEach(timeSlot => {
        // Générer les occurrences pour la plage de dates
        console.log(`🔍 Génération occurrences pour ${course?.courseCode}:`, {
          courseStartDate: schedule.startDate,
          durationWeeks: schedule.durationWeeks,
          timeSlot: timeSlot.dayOfWeek,
          rangeStart: startDate,
          rangeEnd: endDate
        });

        const occurrences = generateOccurrences(
          schedule.startDate,
          schedule.durationWeeks,
          timeSlot,
          startDate,
          endDate
        );

        console.log(`📅 ${occurrences.length} occurrences générées pour ${course?.courseCode}`);

        occurrences.forEach(occurrence => {
          // Informations sur les professeurs
          const instructors = assignment.instructors.map(instructor => ({
            id: instructor.professor._id,
            name: instructor.professor.user ?
              `${instructor.professor.user.firstName} ${instructor.professor.user.lastName}` :
              'Nom non disponible',
            role: instructor.role,
            academicTitle: instructor.professor.academicTitle
          }));

          // Informations sur les classes
          const classes = assignment.classes.map(cls => cls.className);

          events.push({
            id: `${assignment._id}_${timeSlot.dayOfWeek}_${occurrence.toISOString().split('T')[0]}`,
            title: course.title,
            courseCode: course.courseCode,
            description: course.description,
            start: occurrence.toISOString(),
            end: new Date(occurrence.getTime() + (3 * 60 + 15) * 60 * 1000).toISOString(), // 3h15
            instructors: instructors,
            classes: classes,
            room: {
              id: timeSlot.location?.room || 'TBD',
              name: timeSlot.location?.room || 'Salle à définir'
            },
            type: 'course',
            status: assignment.status,
            assignmentId: assignment._id,
            courseId: course._id,
            color: getEventColor('course')
          });
        });
      });
    });

    // Trier les événements par date
    events.sort((a, b) => new Date(a.start) - new Date(b.start));

    console.log(`📅 ${events.length} événements générés`);

    res.json({
      success: true,
      events: events,
      period: {
        start: startDate,
        end: endDate,
        view: view
      }
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des événements:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des événements',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Fonction utilitaire pour générer les occurrences d'un cours
function generateOccurrences(startDate, durationWeeks, timeSlot, rangeStart, rangeEnd) {
  const occurrences = [];
  const courseStart = new Date(startDate);

  // Mapper les jours de la semaine
  const dayMap = {
    'Monday': 1, 'Tuesday': 2, 'Wednesday': 3, 'Thursday': 4,
    'Friday': 5, 'Saturday': 6, 'Sunday': 0
  };

  const targetDay = dayMap[timeSlot.dayOfWeek];

  // Trouver le premier jour correspondant
  let currentDate = new Date(courseStart);
  while (currentDate.getDay() !== targetDay) {
    currentDate.setDate(currentDate.getDate() + 1);
  }

  // Générer les occurrences pour la durée du cours
  for (let week = 0; week < durationWeeks; week++) {
    const occurrenceDate = new Date(currentDate);
    occurrenceDate.setDate(currentDate.getDate() + (week * 7));

    // Ajouter l'heure
    const [hours, minutes] = timeSlot.startTime.split(':');
    occurrenceDate.setHours(parseInt(hours), parseInt(minutes), 0, 0);

    // Vérifier si l'occurrence est dans la plage demandée
    if (occurrenceDate >= rangeStart && occurrenceDate <= rangeEnd) {
      occurrences.push(new Date(occurrenceDate));
    }
  }

  return occurrences;
}

// Fonction utilitaire pour obtenir la couleur d'un événement
function getEventColor(type) {
  const colors = {
    course: '#dc2626',
    exam: '#ea580c',
    meeting: '#7c3aed',
    maintenance: '#d97706'
  };
  return colors[type] || '#dc2626';
}

router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 10, date, instructor, course } = req.query;
    const query = {};
    if (date) query.date = { $gte: new Date(date) };
    if (instructor) query.instructor = instructor;
    if (course) query.course = course;
    
    const schedules = await Schedule.find(query)
      .populate('instructor', 'user academicTitle')
      .populate('course', 'courseCode title')
      .populate('module', 'moduleCode title')
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ date: 1, startTime: 1 });
    
    const total = await Schedule.countDocuments(query);
    res.json({ schedules, totalPages: Math.ceil(total / limit), currentPage: page, total });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

router.get('/:id', async (req, res) => {
  try {
    const schedule = await Schedule.findById(req.params.id)
      .populate('instructor', 'user academicTitle')
      .populate('course', 'courseCode title')
      .populate('participants.expectedStudents', 'studentNumber user');
    
    if (!schedule) return res.status(404).json({ message: 'Schedule not found' });
    res.json(schedule);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

router.post('/', async (req, res) => {
  try {
    const schedule = new Schedule(req.body);
    await schedule.save();
    res.status(201).json(schedule);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.put('/:id', async (req, res) => {
  try {
    const schedule = await Schedule.findByIdAndUpdate(req.params.id, req.body, { new: true });
    if (!schedule) return res.status(404).json({ message: 'Schedule not found' });
    res.json(schedule);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.post('/:id/attendance', async (req, res) => {
  try {
    const { studentId, status, recordedBy, notes } = req.body;
    const schedule = await Schedule.findById(req.params.id);
    
    if (!schedule) return res.status(404).json({ message: 'Schedule not found' });
    
    await schedule.markAttendance(studentId, status, recordedBy, notes);
    res.json({ message: 'Attendance marked successfully' });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// GET /api/schedule/class/:className - Emploi du temps d'une classe
router.get('/class/:className', authenticate, async (req, res) => {
  try {
    const { className } = req.params;

    // Récupérer toutes les affectations pour cette classe
    const assignments = await CourseAssignment.find({
      'classes.className': className,
      status: 'active'
    })
    .populate('course', 'courseCode title description')
    .populate('instructors.professor', 'user academicTitle')
    .populate('instructors.professor.user', 'firstName lastName')
    .sort({ 'schedule.timeSlots.dayOfWeek': 1, 'schedule.timeSlots.startTime': 1 });

    const schedule = [];
    const dayOrder = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

    assignments.forEach(assignment => {
      assignment.schedule.timeSlots.forEach(timeSlot => {
        const primaryInstructor = assignment.instructors.find(i => i.role === 'primary') || assignment.instructors[0];

        schedule.push({
          courseId: assignment.course._id,
          courseCode: assignment.course.courseCode,
          courseName: assignment.course.title,
          dayOfWeek: getDayLabel(timeSlot.dayOfWeek),
          dayOfWeekEn: timeSlot.dayOfWeek,
          startTime: timeSlot.startTime,
          endTime: timeSlot.endTime,
          duration: calculateDuration(timeSlot.startTime, timeSlot.endTime),
          professorName: primaryInstructor?.professor?.user ?
            `${primaryInstructor.professor.user.firstName} ${primaryInstructor.professor.user.lastName}` :
            'Non assigné',
          professorTitle: primaryInstructor?.professor?.academicTitle || '',
          location: timeSlot.location ?
            `${timeSlot.location.building || ''} ${timeSlot.location.room || ''}`.trim() || 'À définir' :
            'À définir',
          building: timeSlot.location?.building || '',
          room: timeSlot.location?.room || '',
          capacity: timeSlot.location?.capacity || 30
        });
      });
    });

    // Trier par jour puis par heure
    schedule.sort((a, b) => {
      const dayA = dayOrder.indexOf(a.dayOfWeekEn);
      const dayB = dayOrder.indexOf(b.dayOfWeekEn);
      if (dayA !== dayB) return dayA - dayB;
      return a.startTime.localeCompare(b.startTime);
    });

    res.json({
      success: true,
      className,
      schedule,
      totalSessions: schedule.length
    });
  } catch (error) {
    console.error('Erreur lors de la récupération de l\'emploi du temps:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération de l\'emploi du temps'
    });
  }
});

// GET /api/schedule/professor/:professorId - Emploi du temps d'un professeur
router.get('/professor/:professorId', authenticate, async (req, res) => {
  try {
    const { professorId } = req.params;

    // Récupérer toutes les affectations pour ce professeur
    const assignments = await CourseAssignment.find({
      'instructors.professor': professorId,
      status: 'active'
    })
    .populate('course', 'courseCode title description')
    .populate('instructors.professor', 'user academicTitle')
    .populate('instructors.professor.user', 'firstName lastName')
    .sort({ 'schedule.timeSlots.dayOfWeek': 1, 'schedule.timeSlots.startTime': 1 });

    const schedule = [];
    const dayOrder = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

    assignments.forEach(assignment => {
      // Vérifier si ce professeur est dans cette affectation
      const professorRole = assignment.instructors.find(i => i.professor._id.toString() === professorId);
      if (!professorRole) return;

      assignment.schedule.timeSlots.forEach(timeSlot => {
        // Récupérer toutes les classes pour cette affectation
        const classNames = assignment.classes.map(c => c.className).join(', ');

        schedule.push({
          courseId: assignment.course._id,
          courseCode: assignment.course.courseCode,
          courseName: assignment.course.title,
          classes: classNames,
          classCount: assignment.classes.length,
          dayOfWeek: getDayLabel(timeSlot.dayOfWeek),
          dayOfWeekEn: timeSlot.dayOfWeek,
          startTime: timeSlot.startTime,
          endTime: timeSlot.endTime,
          duration: calculateDuration(timeSlot.startTime, timeSlot.endTime),
          role: professorRole.role,
          location: timeSlot.location ?
            `${timeSlot.location.building || ''} ${timeSlot.location.room || ''}`.trim() || 'À définir' :
            'À définir',
          building: timeSlot.location?.building || '',
          room: timeSlot.location?.room || '',
          capacity: timeSlot.location?.capacity || 30
        });
      });
    });

    // Trier par jour puis par heure
    schedule.sort((a, b) => {
      const dayA = dayOrder.indexOf(a.dayOfWeekEn);
      const dayB = dayOrder.indexOf(b.dayOfWeekEn);
      if (dayA !== dayB) return dayA - dayB;
      return a.startTime.localeCompare(b.startTime);
    });

    res.json({
      success: true,
      professorId,
      schedule,
      totalSessions: schedule.length
    });
  } catch (error) {
    console.error('Erreur lors de la récupération de l\'emploi du temps du professeur:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération de l\'emploi du temps'
    });
  }
});

// Fonctions utilitaires
function getDayLabel(day) {
  const labels = {
    'Monday': 'Lundi',
    'Tuesday': 'Mardi',
    'Wednesday': 'Mercredi',
    'Thursday': 'Jeudi',
    'Friday': 'Vendredi',
    'Saturday': 'Samedi'
  };
  return labels[day] || day;
}

function calculateDuration(startTime, endTime) {
  const start = new Date(`2000-01-01T${startTime}:00`);
  const end = new Date(`2000-01-01T${endTime}:00`);
  const diffMs = end - start;
  const diffHours = diffMs / (1000 * 60 * 60);

  if (diffHours === 1) return '1h';
  if (diffHours < 1) return `${Math.round(diffHours * 60)}min`;

  const hours = Math.floor(diffHours);
  const minutes = Math.round((diffHours - hours) * 60);

  if (minutes === 0) return `${hours}h`;
  return `${hours}h${minutes.toString().padStart(2, '0')}`;
}

module.exports = router;
