const express = require('express');
const router = express.Router();
const Student = require('../models/Student');
const Course = require('../models/Course');
const Enrollment = require('../models/Enrollment');
const Assignment = require('../models/Assignment');
const Submission = require('../models/Submission');
const { authenticate, authorize } = require('../middleware/auth');

// Route de test simple au début
router.get('/test-simple', (req, res) => {
  console.log('🧪 Route de test simple appelée - VERSION 2');
  res.json({ success: true, message: 'Test simple fonctionne - VERSION 2' });
});

// Route de test pour voir les utilisateurs étudiants (sans auth pour debug)
router.get('/test-users', async (req, res) => {
  try {
    const User = require('../models/User');
    const users = await User.find({ role: 'student' }, 'firstName lastName email role').limit(5);
    const students = await Student.find({}).populate('user', 'firstName lastName email role').limit(5);

    res.json({
      success: true,
      users: users,
      students: students,
      message: 'Utilisateurs étudiants trouvés'
    });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Route de test avec authentification
router.get('/test-auth', authenticate, authorize(['admin', 'staff', 'professor']), (req, res) => {
  console.log('🔐 Route de test avec auth appelée');
  res.json({ success: true, message: 'Test avec auth fonctionne' });
});

// GET /api/students - Obtenir tous les étudiants
router.get('/', authenticate, authorize('admin', 'staff', 'professor'), async (req, res) => {
  try {
    const { page = 1, limit = 10, cohort, enrollmentStatus, academicYear } = req.query;
    const query = {};

    if (cohort) query.cohort = cohort;
    if (enrollmentStatus) query.enrollmentStatus = enrollmentStatus;
    if (academicYear) query.academicYear = academicYear;

    const students = await Student.find(query)
      .populate('user', 'firstName lastName email phone profilePicture')
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });

    const total = await Student.countDocuments(query);

    res.json({
      students,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// ROUTE DÉPLACÉE VERS LA FIN DU FICHIER POUR ÉVITER LES CONFLITS AVEC /me

// POST /api/students - Créer un nouvel étudiant avec données utilisateur
router.post('/', authenticate, authorize('admin', 'staff'), async (req, res) => {
  let createdUser = null;

  try {
    const { firstName, lastName, email, phone, ...studentData } = req.body;

    console.log('Données reçues:', { firstName, lastName, email, phone, ...studentData });

    // 1. Créer d'abord l'utilisateur
    const User = require('../models/User');
    const userData = {
      firstName,
      lastName,
      email,
      phone,
      role: 'student',
      password: generateTemporaryPassword(),
      isActive: true
    };

    const user = new User(userData);
    await user.save();
    createdUser = user; // Garder référence pour cleanup si nécessaire
    console.log('Utilisateur créé:', user._id);

    // 2. Générer le numéro d'étudiant
    const studentNumber = await generateStudentNumber();
    console.log('Numéro étudiant généré:', studentNumber);

    // 3. Créer l'étudiant avec la référence utilisateur
    const completeStudentData = {
      ...studentData,
      user: user._id,
      studentNumber
    };

    console.log('Données étudiant complètes:', completeStudentData);

    const student = new Student(completeStudentData);
    await student.save();
    console.log('Étudiant créé:', student._id);

    // 4. Retourner l'étudiant avec les données utilisateur populées
    const populatedStudent = await Student.findById(student._id)
      .populate('user', 'firstName lastName email phone profilePicture');

    console.log('✅ Étudiant créé avec succès:', populatedStudent.studentNumber);
    res.status(201).json(populatedStudent);

  } catch (error) {
    console.error('❌ Erreur création étudiant:', error);

    // Si l'utilisateur a été créé mais pas l'étudiant, le supprimer
    if (createdUser && error.name !== 'MongoError' && error.code !== 11000) {
      try {
        await User.findByIdAndDelete(createdUser._id);
        console.log('🧹 Utilisateur orphelin supprimé:', createdUser._id);
      } catch (cleanupError) {
        console.error('⚠️ Erreur lors du nettoyage:', cleanupError);
      }
    }

    if (error.code === 11000) {
      if (error.keyPattern?.email) {
        res.status(400).json({ message: 'Cette adresse email est déjà utilisée' });
      } else {
        res.status(400).json({ message: 'Cet étudiant existe déjà' });
      }
    } else {
      res.status(400).json({ message: error.message });
    }
  }
});

// PUT /api/students/:id - Mettre à jour un étudiant
router.put('/:id', authenticate, authorize('admin', 'staff'), async (req, res) => {
  try {
    const student = await Student.findByIdAndUpdate(
      req.params.id,
      { ...req.body, updatedBy: req.body.updatedBy },
      { new: true, runValidators: true }
    ).populate('user', 'firstName lastName email');

    if (!student) {
      return res.status(404).json({ message: 'Student not found' });
    }

    res.json(student);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// DELETE /api/students/:id - Supprimer un étudiant
router.delete('/:id', authenticate, authorize('admin'), async (req, res) => {
  try {
    const student = await Student.findById(req.params.id);

    if (!student) {
      return res.status(404).json({ message: 'Student not found' });
    }

    // Supprimer l'utilisateur associé si nécessaire
    if (student.user) {
      const User = require('../models/User');
      await User.findByIdAndDelete(student.user);
    }

    // Supprimer l'étudiant
    await Student.findByIdAndDelete(req.params.id);

    res.json({ message: 'Student deleted successfully' });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// GET /api/students/:id/academic-progress - Obtenir la progression académique
router.get('/:id/academic-progress', authenticate, authorize('admin', 'staff', 'professor'), async (req, res) => {
  try {
    const student = await Student.findById(req.params.id);

    if (!student) {
      return res.status(404).json({ message: 'Student not found' });
    }

    const progress = student.getAcademicProgress();
    const isEligible = student.isEligibleForGraduation();

    res.json({
      progressPercentage: progress,
      creditsCompleted: student.academicRecord.creditsCompleted,
      creditsRequired: student.academicRecord.creditsRequired,
      currentGPA: student.academicRecord.currentGPA,
      isEligibleForGraduation: isEligible,
      academicStanding: student.academicRecord.academicStanding
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// GET /api/students/:id/attendance - Obtenir les statistiques de présence
router.get('/:id/attendance', authenticate, authorize('admin', 'staff', 'professor'), async (req, res) => {
  try {
    const student = await Student.findById(req.params.id);

    if (!student) {
      return res.status(404).json({ message: 'Student not found' });
    }

    res.json({
      totalSessions: student.attendanceRecord.totalSessions,
      attendedSessions: student.attendanceRecord.attendedSessions,
      attendanceRate: student.attendancePercentage,
      absenceReasons: student.attendanceRecord.absenceReasons
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// GET /api/students/:id/financial - Obtenir les informations financières
router.get('/:id/financial', async (req, res) => {
  try {
    const student = await Student.findById(req.params.id);

    if (!student) {
      return res.status(404).json({ message: 'Student not found' });
    }

    res.json({
      tuitionFee: student.tuitionFee,
      remainingBalance: student.remainingBalance,
      paymentPlan: student.tuitionFee.paymentPlan
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// POST /api/students/:id/notes - Ajouter une note
router.post('/:id/notes', async (req, res) => {
  try {
    const { content, category, isPrivate, author } = req.body;
    const student = await Student.findById(req.params.id);

    if (!student) {
      return res.status(404).json({ message: 'Student not found' });
    }

    student.notes.push({
      author,
      content,
      category: category || 'academic',
      isPrivate: isPrivate || false
    });

    await student.save();

    res.status(201).json({ message: 'Note added successfully' });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// POST /api/students/:id/documents - Ajouter un document
router.post('/:id/documents', async (req, res) => {
  try {
    const student = await Student.findById(req.params.id);

    if (!student) {
      return res.status(404).json({ message: 'Student not found' });
    }

    student.documents.push(req.body);
    await student.save();

    res.status(201).json({ message: 'Document added successfully' });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// GET /api/students/search - Rechercher des étudiants
router.get('/search', async (req, res) => {
  try {
    const { q, cohort, program, limit = 10 } = req.query;

    if (!q) {
      return res.status(400).json({ message: 'Search query is required' });
    }

    const query = {
      $or: [
        { studentNumber: { $regex: q, $options: 'i' } }
      ]
    };

    if (cohort) query.cohort = cohort;
    if (program) query.program = program;

    const students = await Student.find(query)
      .populate('user', 'firstName lastName email')
      .limit(parseInt(limit))
      .sort({ studentNumber: 1 });

    res.json(students);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// GET /api/students/debug - Debug: vérifier les collections
router.get('/debug', async (req, res) => {
  try {
    const User = require('../models/User');

    const usersCount = await User.countDocuments({ role: 'student' });
    const studentsCount = await Student.countDocuments();

    const recentUsers = await User.find({ role: 'student' })
      .select('firstName lastName email createdAt')
      .sort({ createdAt: -1 })
      .limit(5);

    const recentStudents = await Student.find()
      .populate('user', 'firstName lastName email')
      .sort({ createdAt: -1 })
      .limit(5);

    res.json({
      counts: {
        users: usersCount,
        students: studentsCount
      },
      recentUsers,
      recentStudents
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// GET /api/students/statistics - Obtenir les statistiques des étudiants
router.get('/statistics', authenticate, authorize('admin', 'staff', 'professor'), async (req, res) => {
  try {
    const { academicYear, cohort } = req.query;
    const matchQuery = {};

    if (academicYear) matchQuery.academicYear = academicYear;
    if (cohort) matchQuery.cohort = cohort;

    const stats = await Student.aggregate([
      { $match: matchQuery },
      {
        $group: {
          _id: '$enrollmentStatus',
          count: { $sum: 1 },
          averageGPA: { $avg: '$academicRecord.currentGPA' }
        }
      }
    ]);

    const totalStudents = await Student.countDocuments(matchQuery);

    res.json({
      totalStudents,
      statusBreakdown: stats
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Fonctions utilitaires
const generateTemporaryPassword = () => {
  return Math.random().toString(36).slice(-8) + Math.random().toString(36).slice(-8);
};

const generateStudentNumber = async () => {
  const currentYear = new Date().getFullYear();
  const prefix = `EMBA${currentYear}`;

  // Trouver le dernier numéro d'étudiant de cette année
  const lastStudent = await Student.findOne({
    studentNumber: { $regex: `^${prefix}` }
  }).sort({ studentNumber: -1 });

  let nextNumber = 1;
  if (lastStudent) {
    const lastNumber = parseInt(lastStudent.studentNumber.replace(prefix, ''));
    nextNumber = lastNumber + 1;
  }

  return `${prefix}${nextNumber.toString().padStart(3, '0')}`;
};

// Route pour obtenir la progression de candidature d'un étudiant
router.get('/:id/application-progress', authenticate, authorize('admin', 'staff'), async (req, res) => {
  try {
    const Application = require('../models/Application');
    const student = await Student.findById(req.params.id).populate('user');

    if (!student) {
      return res.status(404).json({ message: 'Étudiant non trouvé' });
    }

    // Chercher la candidature associée à cet étudiant
    const application = await Application.findOne({
      'personalInfo.email': student.user.email
    });

    if (!application) {
      return res.json({
        sections: [
          { name: 'Informations personnelles', percentage: 0, status: 'missing', missingFields: ['Toutes les informations'] },
          { name: 'Formation académique', percentage: 0, status: 'missing', missingFields: ['Toutes les informations'] },
          { name: 'Expérience professionnelle', percentage: 0, status: 'missing', missingFields: ['Toutes les informations'] },
          { name: 'Motivation', percentage: 0, status: 'missing', missingFields: ['Toutes les informations'] },
          { name: 'Documents', percentage: 0, status: 'missing', missingFields: ['Tous les documents'] }
        ]
      });
    }

    // Calculer la progression pour chaque section
    const sections = [
      {
        name: 'Informations personnelles',
        percentage: calculatePersonalInfoProgress(application.personalInfo),
        status: getProgressStatus(calculatePersonalInfoProgress(application.personalInfo)),
        missingFields: getMissingPersonalFields(application.personalInfo)
      },
      {
        name: 'Formation académique',
        percentage: calculateAcademicProgress(application.academicBackground),
        status: getProgressStatus(calculateAcademicProgress(application.academicBackground)),
        missingFields: getMissingAcademicFields(application.academicBackground)
      },
      {
        name: 'Expérience professionnelle',
        percentage: calculateWorkProgress(application.workExperience),
        status: getProgressStatus(calculateWorkProgress(application.workExperience)),
        missingFields: getMissingWorkFields(application.workExperience)
      },
      {
        name: 'Motivation',
        percentage: calculateMotivationProgress(application.motivation),
        status: getProgressStatus(calculateMotivationProgress(application.motivation)),
        missingFields: getMissingMotivationFields(application.motivation)
      },
      {
        name: 'Documents',
        percentage: calculateDocumentsProgress(application.documents),
        status: getProgressStatus(calculateDocumentsProgress(application.documents)),
        missingFields: getMissingDocumentFields(application.documents)
      }
    ];

    res.json({ sections });
  } catch (error) {
    console.error('Erreur lors du calcul de la progression:', error);
    res.status(500).json({ message: error.message });
  }
});

// Route pour obtenir l'historique des actions d'un étudiant
router.get('/:id/action-history', authenticate, authorize('admin', 'staff'), async (req, res) => {
  try {
    const Application = require('../models/Application');
    const student = await Student.findById(req.params.id).populate('user');

    if (!student) {
      return res.status(404).json({ message: 'Étudiant non trouvé' });
    }

    // Chercher la candidature associée
    const application = await Application.findOne({
      'personalInfo.email': student.user.email
    });

    const actions = [];

    // Ajouter les actions de l'étudiant
    actions.push({
      action: 'Compte créé',
      description: 'Compte étudiant créé dans le système',
      date: student.createdAt,
      performedBy: 'Système'
    });

    if (student.enrollmentDate) {
      actions.push({
        action: 'Inscription confirmée',
        description: 'Inscription au programme confirmée',
        date: student.enrollmentDate,
        performedBy: 'Administration'
      });
    }

    // Ajouter les actions de la candidature si elle existe
    if (application && application.applicationStatus.statusHistory) {
      application.applicationStatus.statusHistory.forEach(status => {
        actions.push({
          action: `Candidature ${getStatusLabel(status.status)}`,
          description: status.reason || status.comments || `Statut changé vers ${getStatusLabel(status.status)}`,
          date: status.changeDate,
          performedBy: status.changedBy || 'Administration'
        });
      });
    }

    // Trier par date décroissante
    actions.sort((a, b) => new Date(b.date) - new Date(a.date));

    res.json({ actions });
  } catch (error) {
    console.error('Erreur lors de la récupération de l\'historique:', error);
    res.status(500).json({ message: error.message });
  }
});

// Fonctions utilitaires pour calculer la progression
function calculatePersonalInfoProgress(personalInfo) {
  if (!personalInfo) return 0;
  const fields = ['firstName', 'lastName', 'email', 'phone', 'dateOfBirth', 'gender', 'nationality'];
  const completedFields = fields.filter(field => personalInfo[field]);
  return Math.round((completedFields.length / fields.length) * 100);
}

function calculateAcademicProgress(academicBackground) {
  if (!academicBackground || academicBackground.length === 0) return 0;
  const requiredFields = ['degree', 'institution', 'endYear'];
  const firstEducation = academicBackground[0];
  const completedFields = requiredFields.filter(field => firstEducation[field]);
  return Math.round((completedFields.length / requiredFields.length) * 100);
}

function calculateWorkProgress(workExperience) {
  if (!workExperience || workExperience.length === 0) return 0;
  const requiredFields = ['company', 'position', 'industry'];
  const firstWork = workExperience[0];
  const completedFields = requiredFields.filter(field => firstWork[field]);
  return Math.round((completedFields.length / requiredFields.length) * 100);
}

function calculateMotivationProgress(motivation) {
  if (!motivation) return 0;
  const fields = ['whyMBA', 'careerGoals'];
  const completedFields = fields.filter(field => motivation[field]);
  return Math.round((completedFields.length / fields.length) * 100);
}

function calculateDocumentsProgress(documents) {
  if (!documents || documents.length === 0) return 0;
  const requiredDocs = ['cv_resume', 'personal_statement'];
  const submittedDocs = documents.map(doc => doc.type);
  const completedDocs = requiredDocs.filter(docType => submittedDocs.includes(docType));
  return Math.round((completedDocs.length / requiredDocs.length) * 100);
}

function getProgressStatus(percentage) {
  if (percentage >= 80) return 'completed';
  if (percentage >= 40) return 'in_progress';
  return 'missing';
}

function getMissingPersonalFields(personalInfo) {
  if (!personalInfo) return ['Toutes les informations'];
  const fields = ['firstName', 'lastName', 'email', 'phone', 'dateOfBirth', 'gender', 'nationality'];
  return fields.filter(field => !personalInfo[field]);
}

function getMissingAcademicFields(academicBackground) {
  if (!academicBackground || academicBackground.length === 0) return ['Diplôme', 'Institution', 'Année'];
  const requiredFields = ['degree', 'institution', 'endYear'];
  const firstEducation = academicBackground[0];
  return requiredFields.filter(field => !firstEducation[field]);
}

function getMissingWorkFields(workExperience) {
  if (!workExperience || workExperience.length === 0) return ['Entreprise', 'Poste', 'Secteur'];
  const requiredFields = ['company', 'position', 'industry'];
  const firstWork = workExperience[0];
  return requiredFields.filter(field => !firstWork[field]);
}

function getMissingMotivationFields(motivation) {
  if (!motivation) return ['Motivation', 'Objectifs'];
  const fields = ['whyMBA', 'careerGoals'];
  return fields.filter(field => !motivation[field]);
}

function getMissingDocumentFields(documents) {
  if (!documents || documents.length === 0) return ['CV', 'Lettre de motivation'];
  const requiredDocs = ['cv_resume', 'personal_statement'];
  const submittedDocs = documents.map(doc => doc.type);
  return requiredDocs.filter(docType => !submittedDocs.includes(docType));
}

function getStatusLabel(status) {
  const statusLabels = {
    'submitted': 'soumise',
    'under_review': 'en révision',
    'interview_scheduled': 'entretien programmé',
    'accepted': 'acceptée',
    'rejected': 'refusée',
    'waitlisted': 'en liste d\'attente'
  };
  return statusLabels[status] || status;
}

// GET /api/students/me/classmates - Obtenir les camarades de classe de l'étudiant connecté
router.get('/me/classmates', authenticate, authorize(['student']), async (req, res) => {
  try {
    console.log('👥 Récupération des camarades de classe pour:', req.user.email);

    // Trouver l'étudiant connecté
    const currentStudent = await Student.findOne({ user: req.user._id });

    if (!currentStudent) {
      return res.status(404).json({
        success: false,
        message: 'Profil étudiant non trouvé'
      });
    }

    console.log('👥 Classe de l\'étudiant:', currentStudent.class);

    // Récupérer tous les étudiants de la même classe (sauf l'étudiant connecté)
    const classmates = await Student.find({
      class: currentStudent.class,
      _id: { $ne: currentStudent._id }
    })
    .populate('user', 'firstName lastName email phone profilePicture')
    .sort({ studentNumber: 1 })
    .lean();

    console.log('👥 Camarades trouvés:', classmates.length);

    res.json({
      success: true,
      students: classmates,
      className: currentStudent.class,
      count: classmates.length
    });
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des camarades:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération des camarades'
    });
  }
});

// GET /api/students/me - Obtenir les informations de l'étudiant connecté
router.get('/me', authenticate, authorize(['student']), async (req, res) => {
  console.log('🔍 DEBUG - Utilisateur connecté:', {
    id: req.user._id,
    email: req.user.email,
    role: req.user.role
  });
  try {
    const student = await Student.findOne({ user: req.user._id })
      .populate('user', 'firstName lastName email phone profilePicture dateOfBirth');

    if (!student) {
      return res.status(404).json({
        success: false,
        message: 'Profil étudiant non trouvé'
      });
    }

    res.json({
      success: true,
      student
    });
  } catch (error) {
    console.error('Erreur lors de la récupération du profil étudiant:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération du profil'
    });
  }
});

// GET /api/students/me/dashboard - Dashboard data for current student
router.get('/me/dashboard', authenticate, authorize(['student']), async (req, res) => {
  try {
    console.log('📊 STUDENT DASHBOARD - Utilisateur connecté:', {
      id: req.user._id,
      email: req.user.email,
      role: req.user.role
    });
    const student = await Student.findOne({ user: req.user._id })
      .populate('user', 'firstName lastName email profilePicture')
      .populate('finalProject.supervisor', 'user academicTitle');

    if (!student) {
      return res.status(404).json({
        success: false,
        message: 'Profil étudiant non trouvé'
      });
    }

    // Get real enrollments for this student
    const enrollments = await Enrollment.find({
      student: student._id,
      status: { $in: ['enrolled', 'active'] }
    })
    .populate('course', 'title code credits instructor schedule')
    .populate('course.instructor', 'firstName lastName');

    // Calculate real academic data
    let totalCreditsCompleted = 0;
    let totalGradePoints = 0;
    let totalCreditsAttempted = 0;
    let totalAttendanceSessions = 0;
    let totalAttendedSessions = 0;

    for (const enrollment of enrollments) {
      if (enrollment.status === 'completed' && enrollment.academicPerformance.finalGrade) {
        totalCreditsCompleted += enrollment.course.credits || 3;
        totalGradePoints += (enrollment.academicPerformance.gpaPoints || 0) * (enrollment.course.credits || 3);
        totalCreditsAttempted += enrollment.course.credits || 3;
      }

      totalAttendanceSessions += enrollment.attendance.totalSessions || 0;
      totalAttendedSessions += enrollment.attendance.attendedSessions || 0;
    }

    const gpa = totalCreditsAttempted > 0 ? totalGradePoints / totalCreditsAttempted : 0;
    const attendanceRate = totalAttendanceSessions > 0 ? (totalAttendedSessions / totalAttendanceSessions) * 100 : 0;

    // Calculate level based on credits completed
    const level = Math.floor(totalCreditsCompleted / 10) + 1;
    const xpCurrent = (totalCreditsCompleted % 10) * 100;
    const xpRequired = 1000;

    // Calculate badges based on real data
    const badges = [];
    if (gpa >= 3.5) badges.push({ id: 'high_achiever', name: 'High Achiever', icon: '🏆', description: 'GPA supérieur à 3.5' });
    if (attendanceRate >= 95) badges.push({ id: 'perfect_attendance', name: 'Présence Parfaite', icon: '⭐', description: 'Taux de présence supérieur à 95%' });
    if (totalCreditsCompleted >= 30) badges.push({ id: 'halfway_hero', name: 'Mi-Parcours', icon: '🎯', description: '50% des crédits complétés' });
    if (student.finalProject.status === 'completed') badges.push({ id: 'project_master', name: 'Maître de Projet', icon: '📚', description: 'Projet final terminé' });
    if (enrollments.length >= 5) badges.push({ id: 'course_collector', name: 'Collectionneur de Cours', icon: '📚', description: '5 cours ou plus' });

    // Calculate achievements based on real data
    const progressPercentage = totalCreditsCompleted > 0 ? (totalCreditsCompleted / (student.academicRecord.creditsRequired || 60)) * 100 : 0;

    const achievements = [
      {
        id: 'academic_progress',
        title: 'Progression Académique',
        description: `${totalCreditsCompleted}/${student.academicRecord.creditsRequired || 60} crédits complétés`,
        progress: progressPercentage,
        maxProgress: 100,
        icon: '📖',
        category: 'academic'
      },
      {
        id: 'attendance',
        title: 'Assiduité',
        description: `${totalAttendedSessions}/${totalAttendanceSessions} sessions assistées`,
        progress: attendanceRate,
        maxProgress: 100,
        icon: '📅',
        category: 'attendance'
      },
      {
        id: 'gpa',
        title: 'Performance Académique',
        description: `GPA actuel: ${gpa.toFixed(2)}/4.0`,
        progress: (gpa / 4) * 100,
        maxProgress: 100,
        icon: '🎓',
        category: 'performance'
      },
      {
        id: 'courses',
        title: 'Cours Actifs',
        description: `${enrollments.length} cours en cours`,
        progress: (enrollments.length / 8) * 100, // Assuming max 8 courses per semester
        maxProgress: 100,
        icon: '📚',
        category: 'academic'
      }
    ];

    // Get recent submissions and activities
    const recentSubmissions = await Submission.find({ student: student._id })
      .populate('assignment', 'title type')
      .populate('assignment.course', 'title code')
      .sort({ submittedAt: -1 })
      .limit(5);

    const recentActivities = [];

    // Add recent submissions as activities
    for (const submission of recentSubmissions) {
      if (submission.assignment && submission.assignment.course) {
        recentActivities.push({
          id: `submission_${submission._id}`,
          type: submission.isGraded ? 'grade' : 'assignment',
          title: submission.isGraded ? 'Note reçue' : 'Devoir soumis',
          description: `${submission.assignment.title} - ${submission.assignment.course.title}`,
          points: submission.isGraded ? Math.round(submission.grade || 0) : 10,
          date: submission.gradedAt || submission.submittedAt,
          icon: submission.isGraded ? '📝' : '📤'
        });
      }
    }

    // Add enrollment activities
    for (const enrollment of enrollments.slice(0, 3)) {
      if (enrollment.course) {
        recentActivities.push({
          id: `enrollment_${enrollment._id}`,
          type: 'enrollment',
          title: 'Cours rejoint',
          description: `${enrollment.course.title} (${enrollment.course.code})`,
          points: 25,
          date: enrollment.enrollmentDate,
          icon: '🎓'
        });
      }
    }

    // Sort activities by date and limit to 10
    recentActivities.sort((a, b) => new Date(b.date) - new Date(a.date));
    recentActivities.splice(10);

    const dashboardData = {
      profile: {
        studentNumber: student.studentNumber,
        program: student.program,
        cohort: student.cohort,
        specialization: student.specialization,
        enrollmentDate: student.enrollmentDate,
        expectedGraduation: student.expectedGraduationDate,
        status: student.enrollmentStatus
      },
      gamification: {
        level,
        xpCurrent,
        xpRequired,
        totalPoints: totalCreditsCompleted * 100 + Math.floor(attendanceRate * 10),
        badges,
        achievements,
        recentActivities
      },
      academic: {
        currentGPA: gpa,
        creditsCompleted: totalCreditsCompleted,
        creditsRequired: student.academicRecord.creditsRequired || 60,
        academicStanding: gpa >= 3.0 ? 'good' : gpa >= 2.0 ? 'satisfactory' : 'probation',
        progressPercentage
      },
      attendance: {
        totalSessions: totalAttendanceSessions,
        attendedSessions: totalAttendedSessions,
        attendanceRate
      },
      finalProject: {
        title: student.finalProject.title,
        status: student.finalProject.status,
        supervisor: student.finalProject.supervisor
      }
    };

    res.json({
      success: true,
      data: dashboardData
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération du dashboard étudiant:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des données du dashboard',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// GET /api/students/me/courses - Get current student's courses
router.get('/me/courses', authenticate, authorize(['student']), async (req, res) => {
  try {
    const student = await Student.findOne({ user: req.user._id });

    if (!student) {
      return res.status(404).json({
        success: false,
        message: 'Profil étudiant non trouvé'
      });
    }

    // Get real enrollments with populated course data
    const enrollments = await Enrollment.find({
      student: student._id,
      status: { $in: ['enrolled', 'active'] }
    })
    .populate({
      path: 'course',
      populate: {
        path: 'instructor',
        select: 'firstName lastName academicTitle'
      }
    });

    const courses = [];

    for (const enrollment of enrollments) {
      if (!enrollment.course) continue;

      // Get assignments for this course
      const assignments = await Assignment.find({
        course: enrollment.course._id,
        status: 'published',
        dueDate: { $gte: new Date() }
      })
      .sort({ dueDate: 1 })
      .limit(5);

      // Get student's submissions for these assignments
      const assignmentData = [];
      for (const assignment of assignments) {
        const submission = await Submission.findOne({
          assignment: assignment._id,
          student: student._id
        });

        assignmentData.push({
          id: assignment._id,
          title: assignment.title,
          dueDate: assignment.dueDate,
          status: submission ? (submission.isGraded ? 'completed' : 'submitted') : 'pending',
          points: assignment.maxPoints,
          grade: submission?.grade,
          isLate: submission?.isLate || false
        });
      }

      // Calculate progress based on completed assignments
      const totalAssignments = await Assignment.countDocuments({
        course: enrollment.course._id,
        status: 'published'
      });

      const completedAssignments = await Submission.countDocuments({
        student: student._id,
        assignment: { $in: await Assignment.find({ course: enrollment.course._id }).distinct('_id') }
      });

      const progress = totalAssignments > 0 ? Math.round((completedAssignments / totalAssignments) * 100) : 0;

      // Format instructor name
      const instructorName = enrollment.course.instructor
        ? `${enrollment.course.instructor.academicTitle || 'Dr.'} ${enrollment.course.instructor.firstName} ${enrollment.course.instructor.lastName}`
        : 'Non assigné';

      // Calculate next session (mock for now - should come from schedule)
      const nextSession = new Date();
      nextSession.setDate(nextSession.getDate() + 7); // Next week

      courses.push({
        id: enrollment.course._id,
        code: enrollment.course.code,
        title: enrollment.course.title,
        instructor: instructorName,
        schedule: enrollment.course.schedule || 'Horaire à définir',
        progress,
        grade: enrollment.academicPerformance.letterGrade || null,
        credits: enrollment.course.credits || 3,
        status: enrollment.status,
        nextSession,
        assignments: assignmentData,
        enrollmentId: enrollment._id,
        attendanceRate: enrollment.attendance.attendanceRate || 0
      });
    }

    res.json({
      success: true,
      data: courses
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des cours:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des cours',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// GET /api/students/me/documents - Obtenir les documents de l'étudiant connecté
router.get('/me/documents', authenticate, authorize(['student']), async (req, res) => {
  try {
    console.log('📄 Récupération des documents pour l\'étudiant:', req.user.email);

    // Trouver l'étudiant connecté
    const student = await Student.findOne({ user: req.user._id })
      .populate('user', 'firstName lastName email');

    if (!student) {
      return res.status(404).json({
        success: false,
        message: 'Profil étudiant non trouvé'
      });
    }

    // Retourner les documents avec des informations enrichies
    const documentsWithInfo = student.documents.map(doc => ({
      id: doc._id,
      name: doc.originalName,
      type: doc.type,
      category: getDocumentCategory(doc.type),
      size: formatFileSize(doc.size),
      uploadDate: doc.uploadDate.toISOString().split('T')[0],
      status: 'verified', // Par défaut, les documents transférés sont vérifiés
      description: getDocumentDescription(doc.type),
      url: `/uploads/applications/${doc.filename}`,
      filename: doc.filename,
      mimeType: doc.mimeType
    }));

    console.log(`✅ ${documentsWithInfo.length} documents récupérés pour l'étudiant ${student.studentNumber}`);

    res.json({
      success: true,
      documents: documentsWithInfo,
      studentInfo: {
        studentNumber: student.studentNumber,
        name: `${student.user.firstName} ${student.user.lastName}`,
        email: student.user.email
      }
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des documents étudiant:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des documents',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Fonctions utilitaires pour les documents
function getDocumentCategory(type) {
  const categoryMapping = {
    'cv': 'Application',
    'id_copy': 'Identity',
    'photo': 'Identity',
    'diploma': 'Academic',
    'transcript': 'Academic',
    'recommendation_letter': 'Application',
    'other': 'Other'
  };
  return categoryMapping[type] || 'Other';
}

function getDocumentDescription(type) {
  const descriptionMapping = {
    'cv': 'Curriculum Vitae',
    'id_copy': 'Copie de la carte d\'identité',
    'photo': 'Photo d\'identité',
    'diploma': 'Diplôme universitaire',
    'transcript': 'Relevé de notes',
    'recommendation_letter': 'Lettre de recommandation',
    'other': 'Autre document'
  };
  return descriptionMapping[type] || 'Document';
}

function formatFileSize(bytes) {
  if (!bytes) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

// GET /api/students/class/:className - Obtenir les étudiants d'une classe
router.get('/class/:className', authenticate, authorize(['admin', 'staff', 'professor', 'student']), async (req, res) => {
  try {
    const students = await Student.getClassStudents(req.params.className);

    res.json({
      success: true,
      students
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des étudiants:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des étudiants'
    });
  }
});

// GET /api/students/class-stats-test - Route de test simple
router.get('/class-stats-test', (req, res) => {
  console.log('📊 TEST - Route de test appelée');
  res.json({ success: true, message: 'Route de test fonctionne' });
});

// GET /api/students/class-stats - Obtenir les statistiques de toutes les classes
router.get('/class-stats', authenticate, authorize(['admin', 'staff', 'professor']), (req, res) => {
  console.log('📊 DÉBUT - Route class-stats appelée');
  res.json({
    success: true,
    message: 'Route class-stats fonctionne',
    stats: {},
    promotionStats: {
      EMBA1: { classes: 0, totalStudents: 0 },
      EMBA2: { classes: 0, totalStudents: 0 }
    },
    totalClasses: 0,
    totalStudents: 0
  });
});

// GET /api/students/:id - Obtenir un étudiant par ID (ROUTE GÉNÉRIQUE - DOIT ÊTRE À LA FIN)
router.get('/:id', authenticate, authorize(['admin', 'staff', 'professor']), async (req, res) => {
  try {
    const student = await Student.findById(req.params.id)
      .populate('user', '-password -resetPasswordToken -verificationToken')
      .populate('finalProject.supervisor', 'user academicTitle department');

    if (!student) {
      return res.status(404).json({ message: 'Student not found' });
    }

    res.json(student);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

module.exports = router;
