const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Couleurs pour les logs
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

const log = (message, color = 'reset') => {
  console.log(`${colors[color]}${message}${colors.reset}`);
};

// Vérifications du système
const checkSystem = async () => {
  log('🔍 Vérification du système EMBA...', 'blue');
  console.log('');

  let allChecksPass = true;

  // 1. Vérifier les variables d'environnement
  log('1. Variables d\'environnement:', 'yellow');
  const requiredEnvVars = ['MONGO_URI', 'JWT_SECRET', 'PORT'];
  
  requiredEnvVars.forEach(envVar => {
    if (process.env[envVar]) {
      log(`   ✅ ${envVar}: Configuré`, 'green');
    } else {
      log(`   ❌ ${envVar}: Manquant`, 'red');
      allChecksPass = false;
    }
  });

  // 2. Vérifier la connexion MongoDB
  log('\n2. Connexion MongoDB:', 'yellow');
  try {
    await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    log('   ✅ Connexion MongoDB: Réussie', 'green');
    await mongoose.connection.close();
  } catch (error) {
    log(`   ❌ Connexion MongoDB: Échec (${error.message})`, 'red');
    allChecksPass = false;
  }

  // 3. Vérifier les modèles
  log('\n3. Modèles Mongoose:', 'yellow');
  const modelsDir = path.join(__dirname, '../models');
  const expectedModels = [
    'User.js', 'Student.js', 'Professor.js', 'Course.js', 'Module.js',
    'Enrollment.js', 'Schedule.js', 'Grade.js', 'Assignment.js',
    'Attendance.js', 'Payment.js', 'Notification.js', 'Document.js', 'Application.js'
  ];

  expectedModels.forEach(model => {
    const modelPath = path.join(modelsDir, model);
    if (fs.existsSync(modelPath)) {
      log(`   ✅ ${model}: Trouvé`, 'green');
    } else {
      log(`   ❌ ${model}: Manquant`, 'red');
      allChecksPass = false;
    }
  });

  // 4. Vérifier les routes
  log('\n4. Routes API:', 'yellow');
  const routesDir = path.join(__dirname, '../routes');
  const expectedRoutes = [
    'userRoutes.js', 'studentRoutes.js', 'professorRoutes.js', 'courseRoutes.js',
    'moduleRoutes.js', 'enrollmentRoutes.js', 'scheduleRoutes.js', 'gradeRoutes.js',
    'assignmentRoutes.js', 'attendanceRoutes.js', 'paymentRoutes.js',
    'notificationRoutes.js', 'documentRoutes.js', 'applicationRoutes.js'
  ];

  expectedRoutes.forEach(route => {
    const routePath = path.join(routesDir, route);
    if (fs.existsSync(routePath)) {
      log(`   ✅ ${route}: Trouvé`, 'green');
    } else {
      log(`   ❌ ${route}: Manquant`, 'red');
      allChecksPass = false;
    }
  });

  // 5. Vérifier les middlewares
  log('\n5. Middlewares:', 'yellow');
  const middlewareDir = path.join(__dirname, '../middleware');
  const expectedMiddlewares = ['auth.js', 'validation.js'];

  expectedMiddlewares.forEach(middleware => {
    const middlewarePath = path.join(middlewareDir, middleware);
    if (fs.existsSync(middlewarePath)) {
      log(`   ✅ ${middleware}: Trouvé`, 'green');
    } else {
      log(`   ❌ ${middleware}: Manquant`, 'red');
      allChecksPass = false;
    }
  });

  // 6. Vérifier les dépendances package.json
  log('\n6. Dépendances:', 'yellow');
  const packageJsonPath = path.join(__dirname, '../package.json');
  
  if (fs.existsSync(packageJsonPath)) {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    const requiredDeps = [
      'express', 'mongoose', 'cors', 'dotenv', 'bcryptjs', 
      'jsonwebtoken', 'helmet', 'compression', 'morgan'
    ];

    requiredDeps.forEach(dep => {
      if (packageJson.dependencies && packageJson.dependencies[dep]) {
        log(`   ✅ ${dep}: Installé (${packageJson.dependencies[dep]})`, 'green');
      } else {
        log(`   ❌ ${dep}: Manquant`, 'red');
        allChecksPass = false;
      }
    });
  } else {
    log('   ❌ package.json: Manquant', 'red');
    allChecksPass = false;
  }

  // 7. Vérifier les dossiers nécessaires
  log('\n7. Structure des dossiers:', 'yellow');
  const requiredDirs = ['models', 'routes', 'middleware', 'scripts'];
  
  requiredDirs.forEach(dir => {
    const dirPath = path.join(__dirname, '..', dir);
    if (fs.existsSync(dirPath)) {
      log(`   ✅ ${dir}/: Existe`, 'green');
    } else {
      log(`   ❌ ${dir}/: Manquant`, 'red');
      allChecksPass = false;
    }
  });

  // Résumé final
  console.log('\n' + '='.repeat(50));
  if (allChecksPass) {
    log('🎉 Toutes les vérifications sont passées !', 'green');
    log('✅ Le système EMBA est prêt à être démarré.', 'green');
    console.log('');
    log('Pour démarrer le serveur:', 'blue');
    log('   npm run dev', 'yellow');
    console.log('');
    log('Pour initialiser avec des données de test:', 'blue');
    log('   npm run seed', 'yellow');
  } else {
    log('❌ Certaines vérifications ont échoué.', 'red');
    log('⚠️  Veuillez corriger les erreurs avant de démarrer le système.', 'yellow');
  }
  console.log('='.repeat(50));

  process.exit(allChecksPass ? 0 : 1);
};

// Gestion des erreurs
process.on('unhandledRejection', (err) => {
  log(`❌ Erreur non gérée: ${err.message}`, 'red');
  process.exit(1);
});

process.on('uncaughtException', (err) => {
  log(`❌ Exception non capturée: ${err.message}`, 'red');
  process.exit(1);
});

// Exécuter les vérifications
checkSystem();
