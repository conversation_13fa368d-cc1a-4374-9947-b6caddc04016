const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const User = require('../models/User');

// Configuration de la base de données
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/emba_management';

async function createAdmin() {
  try {
    // Connexion à MongoDB
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connecté à MongoDB');

    // Vérifier si un admin existe déjà
    const existingAdmin = await User.findOne({ role: 'admin' });
    
    if (existingAdmin) {
      console.log('👤 Admin existant trouvé:');
      console.log(`   Email: ${existingAdmin.email}`);
      console.log(`   Nom: ${existingAdmin.firstName} ${existingAdmin.lastName}`);
      console.log(`   Rôle: ${existingAdmin.role}`);
      console.log(`   Actif: ${existingAdmin.isActive}`);
      
      // Mettre à jour le rôle si nécessaire
      if (existingAdmin.role !== 'admin') {
        existingAdmin.role = 'admin';
        await existingAdmin.save();
        console.log('✅ Rôle mis à jour vers admin');
      }
      
      return;
    }

    // Créer un nouvel admin
    const adminData = {
      firstName: 'Admin',
      lastName: 'EMBA',
      email: '<EMAIL>',
      password: await bcrypt.hash('admin123', 12),
      role: 'admin',
      isActive: true,
      isEmailVerified: true,
      permissions: ['all']
    };

    const admin = new User(adminData);
    await admin.save();

    console.log('🎉 Nouvel admin créé avec succès !');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Mot de passe: admin123');
    console.log('⚠️  Changez ce mot de passe après la première connexion !');

  } catch (error) {
    console.error('❌ Erreur:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Déconnecté de MongoDB');
  }
}

// Fonction pour lister tous les utilisateurs et leurs rôles
async function listUsers() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connecté à MongoDB');

    const users = await User.find({}, 'firstName lastName email role isActive').sort({ createdAt: -1 });
    
    console.log('\n👥 Liste des utilisateurs:');
    console.log('─'.repeat(80));
    
    if (users.length === 0) {
      console.log('Aucun utilisateur trouvé');
    } else {
      users.forEach((user, index) => {
        const status = user.isActive ? '✅' : '❌';
        console.log(`${index + 1}. ${status} ${user.firstName} ${user.lastName}`);
        console.log(`   📧 ${user.email}`);
        console.log(`   👤 Rôle: ${user.role}`);
        console.log('');
      });
    }

  } catch (error) {
    console.error('❌ Erreur:', error.message);
  } finally {
    await mongoose.disconnect();
  }
}

// Fonction pour mettre à jour le rôle d'un utilisateur
async function updateUserRole(email, newRole) {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connecté à MongoDB');

    const user = await User.findOne({ email });
    
    if (!user) {
      console.log(`❌ Utilisateur avec l'email ${email} non trouvé`);
      return;
    }

    const oldRole = user.role;
    user.role = newRole;
    await user.save();

    console.log(`✅ Rôle de ${user.firstName} ${user.lastName} mis à jour:`);
    console.log(`   Ancien rôle: ${oldRole}`);
    console.log(`   Nouveau rôle: ${newRole}`);

  } catch (error) {
    console.error('❌ Erreur:', error.message);
  } finally {
    await mongoose.disconnect();
  }
}

// Gestion des arguments de ligne de commande
const command = process.argv[2];
const arg1 = process.argv[3];
const arg2 = process.argv[4];

switch (command) {
  case 'create':
    createAdmin();
    break;
  case 'list':
    listUsers();
    break;
  case 'update-role':
    if (!arg1 || !arg2) {
      console.log('Usage: node create-admin.js update-role <email> <role>');
      console.log('Rôles disponibles: admin, student, professor');
    } else {
      updateUserRole(arg1, arg2);
    }
    break;
  default:
    console.log('🛠️  Script de gestion des utilisateurs EMBA');
    console.log('');
    console.log('Commandes disponibles:');
    console.log('  create      - Créer un utilisateur admin');
    console.log('  list        - Lister tous les utilisateurs');
    console.log('  update-role - Mettre à jour le rôle d\'un utilisateur');
    console.log('');
    console.log('Exemples:');
    console.log('  node create-admin.js create');
    console.log('  node create-admin.js list');
    console.log('  node create-admin.js update-role <EMAIL> admin');
}
