require('dotenv').config();
const { fixApplicationNumbers } = require('./fix-application-numbers');
const { fixStudentNumbers } = require('./fix-student-numbers');

// Script global pour corriger tous les problèmes de numérotation
const fixAllNumbers = async () => {
  console.log('🛠️ Script de correction globale des numéros\n');
  console.log('Ce script va corriger les numéros de candidatures et d\'étudiants\n');

  try {
    console.log('📋 Étape 1: Correction des numéros de candidatures...');
    await fixApplicationNumbers();
    
    console.log('\n👨‍🎓 Étape 2: Correction des numéros d\'étudiants...');
    await fixStudentNumbers();
    
    console.log('\n✅ Correction globale terminée !');
    console.log('\n📝 Prochaines étapes recommandées:');
    console.log('1. Testez la création d\'une nouvelle candidature');
    console.log('2. Testez la validation d\'une candidature (création d\'étudiant)');
    console.log('3. Vérifiez que les numéros sont générés correctement');
    
  } catch (error) {
    console.error('❌ Erreur lors de la correction globale:', error);
  }
};

if (require.main === module) {
  fixAllNumbers();
}

module.exports = { fixAllNumbers };
