require('dotenv').config();
const mongoose = require('mongoose');
const Application = require('../models/Application');

// Script pour corriger les numéros de candidature après suppression manuelle
const fixApplicationNumbers = async () => {
  try {
    console.log('🔧 Connexion à MongoDB...');
    await mongoose.connect(process.env.MONGO_URI);
    console.log('✅ Connecté à MongoDB');

    console.log('\n📋 Analyse des candidatures existantes...');
    
    // Récupérer toutes les candidatures triées par date de création
    const applications = await Application.find({})
      .sort({ createdAt: 1 })
      .select('applicationNumber programInfo createdAt');

    console.log(`📊 ${applications.length} candidatures trouvées`);

    // Grouper par année et semestre
    const groups = {};
    applications.forEach(app => {
      const year = app.programInfo.intakeYear;
      const semester = app.programInfo.intakeSemester.charAt(0);
      const key = `${year}${semester}`;
      
      if (!groups[key]) {
        groups[key] = [];
      }
      groups[key].push(app);
    });

    console.log('\n🔍 Groupes trouvés:');
    Object.keys(groups).forEach(key => {
      console.log(`  ${key}: ${groups[key].length} candidatures`);
    });

    // Vérifier les doublons et les trous
    console.log('\n🔍 Vérification des numéros...');
    let hasIssues = false;

    for (const [key, apps] of Object.entries(groups)) {
      console.log(`\n📝 Groupe ${key}:`);
      
      const numbers = apps.map(app => {
        const match = app.applicationNumber.match(/\d{4}$/);
        return match ? parseInt(match[0]) : 0;
      }).sort((a, b) => a - b);

      // Vérifier les doublons
      const duplicates = numbers.filter((num, index) => numbers.indexOf(num) !== index);
      if (duplicates.length > 0) {
        console.log(`  ❌ Doublons trouvés: ${duplicates.join(', ')}`);
        hasIssues = true;
      }

      // Vérifier les trous
      const expectedSequence = Array.from({length: numbers.length}, (_, i) => i + 1);
      const missing = expectedSequence.filter(num => !numbers.includes(num));
      if (missing.length > 0) {
        console.log(`  ⚠️ Numéros manquants: ${missing.join(', ')}`);
        hasIssues = true;
      }

      if (!hasIssues) {
        console.log(`  ✅ Séquence correcte: ${numbers.join(', ')}`);
      }
    }

    if (hasIssues) {
      console.log('\n🛠️ Correction des numéros de candidature...');
      
      for (const [key, apps] of Object.entries(groups)) {
        console.log(`\n📝 Correction du groupe ${key}...`);
        
        // Trier par date de création pour maintenir l'ordre chronologique
        apps.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));
        
        for (let i = 0; i < apps.length; i++) {
          const newNumber = `APP${key}${String(i + 1).padStart(4, '0')}`;
          const oldNumber = apps[i].applicationNumber;
          
          if (oldNumber !== newNumber) {
            console.log(`  📝 ${oldNumber} → ${newNumber}`);
            
            // Mettre à jour le numéro
            await Application.findByIdAndUpdate(apps[i]._id, {
              applicationNumber: newNumber
            });
          }
        }
      }
      
      console.log('\n✅ Correction terminée !');
    } else {
      console.log('\n✅ Aucune correction nécessaire');
    }

  } catch (error) {
    console.error('❌ Erreur:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Déconnecté de MongoDB');
  }
};

// Script pour supprimer les index problématiques et les recréer
const resetApplicationNumberIndex = async () => {
  try {
    console.log('🔧 Connexion à MongoDB...');
    await mongoose.connect(process.env.MONGO_URI);
    console.log('✅ Connecté à MongoDB');

    console.log('\n🗑️ Suppression de l\'index applicationNumber...');
    try {
      await Application.collection.dropIndex('applicationNumber_1');
      console.log('✅ Index supprimé');
    } catch (error) {
      console.log('⚠️ Index déjà supprimé ou inexistant');
    }

    console.log('\n🔧 Recréation de l\'index applicationNumber...');
    await Application.collection.createIndex({ applicationNumber: 1 }, { unique: true });
    console.log('✅ Index recréé');

  } catch (error) {
    console.error('❌ Erreur:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Déconnecté de MongoDB');
  }
};

// Menu principal
const main = async () => {
  const args = process.argv.slice(2);
  
  if (args.includes('--reset-index')) {
    await resetApplicationNumberIndex();
  } else if (args.includes('--fix-numbers')) {
    await fixApplicationNumbers();
  } else {
    console.log('🛠️ Script de correction des numéros de candidature\n');
    console.log('Usage:');
    console.log('  node fix-application-numbers.js --fix-numbers    # Corriger les numéros');
    console.log('  node fix-application-numbers.js --reset-index    # Réinitialiser l\'index');
    console.log('\nRecommandation: Exécutez d\'abord --fix-numbers puis --reset-index');
  }
};

if (require.main === module) {
  main();
}

module.exports = { fixApplicationNumbers, resetApplicationNumberIndex };
