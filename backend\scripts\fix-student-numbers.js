require('dotenv').config();
const mongoose = require('mongoose');
const Student = require('../models/Student');

// Script pour corriger les numéros d'étudiants après suppression manuelle
const fixStudentNumbers = async () => {
  try {
    console.log('🔧 Connexion à MongoDB...');
    await mongoose.connect(process.env.MONGO_URI);
    console.log('✅ Connecté à MongoDB');

    console.log('\n📋 Analyse des étudiants existants...');
    
    // Récupérer tous les étudiants triés par date de création
    const students = await Student.find({})
      .sort({ createdAt: 1 })
      .select('studentNumber createdAt')
      .populate('user', 'firstName lastName email');

    console.log(`📊 ${students.length} étudiants trouvés`);

    if (students.length === 0) {
      console.log('✅ Aucun étudiant à corriger');
      return;
    }

    // Grouper par année
    const groups = {};
    students.forEach(student => {
      const match = student.studentNumber.match(/^EMBA(\d{4})/);
      const year = match ? match[1] : new Date().getFullYear().toString();
      
      if (!groups[year]) {
        groups[year] = [];
      }
      groups[year].push(student);
    });

    console.log('\n🔍 Groupes trouvés:');
    Object.keys(groups).forEach(year => {
      console.log(`  ${year}: ${groups[year].length} étudiants`);
    });

    // Vérifier les doublons et les trous
    console.log('\n🔍 Vérification des numéros...');
    let hasIssues = false;

    for (const [year, studentList] of Object.entries(groups)) {
      console.log(`\n📝 Année ${year}:`);
      
      const numbers = studentList.map(student => {
        const match = student.studentNumber.match(/\d{3}$/);
        return match ? parseInt(match[0]) : 0;
      }).sort((a, b) => a - b);

      console.log(`  📊 Numéros actuels: ${numbers.join(', ')}`);

      // Vérifier les doublons
      const duplicates = numbers.filter((num, index) => numbers.indexOf(num) !== index);
      if (duplicates.length > 0) {
        console.log(`  ❌ Doublons trouvés: ${duplicates.join(', ')}`);
        hasIssues = true;
      }

      // Vérifier les trous
      const expectedSequence = Array.from({length: numbers.length}, (_, i) => i + 1);
      const missing = expectedSequence.filter(num => !numbers.includes(num));
      if (missing.length > 0) {
        console.log(`  ⚠️ Numéros manquants: ${missing.join(', ')}`);
        hasIssues = true;
      }

      if (!hasIssues) {
        console.log(`  ✅ Séquence correcte`);
      }
    }

    if (hasIssues) {
      console.log('\n🛠️ Correction des numéros d\'étudiants...');
      
      for (const [year, studentList] of Object.entries(groups)) {
        console.log(`\n📝 Correction de l'année ${year}...`);
        
        // Trier par date de création pour maintenir l'ordre chronologique
        studentList.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));
        
        for (let i = 0; i < studentList.length; i++) {
          const newNumber = `EMBA${year}${String(i + 1).padStart(3, '0')}`;
          const oldNumber = studentList[i].studentNumber;
          
          if (oldNumber !== newNumber) {
            const studentInfo = studentList[i].user ? 
              `${studentList[i].user.firstName} ${studentList[i].user.lastName}` : 
              'Utilisateur inconnu';
            
            console.log(`  📝 ${oldNumber} → ${newNumber} (${studentInfo})`);
            
            // Mettre à jour le numéro
            await Student.findByIdAndUpdate(studentList[i]._id, {
              studentNumber: newNumber
            });
          }
        }
      }
      
      console.log('\n✅ Correction terminée !');
    } else {
      console.log('\n✅ Aucune correction nécessaire');
    }

  } catch (error) {
    console.error('❌ Erreur:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Déconnecté de MongoDB');
  }
};

// Script pour supprimer les index problématiques et les recréer
const resetStudentNumberIndex = async () => {
  try {
    console.log('🔧 Connexion à MongoDB...');
    await mongoose.connect(process.env.MONGO_URI);
    console.log('✅ Connecté à MongoDB');

    console.log('\n🗑️ Suppression de l\'index studentNumber...');
    try {
      await Student.collection.dropIndex('studentNumber_1');
      console.log('✅ Index supprimé');
    } catch (error) {
      console.log('⚠️ Index déjà supprimé ou inexistant');
    }

    console.log('\n🔧 Recréation de l\'index studentNumber...');
    await Student.collection.createIndex({ studentNumber: 1 }, { unique: true });
    console.log('✅ Index recréé');

  } catch (error) {
    console.error('❌ Erreur:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Déconnecté de MongoDB');
  }
};

// Fonction pour afficher les étudiants existants
const listStudents = async () => {
  try {
    console.log('🔧 Connexion à MongoDB...');
    await mongoose.connect(process.env.MONGO_URI);
    console.log('✅ Connecté à MongoDB');

    console.log('\n📋 Liste des étudiants:');
    
    const students = await Student.find({})
      .sort({ studentNumber: 1 })
      .populate('user', 'firstName lastName email')
      .select('studentNumber createdAt enrollmentStatus');

    if (students.length === 0) {
      console.log('📭 Aucun étudiant trouvé');
    } else {
      console.log(`📊 ${students.length} étudiants trouvés:\n`);
      
      students.forEach(student => {
        const userInfo = student.user ? 
          `${student.user.firstName} ${student.user.lastName} (${student.user.email})` : 
          'Utilisateur supprimé';
        
        console.log(`  📝 ${student.studentNumber} - ${userInfo} - ${student.enrollmentStatus}`);
      });
    }

  } catch (error) {
    console.error('❌ Erreur:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Déconnecté de MongoDB');
  }
};

// Menu principal
const main = async () => {
  const args = process.argv.slice(2);
  
  if (args.includes('--list')) {
    await listStudents();
  } else if (args.includes('--reset-index')) {
    await resetStudentNumberIndex();
  } else if (args.includes('--fix-numbers')) {
    await fixStudentNumbers();
  } else {
    console.log('🛠️ Script de correction des numéros d\'étudiants\n');
    console.log('Usage:');
    console.log('  node fix-student-numbers.js --list           # Lister les étudiants');
    console.log('  node fix-student-numbers.js --fix-numbers    # Corriger les numéros');
    console.log('  node fix-student-numbers.js --reset-index    # Réinitialiser l\'index');
    console.log('\nRecommandation: Exécutez d\'abord --list puis --fix-numbers puis --reset-index');
  }
};

if (require.main === module) {
  main();
}

module.exports = { fixStudentNumbers, resetStudentNumberIndex, listStudents };
