const mongoose = require('mongoose');
require('dotenv').config();

// Import des modèles
const User = require('../models/User');
const Student = require('../models/Student');
const Professor = require('../models/Professor');
const Course = require('../models/Course');
const Module = require('../models/Module');

// Connexion à la base de données
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URI);
    console.log('✅ MongoDB Connected');
  } catch (error) {
    console.error('❌ MongoDB Connection Error:', error.message);
    process.exit(1);
  }
};

// Données de seed
const seedData = {
  users: [
    {
      firstName: 'Admin',
      lastName: 'System',
      email: '<EMAIL>',
      password: 'admin123',
      role: 'admin',
      phone: '+21612345678',
      isActive: true,
      isVerified: true,
      permissions: ['read', 'write', 'delete', 'manage_users', 'manage_courses', 'manage_grades', 'manage_payments']
    },
    {
      firstName: 'Ahmed',
      lastName: '<PERSON>',
      email: '<EMAIL>',
      password: 'student123',
      role: 'student',
      phone: '+21698765432',
      dateOfBirth: new Date('1985-03-15'),
      address: {
        street: '123 Avenue Habib Bourguiba',
        city: 'Tunis',
        zipCode: '1000',
        country: 'Tunisia'
      },
      isActive: true,
      isVerified: true
    },
    {
      firstName: 'Fatma',
      lastName: 'Trabelsi',
      email: '<EMAIL>',
      password: 'student123',
      role: 'student',
      phone: '+21687654321',
      dateOfBirth: new Date('1982-07-22'),
      address: {
        street: '456 Rue de la République',
        city: 'Sfax',
        zipCode: '3000',
        country: 'Tunisia'
      },
      isActive: true,
      isVerified: true
    },
    {
      firstName: 'Dr. Mohamed',
      lastName: 'Gharbi',
      email: '<EMAIL>',
      password: 'prof123',
      role: 'professor',
      phone: '+21676543210',
      dateOfBirth: new Date('1975-11-08'),
      address: {
        street: '789 Boulevard du 7 Novembre',
        city: 'Tunis',
        zipCode: '1002',
        country: 'Tunisia'
      },
      isActive: true,
      isVerified: true
    },
    {
      firstName: 'Dr. Leila',
      lastName: 'Mansouri',
      email: '<EMAIL>',
      password: 'prof123',
      role: 'professor',
      phone: '+***********',
      dateOfBirth: new Date('1978-05-12'),
      address: {
        street: '321 Avenue de la Liberté',
        city: 'Sousse',
        zipCode: '4000',
        country: 'Tunisia'
      },
      isActive: true,
      isVerified: true
    }
  ],

  professors: [
    {
      employeeNumber: 'PROF001',
      academicTitle: 'Dr.',
      academicRank: 'Full Professor',
      department: 'Management',
      education: [{
        degree: 'PhD',
        fieldOfStudy: 'Business Administration',
        institution: 'University of Tunis',
        graduationYear: 2005,
        country: 'Tunisia'
      }],
      teachingExperience: {
        yearsOfTeaching: 15,
        teachingAreas: ['Strategic Management', 'Leadership', 'Organizational Behavior']
      },
      employmentStatus: 'full_time',
      contractType: 'permanent',
      startDate: new Date('2010-09-01')
    },
    {
      employeeNumber: 'PROF002',
      academicTitle: 'Dr.',
      academicRank: 'Associate Professor',
      department: 'Finance',
      education: [{
        degree: 'PhD',
        fieldOfStudy: 'Finance',
        institution: 'ESSEC Business School',
        graduationYear: 2008,
        country: 'France'
      }],
      teachingExperience: {
        yearsOfTeaching: 12,
        teachingAreas: ['Corporate Finance', 'Investment Analysis', 'Financial Markets']
      },
      employmentStatus: 'full_time',
      contractType: 'permanent',
      startDate: new Date('2012-09-01')
    }
  ],

  students: [
    {
      studentNumber: 'EMBA2024001',
      academicYear: '2024-2025',
      cohort: 'EMBA2024A',
      program: 'EMBA',
      specialization: 'General Management',
      enrollmentStatus: 'active',
      enrollmentDate: new Date('2024-09-01'),
      expectedGraduationDate: new Date('2026-06-30'),
      currentEmployment: {
        company: 'Tunisie Telecom',
        position: 'Marketing Director',
        industry: 'Telecommunications',
        yearsOfExperience: 12,
        managementLevel: 'Senior Level'
      },
      previousEducation: [{
        degree: 'Master',
        fieldOfStudy: 'Marketing',
        institution: 'IHEC Carthage',
        graduationYear: 2010,
        country: 'Tunisia'
      }],
      tuitionFee: {
        total: 25000,
        paid: 10000,
        remaining: 15000,
        currency: 'TND',
        paymentPlan: 'installments'
      },
      academicRecord: {
        creditsRequired: 60,
        creditsCompleted: 0,
        currentGPA: 0,
        academicStanding: 'satisfactory'
      }
    },
    {
      studentNumber: 'EMBA2024002',
      academicYear: '2024-2025',
      cohort: 'EMBA2024A',
      program: 'EMBA',
      specialization: 'Finance',
      enrollmentStatus: 'active',
      enrollmentDate: new Date('2024-09-01'),
      expectedGraduationDate: new Date('2026-06-30'),
      currentEmployment: {
        company: 'Banque Centrale de Tunisie',
        position: 'Senior Financial Analyst',
        industry: 'Banking',
        yearsOfExperience: 10,
        managementLevel: 'Mid-Level'
      },
      previousEducation: [{
        degree: 'Master',
        fieldOfStudy: 'Finance',
        institution: 'FSEG Tunis',
        graduationYear: 2012,
        country: 'Tunisia'
      }],
      tuitionFee: {
        total: 25000,
        paid: 25000,
        remaining: 0,
        currency: 'TND',
        paymentPlan: 'full_payment'
      },
      academicRecord: {
        creditsRequired: 60,
        creditsCompleted: 0,
        currentGPA: 0,
        academicStanding: 'satisfactory'
      }
    }
  ],

  modules: [
    {
      moduleCode: 'MOD101',
      title: 'Foundation of Management',
      description: 'Introduction aux concepts fondamentaux du management moderne',
      creditHours: 12,
      duration: {
        weeks: 8,
        totalContactHours: 48
      },
      category: 'Foundation',
      level: 'Beginner',
      theme: 'Leadership',
      academicYear: '2024-2025',
      semester: 'Fall',
      startDate: new Date('2024-09-15'),
      endDate: new Date('2024-11-10'),
      capacity: {
        maximum: 30,
        minimum: 15,
        current: 0
      },
      status: 'approved',
      feedback: {
        averageRatings: {
          contentRelevance: 4.5,
          instructorQuality: 4.3,
          learningExperience: 4.4,
          practicalApplication: 4.2,
          overallSatisfaction: 4.4
        }
      }
    }
  ],

  courses: [
    {
      courseCode: 'MGT101',
      title: 'Strategic Management',
      description: 'Cours sur la gestion stratégique des organisations',
      creditHours: 3,
      contactHours: 24,
      level: 'Core',
      category: 'Management',
      academicYear: '2024-2025',
      semester: 'Fall',
      startDate: new Date('2024-09-15'),
      endDate: new Date('2024-12-15'),
      capacity: {
        maximum: 25,
        minimum: 10,
        current: 0
      },
      status: 'active',
      registrationStatus: 'open',
      evaluations: {
        averageRatings: {
          courseContent: 4.3,
          instructorEffectiveness: 4.5,
          courseDifficulty: 3.8,
          workload: 3.5,
          overallSatisfaction: 4.2
        }
      }
    },
    {
      courseCode: 'FIN201',
      title: 'Corporate Finance',
      description: 'Principes de finance d\'entreprise',
      creditHours: 3,
      contactHours: 24,
      level: 'Core',
      category: 'Finance',
      academicYear: '2024-2025',
      semester: 'Fall',
      startDate: new Date('2024-09-15'),
      endDate: new Date('2024-12-15'),
      capacity: {
        maximum: 25,
        minimum: 10,
        current: 0
      },
      status: 'active',
      registrationStatus: 'open',
      evaluations: {
        averageRatings: {
          courseContent: 4.1,
          instructorEffectiveness: 4.4,
          courseDifficulty: 4.0,
          workload: 3.8,
          overallSatisfaction: 4.3
        }
      }
    }
  ]
};

// Fonction pour nettoyer la base de données
const clearDatabase = async () => {
  try {
    await User.deleteMany({});
    await Student.deleteMany({});
    await Professor.deleteMany({});
    await Course.deleteMany({});
    await Module.deleteMany({});
    console.log('🧹 Database cleared');
  } catch (error) {
    console.error('❌ Error clearing database:', error.message);
  }
};

// Fonction pour créer les utilisateurs
const createUsers = async () => {
  try {
    const users = await User.insertMany(seedData.users);
    console.log(`✅ Created ${users.length} users`);
    return users;
  } catch (error) {
    console.error('❌ Error creating users:', error.message);
    return [];
  }
};

// Fonction pour créer les professeurs
const createProfessors = async (users) => {
  try {
    const professorUsers = users.filter(user => user.role === 'professor');
    const professorsData = seedData.professors.map((prof, index) => ({
      ...prof,
      user: professorUsers[index]._id
    }));

    const professors = await Professor.insertMany(professorsData);
    console.log(`✅ Created ${professors.length} professors`);
    return professors;
  } catch (error) {
    console.error('❌ Error creating professors:', error.message);
    return [];
  }
};

// Fonction pour créer les étudiants
const createStudents = async (users) => {
  try {
    const studentUsers = users.filter(user => user.role === 'student');
    const studentsData = seedData.students.map((student, index) => ({
      ...student,
      user: studentUsers[index]._id
    }));

    const students = await Student.insertMany(studentsData);
    console.log(`✅ Created ${students.length} students`);
    return students;
  } catch (error) {
    console.error('❌ Error creating students:', error.message);
    return [];
  }
};

// Fonction pour créer les modules
const createModules = async (professors) => {
  try {
    const modulesData = seedData.modules.map(module => ({
      ...module,
      coordinator: professors[0]._id
    }));

    const modules = await Module.insertMany(modulesData);
    console.log(`✅ Created ${modules.length} modules`);
    return modules;
  } catch (error) {
    console.error('❌ Error creating modules:', error.message);
    return [];
  }
};

// Fonction pour créer les cours
const createCourses = async (professors) => {
  try {
    const coursesData = seedData.courses.map((course, index) => ({
      ...course,
      instructor: professors[index % professors.length]._id
    }));

    const courses = await Course.insertMany(coursesData);
    console.log(`✅ Created ${courses.length} courses`);
    return courses;
  } catch (error) {
    console.error('❌ Error creating courses:', error.message);
    return [];
  }
};

// Fonction principale de seed
const seedDatabase = async () => {
  try {
    console.log('🌱 Starting database seeding...');

    await connectDB();
    await clearDatabase();

    const users = await createUsers();
    const professors = await createProfessors(users);
    const students = await createStudents(users);
    const modules = await createModules(professors);
    const courses = await createCourses(professors);

    console.log('🎉 Database seeding completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`- Users: ${users.length}`);
    console.log(`- Professors: ${professors.length}`);
    console.log(`- Students: ${students.length}`);
    console.log(`- Modules: ${modules.length}`);
    console.log(`- Courses: ${courses.length}`);

    console.log('\n🔑 Default Login Credentials:');
    console.log('Admin: <EMAIL> / admin123');
    console.log('Student: <EMAIL> / student123');
    console.log('Professor: <EMAIL> / prof123');

  } catch (error) {
    console.error('❌ Seeding failed:', error.message);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
    process.exit(0);
  }
};

// Exécuter le script si appelé directement
if (require.main === module) {
  seedDatabase();
}

module.exports = { seedDatabase };
