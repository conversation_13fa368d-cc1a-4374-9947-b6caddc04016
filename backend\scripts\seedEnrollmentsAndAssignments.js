const mongoose = require('mongoose');
const Student = require('../models/Student');
const Course = require('../models/Course');
const Enrollment = require('../models/Enrollment');
const Assignment = require('../models/Assignment');
const Submission = require('../models/Submission');
const User = require('../models/User');

async function seedEnrollmentsAndAssignments() {
  try {
    await mongoose.connect('mongodb://localhost:27017/emba_management');
    console.log('✅ Connecté à MongoDB');

    // Find existing students and courses
    const students = await Student.find().populate('user');
    const courses = await Course.find().populate('instructor');

    console.log(`📚 Trouvé ${students.length} étudiants et ${courses.length} cours`);

    if (students.length === 0 || courses.length === 0) {
      console.log('❌ Pas assez de données de base. Exécutez d\'abord le script de seed principal.');
      return;
    }

    // Clear existing enrollments and assignments for clean start
    await Enrollment.deleteMany({});
    await Assignment.deleteMany({});
    await Submission.deleteMany({});
    console.log('🧹 Données existantes nettoyées');

    // Create enrollments for each student
    const enrollments = [];
    for (const student of students) {
      // Enroll each student in 2-3 random courses
      const shuffledCourses = courses.sort(() => 0.5 - Math.random());
      const studentCourses = shuffledCourses.slice(0, Math.floor(Math.random() * 2) + 2);

      for (const course of studentCourses) {
        const enrollment = new Enrollment({
          student: student._id,
          course: course._id,
          enrollmentNumber: `ENR2024${String(enrollments.length + 1).padStart(6, '0')}`,
          academicYear: '2024-2025',
          semester: 'Fall',
          enrollmentDate: new Date('2024-09-01'),
          startDate: new Date('2024-09-15'),
          expectedEndDate: new Date('2025-01-15'),
          status: 'active',
          tuitionFee: {
            amount: 5000,
            currency: 'TND',
            dueDate: new Date('2024-10-01'),
            paidAmount: Math.random() > 0.3 ? 5000 : Math.floor(Math.random() * 3000),
            remainingAmount: 0
          },
          academicPerformance: {
            currentGrade: Math.floor(Math.random() * 8) + 12, // 12-20
            letterGrade: ['A', 'A-', 'B+', 'B', 'B-', 'C+'][Math.floor(Math.random() * 6)],
            gpaPoints: Math.random() * 1.5 + 2.5, // 2.5-4.0
            creditHours: course.credits || 3
          },
          attendance: {
            totalSessions: 16,
            attendedSessions: Math.floor(Math.random() * 4) + 12, // 12-16
            attendanceRate: 0 // Will be calculated automatically
          }
        });

        enrollments.push(enrollment);
      }
    }

    await Enrollment.insertMany(enrollments);
    console.log(`✅ ${enrollments.length} inscriptions créées`);

    // Create assignments for each course
    const assignments = [];
    const assignmentTypes = ['homework', 'quiz', 'exam', 'project', 'presentation'];
    const assignmentTitles = {
      homework: ['Homework Assignment', 'Weekly Exercise', 'Practice Problems'],
      quiz: ['Quiz', 'Short Test', 'Knowledge Check'],
      exam: ['Midterm Exam', 'Final Exam', 'Chapter Test'],
      project: ['Research Project', 'Case Study Analysis', 'Group Project'],
      presentation: ['Class Presentation', 'Topic Presentation', 'Final Presentation']
    };

    for (const course of courses) {
      // Create 4-6 assignments per course
      const numAssignments = Math.floor(Math.random() * 3) + 4;
      
      for (let i = 0; i < numAssignments; i++) {
        const type = assignmentTypes[Math.floor(Math.random() * assignmentTypes.length)];
        const titles = assignmentTitles[type];
        const title = `${titles[Math.floor(Math.random() * titles.length)]} ${i + 1}`;
        
        const dueDate = new Date();
        dueDate.setDate(dueDate.getDate() + Math.floor(Math.random() * 30) + 1); // 1-30 days from now

        const assignment = new Assignment({
          title,
          description: `Description for ${title} in ${course.title}`,
          course: course._id,
          instructor: course.instructor,
          type,
          assignedDate: new Date(),
          dueDate,
          maxPoints: [50, 75, 100, 150][Math.floor(Math.random() * 4)],
          status: 'published',
          submissionSettings: {
            allowLateSubmission: Math.random() > 0.3,
            latePenaltyPerDay: Math.floor(Math.random() * 10) + 5,
            maxLateDays: Math.floor(Math.random() * 5) + 2,
            allowMultipleSubmissions: Math.random() > 0.7
          }
        });

        assignments.push(assignment);
      }
    }

    await Assignment.insertMany(assignments);
    console.log(`✅ ${assignments.length} devoirs créés`);

    // Create some submissions
    const submissions = [];
    for (const enrollment of enrollments) {
      const courseAssignments = assignments.filter(a => a.course.toString() === enrollment.course.toString());
      
      // Submit 60-80% of assignments
      const numToSubmit = Math.floor(courseAssignments.length * (0.6 + Math.random() * 0.2));
      const shuffledAssignments = courseAssignments.sort(() => 0.5 - Math.random());
      
      for (let i = 0; i < numToSubmit; i++) {
        const assignment = shuffledAssignments[i];
        const submittedAt = new Date(assignment.dueDate);
        submittedAt.setDate(submittedAt.getDate() - Math.floor(Math.random() * 5)); // Submit 0-5 days before due

        const grade = Math.floor(Math.random() * 40) + 60; // 60-100% of max points
        const submission = new Submission({
          assignment: assignment._id,
          student: enrollment.student,
          enrollment: enrollment._id,
          submittedAt,
          textContent: `Submission for ${assignment.title}`,
          status: Math.random() > 0.3 ? 'graded' : 'submitted',
          grade: Math.random() > 0.3 ? (grade / 100) * assignment.maxPoints : undefined,
          maxPoints: assignment.maxPoints,
          gradedAt: Math.random() > 0.3 ? new Date() : undefined
        });

        submissions.push(submission);
      }
    }

    await Submission.insertMany(submissions);
    console.log(`✅ ${submissions.length} soumissions créées`);

    // Update enrollment statistics
    for (const enrollment of enrollments) {
      const courseSubmissions = submissions.filter(s => s.enrollment.toString() === enrollment._id.toString());
      const gradedSubmissions = courseSubmissions.filter(s => s.grade !== undefined);
      
      if (gradedSubmissions.length > 0) {
        const totalGrade = gradedSubmissions.reduce((sum, s) => sum + s.grade, 0);
        const avgGrade = totalGrade / gradedSubmissions.length;
        
        enrollment.academicPerformance.currentGrade = Math.round(avgGrade);
        enrollment.progress.completedAssignments = courseSubmissions.length;
        enrollment.progress.totalAssignments = assignments.filter(a => a.course.toString() === enrollment.course.toString()).length;
        
        await enrollment.save();
      }
    }

    console.log('✅ Statistiques d\'inscription mises à jour');
    console.log('\n🎉 Données de test créées avec succès !');
    console.log('\nVous pouvez maintenant tester le dashboard gamifié avec des données réelles.');

  } catch (error) {
    console.error('❌ Erreur:', error);
  } finally {
    await mongoose.disconnect();
  }
}

// Execute if run directly
if (require.main === module) {
  seedEnrollmentsAndAssignments();
}

module.exports = seedEnrollmentsAndAssignments;
