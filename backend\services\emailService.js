const nodemailer = require('nodemailer');

// Configuration du transporteur email
const createTransporter = () => {
  try {
    // Configuration utilisant les variables d'environnement
    const config = {
      host: process.env.EMAIL_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.EMAIL_PORT) || 587,
      secure: process.env.EMAIL_SECURE === 'true', // true pour 465, false pour autres ports
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
      },
      // Options supplémentaires pour Gmail
      tls: {
        rejectUnauthorized: false
      }
    };

    // Vérifier que les credentials sont configurés
    if (!process.env.EMAIL_USER || !process.env.EMAIL_PASS) {
      console.warn('⚠️ Variables d\'environnement EMAIL_USER et EMAIL_PASS non configurées');
      console.warn('📧 Les emails ne seront pas envoyés');
      return null;
    }

    console.log('📧 Configuration email:', {
      host: config.host,
      port: config.port,
      user: config.auth.user,
      secure: config.secure
    });

    return nodemailer.createTransport(config);
  } catch (error) {
    console.error('❌ Erreur lors de la création du transporteur email:', error);
    return null;
  }
};

// Template d'email pour les identifiants étudiants
const getStudentCredentialsTemplate = (studentData) => {
  return {
    subject: 'Bienvenue dans le programme EMBA - Vos identifiants de connexion',
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #dc2626, #b91c1c); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f9fafb; padding: 30px; border-radius: 0 0 8px 8px; }
          .credentials { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #dc2626; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
          .button { display: inline-block; background: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🎓 Bienvenue dans le programme EMBA</h1>
            <p>Félicitations ! Votre candidature a été acceptée</p>
          </div>
          
          <div class="content">
            <h2>Bonjour ${studentData.firstName} ${studentData.lastName},</h2>
            
            <p>Nous avons le plaisir de vous informer que votre candidature au programme Executive MBA a été <strong>acceptée</strong> !</p>
            
            <p>Votre compte étudiant a été créé avec succès. Voici vos identifiants de connexion :</p>
            
            <div class="credentials">
              <h3>📧 Identifiants de connexion</h3>
              <p><strong>Numéro d'étudiant :</strong> ${studentData.studentNumber}</p>
              <p><strong>Email :</strong> ${studentData.email}</p>
              <p><strong>Mot de passe temporaire :</strong> ${studentData.temporaryPassword}</p>
            </div>
            
            <p><strong>⚠️ Important :</strong> Pour des raisons de sécurité, veuillez changer votre mot de passe lors de votre première connexion.</p>
            
            <a href="${process.env.FRONTEND_URL || 'http://localhost:3000'}/login" class="button">
              Se connecter à la plateforme
            </a>
            
            <h3>📋 Prochaines étapes :</h3>
            <ul>
              <li>Connectez-vous à votre espace étudiant</li>
              <li>Complétez votre profil</li>
              <li>Consultez votre planning de cours</li>
              <li>Téléchargez les documents nécessaires</li>
            </ul>
            
            <p>Si vous avez des questions, n'hésitez pas à nous contacter à <a href="mailto:<EMAIL>"><EMAIL></a></p>
            
            <p>Nous vous souhaitons une excellente formation !</p>
            
            <p>Cordialement,<br>
            <strong>L'équipe EMBA ESPRIT</strong></p>
          </div>
          
          <div class="footer">
            <p>© 2024 EMBA ESPRIT - Tous droits réservés</p>
            <p>Cet email a été envoyé automatiquement, merci de ne pas y répondre.</p>
          </div>
        </div>
      </body>
      </html>
    `
  };
};

// Template d'email pour candidature refusée
const getRejectionTemplate = (candidateData, reason) => {
  return {
    subject: 'Candidature EMBA - Décision',
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #f3f4f6; color: #374151; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f9fafb; padding: 30px; border-radius: 0 0 8px 8px; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Candidature EMBA</h1>
          </div>
          
          <div class="content">
            <h2>Bonjour ${candidateData.firstName} ${candidateData.lastName},</h2>
            
            <p>Nous vous remercions pour l'intérêt que vous avez porté à notre programme Executive MBA.</p>
            
            <p>Après un examen attentif de votre dossier, nous regrettons de vous informer que nous ne pouvons pas donner suite favorable à votre candidature pour cette session.</p>
            
            ${reason ? `<p><strong>Motif :</strong> ${reason}</p>` : ''}
            
            <p>Cette décision ne remet pas en question vos qualifications professionnelles. Nous vous encourageons à postuler à nouveau lors des prochaines sessions d'admission.</p>
            
            <p>Nous vous souhaitons beaucoup de succès dans vos projets futurs.</p>
            
            <p>Cordialement,<br>
            <strong>L'équipe EMBA ESPRIT</strong></p>
          </div>
          
          <div class="footer">
            <p>© 2024 EMBA ESPRIT - Tous droits réservés</p>
          </div>
        </div>
      </body>
      </html>
    `
  };
};

// Template d'email pour entretien programmé avec détails complets
const getInterviewTemplate = (candidateData, interviewData) => {
  // Formater la date et l'heure
  const formatDateTime = (date) => {
    const options = {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZone: 'Africa/Tunis'
    };
    return new Date(date).toLocaleDateString('fr-FR', options);
  };

  // Déterminer le type d'entretien et les instructions spécifiques
  const getInterviewTypeInfo = () => {
    switch (interviewData.location) {
      case 'video_call':
        return {
          icon: '💻',
          title: 'Entretien en visioconférence',
          instructions: `
            <div class="interview-details">
              <h3>💻 Entretien en ligne</h3>
              <p><strong>Lien de connexion :</strong></p>
              <div class="meeting-link">
                <a href="${interviewData.meetingLink}" target="_blank" class="button">
                  🔗 Rejoindre l'entretien
                </a>
                <p class="link-text">${interviewData.meetingLink}</p>
              </div>

              <div class="tech-requirements">
                <h4>📋 Prérequis techniques :</h4>
                <ul>
                  <li>Connexion internet stable</li>
                  <li>Caméra et microphone fonctionnels</li>
                  <li>Environnement calme et bien éclairé</li>
                  <li>Testez votre connexion 15 minutes avant</li>
                </ul>
              </div>
            </div>
          `
        };

      case 'in_person':
        return {
          icon: '🏢',
          title: 'Entretien en présentiel',
          instructions: `
            <div class="interview-details">
              <h3>🏢 Entretien sur site</h3>
              <div class="location-info">
                <p><strong>📍 Lieu :</strong> Campus ESPRIT</p>
                <p><strong>🚪 Salle :</strong> ${interviewData.meetingRoom || 'Salle à confirmer'}</p>
                <p><strong>📍 Adresse :</strong> Ariana, Tunisie</p>
              </div>

              <div class="arrival-instructions">
                <h4>📋 Instructions d'arrivée :</h4>
                <ul>
                  <li>Présentez-vous à l'accueil 15 minutes avant l'heure</li>
                  <li>Munissez-vous d'une pièce d'identité</li>
                  <li>Apportez une copie de votre CV et diplômes</li>
                  <li>Prévoyez du temps pour le stationnement</li>
                </ul>
              </div>
            </div>
          `
        };

      case 'phone':
        return {
          icon: '📞',
          title: 'Entretien téléphonique',
          instructions: `
            <div class="interview-details">
              <h3>📞 Entretien par téléphone</h3>
              <div class="phone-info">
                <p><strong>📱 Modalité :</strong> Nous vous appellerons au numéro que vous avez fourni lors de votre candidature</p>
                <p><strong>⏰ Heure d'appel :</strong> Exactement à l'heure prévue</p>
              </div>

              <div class="phone-instructions">
                <h4>📋 Préparation :</h4>
                <ul>
                  <li>Assurez-vous d'être dans un endroit calme</li>
                  <li>Vérifiez que votre téléphone est chargé</li>
                  <li>Ayez votre CV et documents à portée de main</li>
                  <li>Préparez un carnet pour prendre des notes</li>
                </ul>
              </div>
            </div>
          `
        };

      default:
        return {
          icon: '🎯',
          title: 'Entretien programmé',
          instructions: `
            <div class="interview-details">
              <h3>🎯 Détails à confirmer</h3>
              <p>Les modalités précises de l'entretien vous seront communiquées prochainement.</p>
            </div>
          `
        };
    }
  };

  const typeInfo = getInterviewTypeInfo();

  return {
    subject: `Candidature EMBA - ${typeInfo.title} programmé`,
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
          .container { max-width: 650px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white; padding: 30px; text-align: center; border-radius: 12px 12px 0 0; }
          .content { background: #f9fafb; padding: 30px; border-radius: 0 0 12px 12px; }
          .interview-info { background: white; padding: 25px; border-radius: 10px; margin: 25px 0; border-left: 5px solid #3b82f6; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
          .interview-details { background: #f0f9ff; padding: 20px; border-radius: 8px; margin: 15px 0; }
          .meeting-link { text-align: center; margin: 20px 0; }
          .button { display: inline-block; background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; margin: 10px 0; font-weight: bold; }
          .button:hover { background: #1d4ed8; }
          .link-text { font-size: 12px; color: #666; margin-top: 10px; word-break: break-all; }
          .location-info, .phone-info { background: white; padding: 15px; border-radius: 6px; margin: 15px 0; }
          .tech-requirements, .arrival-instructions, .phone-instructions { margin-top: 20px; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
          .urgent { background: #fef3c7; border: 1px solid #f59e0b; padding: 15px; border-radius: 8px; margin: 20px 0; }
          .date-highlight { background: #dcfce7; border: 1px solid #16a34a; padding: 15px; border-radius: 8px; text-align: center; margin: 20px 0; }
          h4 { color: #1d4ed8; margin-top: 20px; }
          ul { margin: 10px 0; padding-left: 20px; }
          li { margin: 5px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>${typeInfo.icon} ${typeInfo.title}</h1>
            <p>Votre candidature EMBA progresse !</p>
          </div>

          <div class="content">
            <h2>Bonjour ${candidateData.firstName} ${candidateData.lastName},</h2>

            <p>Nous avons le plaisir de vous informer que votre candidature au programme <strong>Executive MBA</strong> a retenu notre attention.</p>

            <p>Nous souhaitons vous rencontrer lors d'un entretien pour mieux vous connaître et discuter de votre projet professionnel.</p>

            <div class="date-highlight">
              <h3>📅 Date et heure de l'entretien</h3>
              <p style="font-size: 18px; font-weight: bold; color: #1d4ed8; margin: 10px 0;">
                ${formatDateTime(interviewData.scheduledDate)}
              </p>
              <p><strong>⏱️ Durée estimée :</strong> ${interviewData.duration || 60} minutes</p>
            </div>

            ${typeInfo.instructions}

            <div class="urgent">
              <h4>⚠️ Important - À retenir :</h4>
              <ul>
                <li><strong>Ponctualité :</strong> Soyez prêt(e) 15 minutes avant l'heure</li>
                <li><strong>Documents :</strong> Ayez votre CV et diplômes à disposition</li>
                <li><strong>Contact :</strong> En cas d'imprévu, contactez-nous immédiatement</li>
                <li><strong>Confirmation :</strong> Merci de confirmer votre présence par retour d'email</li>
              </ul>
            </div>

            <div class="interview-info">
              <h3>📋 Préparation de l'entretien</h3>
              <p>Pour optimiser cet échange, nous vous recommandons de :</p>
              <ul>
                <li><strong>Préparer votre présentation :</strong> Parcours professionnel en 5 minutes</li>
                <li><strong>Définir vos motivations :</strong> Pourquoi l'EMBA ? Pourquoi maintenant ?</li>
                <li><strong>Projet professionnel :</strong> Vos objectifs à court et moyen terme</li>
                <li><strong>Questions sur le programme :</strong> Préparez vos interrogations</li>
                <li><strong>Exemples concrets :</strong> Situations de leadership, défis relevés</li>
              </ul>
            </div>

            <p>Nous nous réjouissons de vous rencontrer et de découvrir votre profil !</p>

            <p><strong>Contact en cas de besoin :</strong><br>
            📧 Email : <a href="mailto:<EMAIL>"><EMAIL></a><br>
            📞 Téléphone : +216 XX XXX XXX</p>

            <p>Cordialement,<br>
            <strong>L'équipe EMBA ESPRIT</strong></p>
          </div>

          <div class="footer">
            <p>© 2024 EMBA ESPRIT - Tous droits réservés</p>
            <p>Cet email contient des informations importantes concernant votre entretien.</p>
          </div>
        </div>
      </body>
      </html>
    `
  };
};

// Fonction pour envoyer un email
const sendEmail = async (to, template) => {
  try {
    const transporter = createTransporter();

    // Si le transporteur n'est pas disponible, simuler l'envoi
    if (!transporter) {
      console.warn('⚠️ Transporteur email non disponible - Simulation d\'envoi');
      console.log('📧 Email simulé envoyé à:', to);
      console.log('📧 Sujet:', template.subject);
      return {
        success: true,
        messageId: 'simulated-' + Date.now(),
        simulated: true
      };
    }

    const mailOptions = {
      from: `"EMBA ESPRIT" <${process.env.EMAIL_USER || process.env.EMAIL_FROM || '<EMAIL>'}>`,
      to: to,
      subject: template.subject,
      html: template.html
    };

    console.log('📧 Tentative d\'envoi d\'email à:', to);
    const result = await transporter.sendMail(mailOptions);
    console.log('✅ Email envoyé avec succès:', result.messageId);
    return { success: true, messageId: result.messageId };
  } catch (error) {
    console.error('❌ Erreur lors de l\'envoi de l\'email:', error);
    console.error('📧 Détails de l\'erreur:', {
      code: error.code,
      command: error.command,
      response: error.response
    });

    // En cas d'erreur, on considère que l'email est "envoyé" pour ne pas bloquer le processus
    console.warn('⚠️ Email non envoyé mais processus continué');
    return {
      success: true,
      error: error.message,
      fallback: true
    };
  }
};

// Template d'email pour le code de confirmation de changement de mot de passe
const getPasswordResetCodeTemplate = (data) => {
  return {
    subject: 'Code de confirmation - Changement de mot de passe EMBA',
    html: `
      <!DOCTYPE html>
      <html lang="fr">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Code de Confirmation</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #dc2626, #b91c1c); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #f9fafb; padding: 30px; border-radius: 0 0 10px 10px; }
          .code-box { background: #fff; border: 2px solid #dc2626; border-radius: 8px; padding: 20px; text-align: center; margin: 20px 0; }
          .code { font-size: 32px; font-weight: bold; color: #dc2626; letter-spacing: 5px; font-family: monospace; }
          .warning { background: #fef3c7; border-left: 4px solid #f59e0b; padding: 15px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 30px; color: #6b7280; font-size: 14px; }
          .button { display: inline-block; background: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🔐 Code de Confirmation</h1>
            <p>Changement de mot de passe EMBA</p>
          </div>

          <div class="content">
            <h2>Bonjour,</h2>

            <p>Vous avez demandé à changer votre mot de passe pour votre compte EMBA. Pour des raisons de sécurité, veuillez utiliser le code de confirmation ci-dessous :</p>

            <div class="code-box">
              <div class="code">${data.confirmationCode}</div>
              <p style="margin: 10px 0 0 0; color: #6b7280;">Code de confirmation</p>
            </div>

            <div class="warning">
              <strong>⚠️ Important :</strong>
              <ul style="margin: 10px 0;">
                <li>Ce code expire dans <strong>15 minutes</strong></li>
                <li>Ne partagez jamais ce code avec personne</li>
                <li>Si vous n'avez pas demandé ce changement, ignorez cet email</li>
              </ul>
            </div>

            <h3>Comment utiliser ce code :</h3>
            <ol>
              <li>Retournez sur la page de changement de mot de passe</li>
              <li>Saisissez ce code de confirmation</li>
              <li>Entrez votre nouveau mot de passe</li>
              <li>Confirmez le changement</li>
            </ol>

            <p>Si vous rencontrez des difficultés, contactez l'administration EMBA.</p>

            <div class="footer">
              <p>Cet email a été envoyé automatiquement par le système EMBA ESPRIT.</p>
              <p>© ${new Date().getFullYear()} EMBA ESPRIT - Tous droits réservés</p>
            </div>
          </div>
        </div>
      </body>
      </html>
    `
  };
};

// Fonctions spécialisées
const sendStudentCredentials = async (studentData) => {
  const template = getStudentCredentialsTemplate(studentData);
  return await sendEmail(studentData.email, template);
};

const sendPasswordResetCode = async (data) => {
  const template = getPasswordResetCodeTemplate(data);
  return await sendEmail(data.email, template);
};

const sendRejectionEmail = async (candidateData, reason) => {
  const template = getRejectionTemplate(candidateData, reason);
  return await sendEmail(candidateData.email, template);
};

const sendInterviewEmail = async (candidateData, interviewData) => {
  const template = getInterviewTemplate(candidateData, interviewData);
  return await sendEmail(candidateData.email, template);
};

module.exports = {
  sendEmail,
  sendStudentCredentials,
  sendPasswordResetCode,
  sendRejectionEmail,
  sendInterviewEmail,
  getStudentCredentialsTemplate,
  getPasswordResetCodeTemplate,
  getRejectionTemplate,
  getInterviewTemplate
};
