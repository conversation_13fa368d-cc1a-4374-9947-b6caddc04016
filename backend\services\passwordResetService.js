const crypto = require('crypto');
const bcrypt = require('bcryptjs');
const User = require('../models/User');
const { sendPasswordResetCode } = require('./emailService');

// Stockage temporaire des codes de confirmation (en production, utilisez Redis)
const passwordResetCodes = new Map();

// Générer un code de confirmation à 6 chiffres
const generateConfirmationCode = () => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// Envoyer un code de confirmation par email
const sendPasswordResetConfirmation = async (userId, email) => {
  try {
    // Générer un code de confirmation
    const confirmationCode = generateConfirmationCode();
    
    // Stocker le code avec une expiration de 15 minutes
    const expirationTime = Date.now() + (15 * 60 * 1000); // 15 minutes
    passwordResetCodes.set(userId, {
      code: confirmationCode,
      email: email,
      expiresAt: expirationTime,
      attempts: 0
    });

    // Envoyer l'email
    const result = await sendPasswordResetCode({
      email: email,
      confirmationCode: confirmationCode
    });

    console.log(`📧 Code de confirmation envoyé à ${email}: ${confirmationCode}`);
    
    return {
      success: true,
      message: 'Code de confirmation envoyé par email',
      expiresIn: 15 // minutes
    };
  } catch (error) {
    console.error('❌ Erreur lors de l\'envoi du code:', error);
    return {
      success: false,
      error: 'Erreur lors de l\'envoi du code de confirmation'
    };
  }
};

// Vérifier le code de confirmation
const verifyConfirmationCode = async (userId, providedCode) => {
  try {
    const storedData = passwordResetCodes.get(userId);
    
    if (!storedData) {
      return {
        success: false,
        error: 'Aucun code de confirmation trouvé. Veuillez en demander un nouveau.'
      };
    }

    // Vérifier l'expiration
    if (Date.now() > storedData.expiresAt) {
      passwordResetCodes.delete(userId);
      return {
        success: false,
        error: 'Le code de confirmation a expiré. Veuillez en demander un nouveau.'
      };
    }

    // Incrémenter les tentatives
    storedData.attempts += 1;

    // Limiter les tentatives (max 5)
    if (storedData.attempts > 5) {
      passwordResetCodes.delete(userId);
      return {
        success: false,
        error: 'Trop de tentatives incorrectes. Veuillez demander un nouveau code.'
      };
    }

    // Vérifier le code
    if (storedData.code !== providedCode) {
      return {
        success: false,
        error: `Code incorrect. ${5 - storedData.attempts} tentatives restantes.`
      };
    }

    // Code correct - marquer comme vérifié
    storedData.verified = true;
    
    return {
      success: true,
      message: 'Code de confirmation vérifié avec succès'
    };
  } catch (error) {
    console.error('❌ Erreur lors de la vérification du code:', error);
    return {
      success: false,
      error: 'Erreur lors de la vérification du code'
    };
  }
};

// Changer le mot de passe après vérification du code
const changePasswordWithCode = async (userId, newPassword) => {
  try {
    const storedData = passwordResetCodes.get(userId);
    
    if (!storedData || !storedData.verified) {
      return {
        success: false,
        error: 'Code de confirmation non vérifié. Veuillez d\'abord vérifier votre code.'
      };
    }

    // Vérifier que le code n'a pas expiré
    if (Date.now() > storedData.expiresAt) {
      passwordResetCodes.delete(userId);
      return {
        success: false,
        error: 'Le code de confirmation a expiré. Veuillez recommencer le processus.'
      };
    }

    // Valider le nouveau mot de passe
    if (!newPassword || newPassword.length < 8) {
      return {
        success: false,
        error: 'Le mot de passe doit contenir au moins 8 caractères'
      };
    }

    // Hasher le nouveau mot de passe
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
    const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

    // Mettre à jour le mot de passe dans la base de données
    await User.findByIdAndUpdate(userId, {
      password: hashedPassword,
      passwordChangedAt: new Date()
    });

    // Supprimer le code de confirmation utilisé
    passwordResetCodes.delete(userId);

    console.log(`✅ Mot de passe changé avec succès pour l'utilisateur ${userId}`);

    return {
      success: true,
      message: 'Mot de passe modifié avec succès'
    };
  } catch (error) {
    console.error('❌ Erreur lors du changement de mot de passe:', error);
    return {
      success: false,
      error: 'Erreur lors du changement de mot de passe'
    };
  }
};

// Nettoyer les codes expirés (à exécuter périodiquement)
const cleanupExpiredCodes = () => {
  const now = Date.now();
  for (const [userId, data] of passwordResetCodes.entries()) {
    if (now > data.expiresAt) {
      passwordResetCodes.delete(userId);
    }
  }
};

// Nettoyer les codes expirés toutes les 5 minutes
setInterval(cleanupExpiredCodes, 5 * 60 * 1000);

module.exports = {
  sendPasswordResetConfirmation,
  verifyConfirmationCode,
  changePasswordWithCode,
  cleanupExpiredCodes
};
