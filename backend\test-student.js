const mongoose = require('mongoose');
const User = require('./models/User');
const Student = require('./models/Student');
const Course = require('./models/Course');
const Enrollment = require('./models/Enrollment');
const Assignment = require('./models/Assignment');
const Submission = require('./models/Submission');

async function createTestData() {
  try {
    await mongoose.connect('mongodb://localhost:27017/emba_management');
    console.log('✅ Connecté à MongoDB');

    // 1. Créer un utilisateur étudiant de test
    let user = await User.findOne({ email: '<EMAIL>' });
    
    if (!user) {
      user = new User({
        firstName: 'Test',
        lastName: 'Student',
        email: '<EMAIL>',
        password: 'student123',
        role: 'student',
        phone: '+21612345678',
        isActive: true,
        isVerified: true
      });
      await user.save();
      console.log('✅ Utilisateur étudiant créé');
    } else {
      console.log('ℹ️  Utilisateur étudiant existe déjà');
    }

    // 2. C<PERSON>er le profil étudiant
    let student = await Student.findOne({ user: user._id });
    
    if (!student) {
      student = new Student({
        user: user._id,
        studentNumber: 'TEST2025001',
        academicYear: '2024-2025',
        cohort: 'TEST2025',
        program: 'EMBA',
        specialization: 'General Management',
        enrollmentStatus: 'active',
        enrollmentDate: new Date(),
        expectedGraduationDate: new Date('2026-06-30'),
        academicRecord: {
          creditsRequired: 60,
          creditsCompleted: 0,
          currentGPA: 0,
          academicStanding: 'satisfactory'
        }
      });
      await student.save();
      console.log('✅ Profil étudiant créé');
    } else {
      console.log('ℹ️  Profil étudiant existe déjà');
    }

    // 3. Créer quelques cours de test
    const coursesData = [
      {
        code: 'MGT501',
        title: 'Strategic Management',
        description: 'Advanced strategic management concepts',
        credits: 3,
        schedule: 'Samedi 9:00-12:00'
      },
      {
        code: 'FIN502', 
        title: 'Corporate Finance',
        description: 'Corporate financial management',
        credits: 3,
        schedule: 'Dimanche 14:00-17:00'
      }
    ];

    const courses = [];
    for (const courseData of coursesData) {
      let course = await Course.findOne({ code: courseData.code });
      if (!course) {
        course = new Course(courseData);
        await course.save();
        console.log(`✅ Cours ${courseData.code} créé`);
      }
      courses.push(course);
    }

    // 4. Créer des inscriptions
    for (const course of courses) {
      let enrollment = await Enrollment.findOne({ student: student._id, course: course._id });
      
      if (!enrollment) {
        enrollment = new Enrollment({
          student: student._id,
          course: course._id,
          academicYear: '2024-2025',
          semester: 'Fall',
          enrollmentDate: new Date(),
          startDate: new Date(),
          expectedEndDate: new Date('2025-01-15'),
          status: 'active',
          tuitionFee: {
            amount: 5000,
            currency: 'TND',
            dueDate: new Date('2024-12-31'),
            paidAmount: 3000,
            remainingAmount: 2000
          },
          academicPerformance: {
            currentGrade: Math.floor(Math.random() * 5) + 15, // 15-20
            letterGrade: ['A', 'A-', 'B+'][Math.floor(Math.random() * 3)],
            gpaPoints: Math.random() * 1 + 3, // 3.0-4.0
            creditHours: course.credits
          },
          attendance: {
            totalSessions: 16,
            attendedSessions: Math.floor(Math.random() * 3) + 14, // 14-16
            attendanceRate: 0 // Calculé automatiquement
          }
        });
        
        await enrollment.save();
        console.log(`✅ Inscription au cours ${course.code} créée`);
      }
    }

    // 5. Créer quelques devoirs
    for (const course of courses) {
      const assignmentsData = [
        {
          title: `Quiz 1 - ${course.title}`,
          type: 'quiz',
          maxPoints: 50,
          dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // Dans 7 jours
        },
        {
          title: `Assignment 1 - ${course.title}`,
          type: 'homework', 
          maxPoints: 100,
          dueDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000) // Dans 14 jours
        }
      ];

      for (const assignmentData of assignmentsData) {
        let assignment = await Assignment.findOne({ 
          course: course._id, 
          title: assignmentData.title 
        });

        if (!assignment) {
          assignment = new Assignment({
            ...assignmentData,
            course: course._id,
            instructor: user._id, // Utiliser l'utilisateur comme instructeur pour le test
            description: `Description for ${assignmentData.title}`,
            status: 'published'
          });
          
          await assignment.save();
          console.log(`✅ Devoir "${assignmentData.title}" créé`);

          // Créer une soumission pour certains devoirs
          if (Math.random() > 0.5) {
            const submission = new Submission({
              assignment: assignment._id,
              student: student._id,
              enrollment: (await Enrollment.findOne({ student: student._id, course: course._id }))._id,
              textContent: `Ma soumission pour ${assignment.title}`,
              grade: Math.floor(Math.random() * 30) + 70, // 70-100% des points
              status: 'graded',
              gradedAt: new Date()
            });
            
            await submission.save();
            console.log(`✅ Soumission pour "${assignmentData.title}" créée`);
          }
        }
      }
    }

    console.log('\n🎉 Données de test créées avec succès !');
    console.log('\n📋 Identifiants de connexion :');
    console.log('   Email: <EMAIL>');
    console.log('   Mot de passe: student123');
    console.log('\n🌐 Testez maintenant sur : http://localhost:3000/login');

  } catch (error) {
    console.error('❌ Erreur:', error);
  } finally {
    await mongoose.disconnect();
  }
}

createTestData();
