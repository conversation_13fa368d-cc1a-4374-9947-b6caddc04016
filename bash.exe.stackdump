Stack trace:
Frame         Function      Args
0007FFFF9EE0  00021006118E (00021028DEE8, 000210272B3E, 0007FFFF9EE0, 0007FFFF8DE0) msys-2.0.dll+0x2118E
0007FFFF9EE0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x69BA
0007FFFF9EE0  0002100469F2 (00021028DF99, 0007FFFF9D98, 0007FFFF9EE0, 000000000000) msys-2.0.dll+0x69F2
0007FFFF9EE0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF9EE0  00021006A545 (0007FFFF9EF0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFF9EF0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE2E620000 ntdll.dll
7FFE2D930000 KERNEL32.DLL
7FFE2C0E0000 KERNELBASE.dll
7FFE2D6D0000 USER32.dll
7FFE2BED0000 win32u.dll
7FFE2DAA0000 GDI32.dll
7FFE2BBD0000 gdi32full.dll
7FFE2BF00000 msvcp_win.dll
7FFE2BAB0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFE2D880000 advapi32.dll
7FFE2DBB0000 msvcrt.dll
7FFE2E060000 sechost.dll
7FFE2C650000 RPCRT4.dll
7FFE2B170000 CRYPTBASE.DLL
7FFE2BFA0000 bcryptPrimitives.dll
7FFE2C610000 IMM32.DLL
