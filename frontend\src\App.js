import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider } from './contexts/ThemeContext';
import { AuthProvider } from './contexts/AuthContext';

// Composants publics
import Header from './components/Header';
import Footer from './components/Footer';
import Home from './pages/Home';
import About from './pages/About';
import Contact from './pages/Contact';
import Apply from './pages/Apply';
import PublicLogin from './pages/PublicLogin';

// Composants d'administration
import Layout from './components/Layout/Layout';
import ProtectedRoute, { PublicRoute } from './components/ProtectedRoute';
import Dashboard from './pages/Dashboard';
import Students from './pages/Students';
import Applications from './pages/Applications';
import StudentDashboard from './pages/StudentDashboard';
import ProfessorList from './components/admin/ProfessorList';
import ProfessorDashboard from './pages/ProfessorDashboard';
import Professor<PERSON>rofile from './components/professor/ProfessorProfile';
import ProfessorCoursesPage from './pages/professor/ProfessorCoursesPage';
import ProfessorGradesPage from './pages/professor/ProfessorGradesPage';
import Professor<PERSON><PERSON>dancePage from './pages/professor/ProfessorAttendancePage';
import ProfessorStudentsPage from './pages/professor/ProfessorStudentsPage';
import ProfessorNotificationsPage from './pages/professor/ProfessorNotificationsPage';
import ProfessorSettingsPage from './pages/professor/ProfessorSettingsPage';
import AdminCourses from './components/admin/AdminCourses';
import AdminClassesView from './components/admin/AdminClassesView';
import AdminModules from './components/admin/AdminModules';
import AdminPayments from './components/admin/AdminPayments';
import AdminNotifications from './components/admin/AdminNotifications';
import AdminDocuments from './components/admin/AdminDocuments';
import CalendarSchedule from './components/admin/CalendarSchedule';
import AdminGrades from './components/admin/AdminGrades';
import AdminSettings from './components/admin/AdminSettings';


// Composant wrapper pour les routes
const AppContent = () => {
  return (
    <Router>
        <Routes>
          {/* Page d'accueil publique */}
          <Route
            path="/"
            element={
              <div className="min-h-screen flex flex-col">
                <Header />
                <main className="flex-grow">
                  <Home />
                </main>
                <Footer />
              </div>
            }
          />

          {/* Autres pages publiques */}
          <Route
            path="/about"
            element={
              <div className="min-h-screen flex flex-col">
                <Header />
                <main className="flex-grow">
                  <About />
                </main>
                <Footer />
              </div>
            }
          />
          <Route
            path="/contact"
            element={
              <div className="min-h-screen flex flex-col">
                <Header />
                <main className="flex-grow">
                  <Contact />
                </main>
                <Footer />
              </div>
            }
          />
          <Route
            path="/apply"
            element={
              <div className="min-h-screen flex flex-col">
                <Header />
                <main className="flex-grow">
                  <Apply />
                </main>
                <Footer />
              </div>
            }
          />

          {/* Routes publiques */}
          <Route
            path="/login"
            element={
              <div className="min-h-screen flex flex-col">
                <Header />
                <main className="flex-grow">
                  <PublicLogin />
                </main>
                <Footer />
              </div>
            }
          />
          <Route
            path="/admin/login"
            element={
              <PublicRoute>
                <div className="min-h-screen flex flex-col">
                  <Header />
                  <main className="flex-grow">
                    <PublicLogin />
                  </main>
                  <Footer />
                </div>
              </PublicRoute>
            }
          />

          {/* Student Dashboard Route */}
          <Route
            path="/student/dashboard"
            element={
              <ProtectedRoute requiredRole="student">
                <StudentDashboard />
              </ProtectedRoute>
            }
          />

          {/* Routes d'administration protégées */}
          <Route
            path="/admin"
            element={
              <ProtectedRoute>
                <Layout>
                  <Dashboard />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/dashboard"
            element={
              <ProtectedRoute>
                <Layout>
                  <Dashboard />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/students"
            element={
              <ProtectedRoute>
                <Layout>
                  <Students />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/classes"
            element={
              <ProtectedRoute>
                <Layout>
                  <AdminClassesView />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/applications"
            element={
              <ProtectedRoute>
                <Layout>
                  <Applications />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/professors"
            element={
              <ProtectedRoute>
                <Layout>
                  <ProfessorList />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/modules"
            element={
              <ProtectedRoute>
                <Layout>
                  <AdminModules />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/courses"
            element={
              <ProtectedRoute>
                <Layout>
                  <AdminCourses />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/payments"
            element={
              <ProtectedRoute>
                <Layout>
                  <AdminPayments />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/notifications"
            element={
              <ProtectedRoute>
                <Layout>
                  <AdminNotifications />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/documents"
            element={
              <ProtectedRoute>
                <Layout>
                  <AdminDocuments />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/schedule"
            element={
              <ProtectedRoute>
                <Layout>
                  <CalendarSchedule />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/grades"
            element={
              <ProtectedRoute>
                <Layout>
                  <AdminGrades />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/settings"
            element={
              <ProtectedRoute>
                <Layout>
                  <AdminSettings />
                </Layout>
              </ProtectedRoute>
            }
          />

          {/* Routes pour les professeurs */}
          <Route
            path="/professor/dashboard"
            element={
              <ProtectedRoute requiredRole="professor">
                <ProfessorDashboard />
              </ProtectedRoute>
            }
          />
          <Route
            path="/professor/courses"
            element={
              <ProtectedRoute requiredRole="professor">
                <ProfessorDashboard>
                  <ProfessorCoursesPage />
                </ProfessorDashboard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/professor/grades"
            element={
              <ProtectedRoute requiredRole="professor">
                <ProfessorDashboard>
                  <ProfessorGradesPage />
                </ProfessorDashboard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/professor/attendance"
            element={
              <ProtectedRoute requiredRole="professor">
                <ProfessorDashboard>
                  <ProfessorAttendancePage />
                </ProfessorDashboard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/professor/students"
            element={
              <ProtectedRoute requiredRole="professor">
                <ProfessorDashboard>
                  <ProfessorStudentsPage />
                </ProfessorDashboard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/professor/profile"
            element={
              <ProtectedRoute requiredRole="professor">
                <ProfessorDashboard>
                  <ProfessorProfile />
                </ProfessorDashboard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/professor/notifications"
            element={
              <ProtectedRoute requiredRole="professor">
                <ProfessorDashboard>
                  <ProfessorNotificationsPage />
                </ProfessorDashboard>
              </ProtectedRoute>
            }
          />
          <Route
            path="/professor/settings"
            element={
              <ProtectedRoute requiredRole="professor">
                <ProfessorDashboard>
                  <ProfessorSettingsPage />
                </ProfessorDashboard>
              </ProtectedRoute>
            }
          />
        </Routes>
    </Router>
  );
};

function App() {
  return (
    <ThemeProvider>
      <AuthProvider>
        <AppContent />
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;
