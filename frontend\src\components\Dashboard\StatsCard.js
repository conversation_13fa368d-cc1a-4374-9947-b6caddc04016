import React from 'react';
import { cn } from '../../utils/cn';

const StatsCard = ({
  title,
  value,
  change,
  changeType = 'increase',
  icon: Icon,
  color = 'primary',
  className
}) => {
  const colorClasses = {
    primary: 'bg-primary-500',
    success: 'bg-success-500',
    warning: 'bg-warning-500',
    error: 'bg-error-500',
    info: 'bg-info-500'
  };

  const changeClasses = {
    increase: 'text-success-600 dark:text-success-400',
    decrease: 'text-error-600 dark:text-error-400',
    neutral: 'text-gray-600 dark:text-gray-400'
  };

  return (
    <div className={cn('card p-4 sm:p-6 w-full', className)}>
      <div className="flex items-center justify-between">
        <div className="flex-1 min-w-0 pr-4">
          <p className="text-sm font-medium text-gray-600 dark:text-gray-400 truncate">
            {title}
          </p>
          <p className="mt-1 text-2xl sm:text-3xl font-semibold text-gray-900 dark:text-white truncate">
            {value}
          </p>
          {change && (
            <p className={cn('mt-1 text-sm', changeClasses[changeType])}>
              {changeType === 'increase' && '+'}
              {change}
            </p>
          )}
        </div>

        {Icon && (
          <div className={cn(
            'flex-shrink-0 p-2 sm:p-3 rounded-lg',
            colorClasses[color]
          )}>
            <Icon className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
          </div>
        )}
      </div>
    </div>
  );
};

export default StatsCard;
