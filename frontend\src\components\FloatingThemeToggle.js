import React from 'react';
import { SunIcon, MoonIcon } from '@heroicons/react/24/outline';
import { useTheme } from '../contexts/ThemeContext';
import { cn } from '../utils/cn';

const FloatingThemeToggle = ({ className }) => {
  const { isDarkMode, toggleTheme } = useTheme();

  return (
    <button
      onClick={toggleTheme}
      className={cn(
        'fixed right-6 bottom-6 z-50',
        'flex items-center justify-center',
        'w-14 h-14 rounded-full shadow-lg',
        'bg-white dark:bg-gray-800',
        'border border-gray-200 dark:border-gray-700',
        'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100',
        'hover:shadow-xl hover:scale-105',
        'transition-all duration-300 ease-in-out',
        'focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
        'focus:ring-offset-white dark:focus:ring-offset-gray-900',
        'backdrop-blur-sm',
        className
      )}
      aria-label={isDarkMode ? 'Activer le mode clair' : 'Activer le mode sombre'}
      title={isDarkMode ? 'Activer le mode clair' : 'Activer le mode sombre'}
    >
      <div className="relative">
        {/* Icône Soleil (Mode Clair) */}
        <SunIcon
          className={cn(
            'h-6 w-6 transition-all duration-300 ease-in-out',
            isDarkMode 
              ? 'rotate-90 scale-0 opacity-0' 
              : 'rotate-0 scale-100 opacity-100'
          )}
        />
        
        {/* Icône Lune (Mode Sombre) */}
        <MoonIcon
          className={cn(
            'absolute inset-0 h-6 w-6 transition-all duration-300 ease-in-out',
            isDarkMode 
              ? 'rotate-0 scale-100 opacity-100' 
              : '-rotate-90 scale-0 opacity-0'
          )}
        />
      </div>
    </button>
  );
};

export default FloatingThemeToggle;
