import React from 'react';

// Classes CSS communes pour les champs de formulaire avec support du mode sombre
const baseInputClasses = "w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors duration-200";

const baseLabelClasses = "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2";

// Composant pour les champs de saisie texte
export const TextInput = ({ 
  label, 
  id, 
  name, 
  type = "text", 
  value, 
  onChange, 
  placeholder, 
  required = false,
  className = "",
  ...props 
}) => {
  return (
    <div>
      {label && (
        <label htmlFor={id} className={baseLabelClasses}>
          {label} {required && '*'}
        </label>
      )}
      <input
        type={type}
        id={id}
        name={name}
        value={value}
        onChange={onChange}
        required={required}
        className={`${baseInputClasses} ${className}`}
        placeholder={placeholder}
        {...props}
      />
    </div>
  );
};

// Composant pour les champs select
export const SelectInput = ({ 
  label, 
  id, 
  name, 
  value, 
  onChange, 
  options = [], 
  placeholder = "Sélectionnez une option",
  required = false,
  className = "",
  children,
  ...props 
}) => {
  return (
    <div>
      {label && (
        <label htmlFor={id} className={baseLabelClasses}>
          {label} {required && '*'}
        </label>
      )}
      <select
        id={id}
        name={name}
        value={value}
        onChange={onChange}
        required={required}
        className={`${baseInputClasses} ${className}`}
        {...props}
      >
        {placeholder && <option value="">{placeholder}</option>}
        {options.map((option, index) => (
          <option key={index} value={option.value}>
            {option.label}
          </option>
        ))}
        {children}
      </select>
    </div>
  );
};

// Composant pour les champs textarea
export const TextareaInput = ({ 
  label, 
  id, 
  name, 
  value, 
  onChange, 
  placeholder, 
  required = false,
  rows = 4,
  className = "",
  ...props 
}) => {
  return (
    <div>
      {label && (
        <label htmlFor={id} className={baseLabelClasses}>
          {label} {required && '*'}
        </label>
      )}
      <textarea
        id={id}
        name={name}
        value={value}
        onChange={onChange}
        required={required}
        rows={rows}
        className={`${baseInputClasses} ${className}`}
        placeholder={placeholder}
        {...props}
      />
    </div>
  );
};

// Composant pour les champs de fichier
export const FileInput = ({ 
  label, 
  id, 
  name, 
  onChange, 
  accept,
  required = false,
  className = "",
  ...props 
}) => {
  return (
    <div>
      {label && (
        <label htmlFor={id} className={baseLabelClasses}>
          {label} {required && '*'}
        </label>
      )}
      <input
        type="file"
        id={id}
        name={name}
        onChange={onChange}
        required={required}
        accept={accept}
        className={`${baseInputClasses} ${className}`}
        {...props}
      />
    </div>
  );
};

// Export des classes pour utilisation directe si nécessaire
export { baseInputClasses, baseLabelClasses };
