import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import {
  HomeIcon,
  AcademicCapIcon,
  UserGroupIcon,
  BookOpenIcon,
  CalendarDaysIcon,
  ChartBarIcon,
  CurrencyDollarIcon,
  DocumentTextIcon,
  BellIcon,
  Cog6ToothIcon,
  XMarkIcon,
  UserIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { cn } from '../../utils/cn';

// Navigation pour les admins
const adminNavigation = [
  { name: 'Dashboard', href: '/admin/dashboard', icon: HomeIcon },
  { name: 'Candidatures', href: '/admin/applications', icon: DocumentTextIcon },
  { name: 'Étudiants', href: '/admin/students', icon: AcademicCapIcon },
  { name: 'Classes', href: '/admin/classes', icon: UserGroupIcon },
  { name: 'Professeurs', href: '/admin/professors', icon: UserGroupIcon },
  // { name: 'Modules', href: '/admin/modules', icon: BookOpenIcon },
  { name: 'Cours', href: '/admin/courses', icon: BookOpenIcon },
  { name: 'Planning', href: '/admin/schedule', icon: CalendarDaysIcon },
  { name: 'Notes', href: '/admin/grades', icon: ChartBarIcon },
  { name: 'Paiements', href: '/admin/payments', icon: CurrencyDollarIcon },
  { name: 'Documents', href: '/admin/documents', icon: DocumentTextIcon },
  { name: 'Notifications', href: '/admin/notifications', icon: BellIcon },
];

// Navigation pour les professeurs
const professorNavigation = [
  { name: 'Dashboard', href: '/professor/dashboard', icon: HomeIcon },
  { name: 'Mon Profil', href: '/professor/profile', icon: UserIcon },
  { name: 'Mes Cours', href: '/professor/courses', icon: BookOpenIcon },
  { name: 'Mes Étudiants', href: '/professor/students', icon: AcademicCapIcon },
  { name: 'Planning', href: '/professor/schedule', icon: CalendarDaysIcon },
  { name: 'Notes & Évaluations', href: '/professor/grades', icon: ChartBarIcon },
  { name: 'Heures de Bureau', href: '/professor/office-hours', icon: ClockIcon },
  { name: 'Documents', href: '/professor/documents', icon: DocumentTextIcon },
  { name: 'Notifications', href: '/professor/notifications', icon: BellIcon },
];

// Navigation pour les étudiants
const studentNavigation = [
  { name: 'Dashboard', href: '/student/dashboard', icon: HomeIcon },
  { name: 'Mon Profil', href: '/student/dashboard/profile', icon: UserIcon },
  { name: 'Mes Cours', href: '/student/dashboard/courses', icon: BookOpenIcon },
  { name: 'Planning', href: '/student/dashboard/schedule', icon: CalendarDaysIcon },
  { name: 'Notes', href: '/student/dashboard/grades', icon: ChartBarIcon },
  { name: 'Documents', href: '/student/dashboard/documents', icon: DocumentTextIcon },
  { name: 'Notifications', href: '/student/dashboard/notifications', icon: BellIcon },
];

const getSecondaryNavigation = (role) => {
  switch (role) {
    case 'admin':
      return [{ name: 'Paramètres', href: '/admin/settings', icon: Cog6ToothIcon }];
    case 'professor':
      return [{ name: 'Paramètres', href: '/professor/settings', icon: Cog6ToothIcon }];
    case 'student':
      return [{ name: 'Paramètres', href: '/student/dashboard/settings', icon: Cog6ToothIcon }];
    default:
      return [];
  }
};

const Sidebar = ({ isOpen, onClose }) => {
  const location = useLocation();
  const { user } = useAuth();

  // Déterminer la navigation selon le rôle
  const getNavigation = () => {
    switch (user?.role) {
      case 'admin':
        return adminNavigation;
      case 'professor':
        return professorNavigation;
      case 'student':
        return studentNavigation;
      default:
        return adminNavigation; // Par défaut
    }
  };

  const navigation = getNavigation();
  const secondaryNavigation = getSecondaryNavigation(user?.role);
  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <div className={cn(
        'fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 transform transition-transform duration-300 ease-in-out',
        'lg:translate-x-0 lg:fixed lg:z-40',
        isOpen ? 'translate-x-0' : '-translate-x-full'
      )}>
        <div className="flex flex-col h-full">

          {/* Header mobile */}
          <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200 dark:border-gray-700 lg:hidden">
            <div className="flex items-center space-x-3">
              <div className="h-8 w-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">E</span>
              </div>
              <span className="text-xl font-display font-semibold text-gray-900 dark:text-white">
                EMBA
              </span>
            </div>
            <button
              onClick={onClose}
              className="p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-100 dark:hover:bg-gray-800"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-1 overflow-y-auto">
            <div className="space-y-1">
              {navigation.map((item) => {
                const isActive = location.pathname === item.href ||
                  (item.href === '/admin/dashboard' && (location.pathname === '/admin' || location.pathname === '/admin/dashboard'));

                return (
                  <Link
                    key={item.name}
                    to={item.href}
                    onClick={onClose}
                    className={cn(
                      'nav-link group',
                      isActive && 'nav-link-active'
                    )}
                    aria-current={isActive ? 'page' : undefined}
                  >
                    <item.icon
                      className={cn(
                        'mr-3 h-5 w-5 flex-shrink-0',
                        isActive
                          ? 'text-primary-600 dark:text-primary-400'
                          : 'text-gray-400 group-hover:text-primary-600 dark:group-hover:text-primary-400'
                      )}
                    />
                    {item.name}
                  </Link>
                );
              })}
            </div>

            {/* Divider */}
            <div className="border-t border-gray-200 dark:border-gray-700 my-6"></div>

            {/* Secondary navigation */}
            <div className="space-y-1">
              {secondaryNavigation.map((item) => {
                const isActive = location.pathname === item.href;

                return (
                  <Link
                    key={item.name}
                    to={item.href}
                    onClick={onClose}
                    className={cn(
                      'nav-link group',
                      isActive && 'nav-link-active'
                    )}
                  >
                    <item.icon className={cn(
                      'mr-3 h-5 w-5 flex-shrink-0',
                      isActive
                        ? 'text-primary-600 dark:text-primary-400'
                        : 'text-gray-400 group-hover:text-primary-600 dark:group-hover:text-primary-400'
                    )} />
                    {item.name}
                  </Link>
                );
              })}
            </div>
          </nav>

          {/* Footer */}
          <div className="flex-shrink-0 p-4 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-3">
              <div className="h-10 w-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">EM</span>
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                  EMBA System
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                  Version 1.0.0
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
