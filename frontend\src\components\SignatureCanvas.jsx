import React, { useRef, useState, useEffect } from 'react';

const SignatureCanvas = ({ onSignatureChange, required = false, className = '' }) => {
  const canvasRef = useRef(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [hasSignature, setHasSignature] = useState(false);
  const [lastPosition, setLastPosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const canvas = canvasRef.current;
    if (canvas) {
      const ctx = canvas.getContext('2d');
      ctx.lineCap = 'round';
      ctx.lineJoin = 'round';
      ctx.strokeStyle = '#000000';
      ctx.lineWidth = 2;
      
      // Set canvas size
      canvas.width = canvas.offsetWidth;
      canvas.height = canvas.offsetHeight;
      
      // Fill with white background
      ctx.fillStyle = '#ffffff';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
    }
  }, []);

  const getMousePos = (e) => {
    const canvas = canvasRef.current;
    const rect = canvas.getBoundingClientRect();
    return {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    };
  };

  const getTouchPos = (e) => {
    const canvas = canvasRef.current;
    const rect = canvas.getBoundingClientRect();
    return {
      x: e.touches[0].clientX - rect.left,
      y: e.touches[0].clientY - rect.top
    };
  };

  const startDrawing = (e) => {
    e.preventDefault();
    setIsDrawing(true);
    const pos = e.type.includes('touch') ? getTouchPos(e) : getMousePos(e);
    setLastPosition(pos);
  };

  const draw = (e) => {
    e.preventDefault();
    if (!isDrawing) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    const pos = e.type.includes('touch') ? getTouchPos(e) : getMousePos(e);

    ctx.beginPath();
    ctx.moveTo(lastPosition.x, lastPosition.y);
    ctx.lineTo(pos.x, pos.y);
    ctx.stroke();

    setLastPosition(pos);
    setHasSignature(true);
    
    // Notify parent component
    if (onSignatureChange) {
      onSignatureChange(canvas.toDataURL());
    }
  };

  const stopDrawing = (e) => {
    e.preventDefault();
    setIsDrawing(false);
  };

  const clearSignature = () => {
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    setHasSignature(false);
    
    if (onSignatureChange) {
      onSignatureChange(null);
    }
  };

  const getSignatureData = () => {
    if (!hasSignature) return null;
    return canvasRef.current.toDataURL();
  };

  // Expose methods to parent
  React.useImperativeHandle(React.forwardRef(() => canvasRef), () => ({
    getSignatureData,
    clearSignature,
    hasSignature
  }));

  return (
    <div className={`signature-canvas-container ${className}`}>
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Signature manuscrite {required && <span className="text-red-500">*</span>}
        </label>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
          Signez dans la zone ci-dessous avec votre souris ou votre doigt
        </p>
      </div>
      
      <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4 bg-white dark:bg-gray-800">
        <canvas
          ref={canvasRef}
          className="w-full h-40 border border-gray-200 dark:border-gray-700 rounded cursor-crosshair"
          onMouseDown={startDrawing}
          onMouseMove={draw}
          onMouseUp={stopDrawing}
          onMouseLeave={stopDrawing}
          onTouchStart={startDrawing}
          onTouchMove={draw}
          onTouchEnd={stopDrawing}
          style={{ touchAction: 'none' }}
        />
        
        <div className="flex justify-between items-center mt-3">
          <div className="flex items-center space-x-2">
            {hasSignature && (
              <span className="text-sm text-green-600 dark:text-green-400 flex items-center">
                <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                Signature capturée
              </span>
            )}
            {!hasSignature && (
              <span className="text-sm text-gray-500 dark:text-gray-400">
                Aucune signature
              </span>
            )}
          </div>
          
          <button
            type="button"
            onClick={clearSignature}
            className="px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            disabled={!hasSignature}
          >
            Effacer
          </button>
        </div>
      </div>
      
      {required && !hasSignature && (
        <p className="text-sm text-red-500 mt-2">
          La signature est obligatoire pour soumettre votre candidature
        </p>
      )}
    </div>
  );
};

export default SignatureCanvas;
