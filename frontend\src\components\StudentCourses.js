import React, { useState, useEffect } from 'react';
import {
  BookOpenIcon,
  UserIcon,
  CalendarIcon,
  ClockIcon,
  ChartBarIcon,
  AcademicCapIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';
import { cn } from '../utils/cn';

const StudentCourses = () => {
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedCourse, setSelectedCourse] = useState(null);

  // Données exemple - à remplacer par de vraies données de l'API
  const [coursesData] = useState([
    {
      id: 1,
      code: 'MGT501',
      title: 'Strategic Management',
      description: 'Ce cours explore les concepts fondamentaux de la gestion stratégique dans un environnement d\'affaires complexe.',
      instructor: {
        name: 'Dr. <PERSON>',
        email: '<EMAIL>',
        office: 'Bureau 201'
      },
      schedule: {
        day: 'Samedi',
        time: '9:00-12:00',
        room: 'Salle A101',
        location: 'Campus Principal'
      },
      progress: {
        completed: 75,
        total: 100
      },
      grade: {
        current: 'A-',
        percentage: 87,
        outOf20: 17.4
      },
      credits: 3,
      status: 'active',
      nextSession: '2024-12-21',
      assignments: [
        {
          id: 1,
          title: 'Strategic Analysis Report',
          dueDate: '2024-12-20',
          status: 'pending'
        },
        {
          id: 2,
          title: 'Case Study Presentation',
          dueDate: '2024-12-15',
          status: 'completed',
          grade: 'A'
        }
      ],
      attendance: {
        present: 12,
        total: 15,
        rate: 80
      }
    },
    {
      id: 2,
      code: 'FIN502',
      title: 'Corporate Finance',
      description: 'Analyse approfondie des décisions financières dans les entreprises modernes.',
      instructor: {
        name: 'Dr. Fatma Gharbi',
        email: '<EMAIL>',
        office: 'Bureau 305'
      },
      schedule: {
        day: 'Dimanche',
        time: '14:00-17:00',
        room: 'Salle B203',
        location: 'Campus Principal'
      },
      progress: {
        completed: 60,
        total: 100
      },
      grade: {
        current: 'B+',
        percentage: 82,
        outOf20: 16.4
      },
      credits: 3,
      status: 'active',
      nextSession: '2024-12-22',
      assignments: [
        {
          id: 3,
          title: 'Financial Modeling Project',
          dueDate: '2024-12-25',
          status: 'in_progress'
        }
      ],
      attendance: {
        present: 10,
        total: 12,
        rate: 83
      }
    },
    {
      id: 3,
      code: 'MKT503',
      title: 'Digital Marketing Strategy',
      description: 'Stratégies marketing dans l\'ère numérique et transformation digitale.',
      instructor: {
        name: 'Prof. Sarah Mansouri',
        email: '<EMAIL>',
        office: 'Bureau 150'
      },
      schedule: {
        day: 'Lundi',
        time: '18:00-21:00',
        room: 'Salle C105',
        location: 'Campus Principal'
      },
      progress: {
        completed: 45,
        total: 100
      },
      grade: {
        current: 'B',
        percentage: 78,
        outOf20: 15.6
      },
      credits: 2,
      status: 'active',
      nextSession: '2024-12-23',
      assignments: [
        {
          id: 4,
          title: 'Digital Campaign Analysis',
          dueDate: '2024-12-30',
          status: 'not_started'
        }
      ],
      attendance: {
        present: 8,
        total: 10,
        rate: 80
      }
    }
  ]);

  useEffect(() => {
    loadCourses();
  }, []);

  const loadCourses = async () => {
    try {
      setLoading(true);
      // TODO: Remplacer par un vrai appel API
      // const response = await fetch('/api/students/me/courses', {
      //   headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      // });
      // const data = await response.json();
      // setCourses(data.courses);
      
      // Simulation d'un délai de chargement
      setTimeout(() => {
        setCourses(coursesData);
        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error('Erreur lors du chargement des cours:', error);
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    const colors = {
      'active': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      'completed': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
      'paused': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
    };
    return colors[status] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
  };

  const getGradeColor = (percentage) => {
    if (percentage >= 90) return 'text-green-600 dark:text-green-400';
    if (percentage >= 80) return 'text-blue-600 dark:text-blue-400';
    if (percentage >= 70) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  const getAttendanceColor = (rate) => {
    if (rate >= 90) return 'text-green-600 dark:text-green-400';
    if (rate >= 80) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <BookOpenIcon className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    Cours Actifs
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {courses.filter(c => c.status === 'active').length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <AcademicCapIcon className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    Crédits en Cours
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {courses.reduce((sum, course) => sum + course.credits, 0)}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ChartBarIcon className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    Moyenne Générale
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {(courses.reduce((sum, course) => sum + course.grade.outOf20, 0) / courses.length).toFixed(1)}/20
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Courses List */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {courses.map((course) => (
          <div key={course.id} className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div className="p-6">
              {/* Course Header */}
              <div className="flex items-start justify-between mb-4">
                <div>
                  <div className="flex items-center">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                      {course.code}
                    </h3>
                    <span className={cn(
                      'ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                      getStatusColor(course.status)
                    )}>
                      {course.status === 'active' ? 'Actif' : course.status}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    {course.title}
                  </p>
                </div>
                <div className="text-right">
                  <div className={cn('text-lg font-semibold', getGradeColor(course.grade.percentage))}>
                    {course.grade.current}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {course.grade.outOf20}/20
                  </div>
                </div>
              </div>

              {/* Course Info */}
              <div className="space-y-3 mb-4">
                <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                  <UserIcon className="h-4 w-4 mr-2" />
                  {course.instructor.name}
                </div>
                <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                  <CalendarIcon className="h-4 w-4 mr-2" />
                  {course.schedule.day} {course.schedule.time}
                </div>
                <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                  <ClockIcon className="h-4 w-4 mr-2" />
                  Prochaine session: {new Date(course.nextSession).toLocaleDateString('fr-FR')}
                </div>
              </div>

              {/* Progress Bar */}
              <div className="mb-4">
                <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
                  <span>Progression</span>
                  <span>{course.progress.completed}%</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div
                    className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${course.progress.completed}%` }}
                  ></div>
                </div>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-500 dark:text-gray-400">Présence:</span>
                  <span className={cn('ml-1 font-medium', getAttendanceColor(course.attendance.rate))}>
                    {course.attendance.rate}%
                  </span>
                </div>
                <div>
                  <span className="text-gray-500 dark:text-gray-400">Crédits:</span>
                  <span className="ml-1 font-medium text-gray-900 dark:text-white">
                    {course.credits}
                  </span>
                </div>
              </div>

              {/* Assignments */}
              {course.assignments.length > 0 && (
                <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                    Devoirs récents
                  </h4>
                  <div className="space-y-2">
                    {course.assignments.slice(0, 2).map((assignment) => (
                      <div key={assignment.id} className="flex items-center justify-between text-sm">
                        <div className="flex items-center">
                          {assignment.status === 'completed' ? (
                            <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2" />
                          ) : assignment.status === 'pending' ? (
                            <ExclamationTriangleIcon className="h-4 w-4 text-yellow-500 mr-2" />
                          ) : (
                            <InformationCircleIcon className="h-4 w-4 text-gray-400 mr-2" />
                          )}
                          <span className="text-gray-600 dark:text-gray-400 truncate">
                            {assignment.title}
                          </span>
                        </div>
                        <span className="text-xs text-gray-500 dark:text-gray-500">
                          {new Date(assignment.dueDate).toLocaleDateString('fr-FR')}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Action Button */}
              <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                <button
                  onClick={() => setSelectedCourse(course)}
                  className="w-full text-center px-4 py-2 text-sm font-medium text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 hover:bg-primary-50 dark:hover:bg-primary-900 rounded-md transition-colors"
                >
                  Voir les détails
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {courses.length === 0 && (
        <div className="text-center py-12">
          <BookOpenIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
            Aucun cours
          </h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Vous n'êtes inscrit à aucun cours pour le moment.
          </p>
        </div>
      )}
    </div>
  );
};

export default StudentCourses;
