import React, { useState, useEffect } from 'react';
import {
  UserIcon,
  AcademicCapIcon,
  BriefcaseIcon,
  DocumentTextIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationCircleIcon,
  ChartBarIcon,
  CalendarDaysIcon,
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import { cn } from '../utils/cn';

const StudentDetails = ({ student, onClose, onEdit, onDelete }) => {
  const [activeTab, setActiveTab] = useState('profile');
  const [applicationProgress, setApplicationProgress] = useState(null);
  const [actionHistory, setActionHistory] = useState([]);
  const [loading, setLoading] = useState(false);

  // Charger les détails supplémentaires
  useEffect(() => {
    if (student) {
      loadStudentDetails();
    }
  }, [student]);

  const loadStudentDetails = async () => {
    setLoading(true);
    try {
      // Charger la progression de la candidature
      const progressResponse = await fetch(`http://localhost:5000/api/students/${student.id}/application-progress`);
      if (progressResponse.ok) {
        const progressData = await progressResponse.json();
        setApplicationProgress(progressData);
      }

      // Charger l'historique des actions
      const historyResponse = await fetch(`http://localhost:5000/api/students/${student.id}/action-history`);
      if (historyResponse.ok) {
        const historyData = await historyResponse.json();
        setActionHistory(historyData.actions || []);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des détails:', error);
    } finally {
      setLoading(false);
    }
  };

  const tabs = [
    { id: 'profile', label: 'Profil', icon: UserIcon },
    { id: 'academic', label: 'Académique', icon: AcademicCapIcon },
    { id: 'progress', label: 'Progression', icon: ChartBarIcon },
    { id: 'history', label: 'Historique', icon: ClockIcon },
    { id: 'actions', label: 'Actions', icon: PencilIcon }
  ];

  const getProgressColor = (percentage) => {
    if (percentage >= 80) return 'bg-green-500';
    if (percentage >= 60) return 'bg-yellow-500';
    if (percentage >= 40) return 'bg-orange-500';
    return 'bg-red-500';
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'in_progress':
        return <ExclamationCircleIcon className="h-5 w-5 text-yellow-500" />;
      case 'missing':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-400" />;
    }
  };

  const renderProfileTab = () => (
    <div className="space-y-6">
      {/* Informations personnelles */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
          <UserIcon className="h-5 w-5 mr-2" />
          Informations personnelles
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Nom complet</label>
            <p className="text-gray-900 dark:text-white">{student.firstName} {student.lastName}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Email</label>
            <p className="text-gray-900 dark:text-white flex items-center">
              <EnvelopeIcon className="h-4 w-4 mr-2" />
              {student.email}
            </p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Téléphone</label>
            <p className="text-gray-900 dark:text-white flex items-center">
              <PhoneIcon className="h-4 w-4 mr-2" />
              {student.phone || 'Non renseigné'}
            </p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Date de naissance</label>
            <p className="text-gray-900 dark:text-white">
              {student.dateOfBirth ? new Date(student.dateOfBirth).toLocaleDateString('fr-FR') : 'Non renseigné'}
            </p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Nationalité</label>
            <p className="text-gray-900 dark:text-white">{student.nationality || 'Non renseigné'}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Adresse</label>
            <p className="text-gray-900 dark:text-white flex items-center">
              <MapPinIcon className="h-4 w-4 mr-2" />
              {student.address || 'Non renseigné'}
            </p>
          </div>
        </div>
      </div>

      {/* Informations de contact d'urgence */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Contact d'urgence</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Nom</label>
            <p className="text-gray-900 dark:text-white">{student.emergencyContact?.name || 'Non renseigné'}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Téléphone</label>
            <p className="text-gray-900 dark:text-white">{student.emergencyContact?.phone || 'Non renseigné'}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Relation</label>
            <p className="text-gray-900 dark:text-white">{student.emergencyContact?.relationship || 'Non renseigné'}</p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderAcademicTab = () => (
    <div className="space-y-6">
      {/* Informations académiques */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
          <AcademicCapIcon className="h-5 w-5 mr-2" />
          Informations académiques
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Numéro d'étudiant</label>
            <p className="text-gray-900 dark:text-white font-mono">{student.studentNumber || 'Non assigné'}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Programme</label>
            <p className="text-gray-900 dark:text-white">{student.program || 'EMBA'}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Année académique</label>
            <p className="text-gray-900 dark:text-white">{student.academicYear || 'Non défini'}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Cohorte</label>
            <p className="text-gray-900 dark:text-white">{student.cohort || 'Non défini'}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Spécialisation</label>
            <p className="text-gray-900 dark:text-white">{student.specialization || 'Management Général'}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Statut d'inscription</label>
            <p className="text-gray-900 dark:text-white">{student.enrollmentStatus || 'Actif'}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Date d'inscription</label>
            <p className="text-gray-900 dark:text-white">
              {student.enrollmentDate ? new Date(student.enrollmentDate).toLocaleDateString('fr-FR') : 'Non défini'}
            </p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Date de diplôme prévue</label>
            <p className="text-gray-900 dark:text-white">
              {student.expectedGraduationDate ? new Date(student.expectedGraduationDate).toLocaleDateString('fr-FR') : 'Non défini'}
            </p>
          </div>
        </div>
      </div>

      {/* Informations financières */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Informations financières</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Frais de scolarité total</label>
            <p className="text-gray-900 dark:text-white font-semibold">
              {student.tuitionFee?.total || '25,000'} {student.tuitionFee?.currency || 'TND'}
            </p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Montant payé</label>
            <p className="text-gray-900 dark:text-white text-green-600">
              {student.tuitionFee?.paid || '0'} {student.tuitionFee?.currency || 'TND'}
            </p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Solde restant</label>
            <p className="text-gray-900 dark:text-white text-red-600">
              {(student.tuitionFee?.total || 25000) - (student.tuitionFee?.paid || 0)} {student.tuitionFee?.currency || 'TND'}
            </p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderProgressTab = () => (
    <div className="space-y-6">
      {loading ? (
        <div className="flex items-center justify-center h-32">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        </div>
      ) : (
        <>
          {/* Progression globale */}
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
              <ChartBarIcon className="h-5 w-5 mr-2" />
              Progression de la candidature
            </h3>
            
            {applicationProgress ? (
              <div className="space-y-4">
                {applicationProgress.sections.map((section, index) => (
                  <div key={index} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        {getStatusIcon(section.status)}
                        <span className="ml-2 font-medium text-gray-900 dark:text-white">{section.name}</span>
                      </div>
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        {section.percentage}% complété
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div 
                        className={cn('h-2 rounded-full transition-all duration-300', getProgressColor(section.percentage))}
                        style={{ width: `${section.percentage}%` }}
                      ></div>
                    </div>
                    {section.missingFields && section.missingFields.length > 0 && (
                      <div className="mt-2">
                        <p className="text-sm text-red-600 dark:text-red-400">
                          Champs manquants: {section.missingFields.join(', ')}
                        </p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 dark:text-gray-400">Aucune donnée de progression disponible</p>
            )}
          </div>
        </>
      )}
    </div>
  );

  const renderHistoryTab = () => (
    <div className="space-y-6">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
          <ClockIcon className="h-5 w-5 mr-2" />
          Historique des actions
        </h3>
        
        {actionHistory.length > 0 ? (
          <div className="space-y-4">
            {actionHistory.map((action, index) => (
              <div key={index} className="flex items-start space-x-3 p-3 border border-gray-200 dark:border-gray-600 rounded-lg">
                <div className="flex-shrink-0">
                  <CalendarDaysIcon className="h-5 w-5 text-gray-400" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {action.action}
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {action.description}
                  </p>
                  <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                    {new Date(action.date).toLocaleString('fr-FR')} • Par {action.performedBy}
                  </p>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-500 dark:text-gray-400">Aucun historique disponible</p>
        )}
      </div>
    </div>
  );

  const renderActionsTab = () => (
    <div className="space-y-6">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
          <PencilIcon className="h-5 w-5 mr-2" />
          Actions administratives
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button
            onClick={() => onEdit(student)}
            className="flex items-center justify-center px-4 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200"
          >
            <PencilIcon className="h-5 w-5 mr-2" />
            Modifier le profil
          </button>
          
          <button
            onClick={() => {/* Fonction pour envoyer un email */}}
            className="flex items-center justify-center px-4 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-200"
          >
            <EnvelopeIcon className="h-5 w-5 mr-2" />
            Envoyer un email
          </button>
          
          <button
            onClick={() => {/* Fonction pour réinitialiser le mot de passe */}}
            className="flex items-center justify-center px-4 py-3 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg transition-colors duration-200"
          >
            <UserIcon className="h-5 w-5 mr-2" />
            Réinitialiser mot de passe
          </button>
          
          <button
            onClick={() => onDelete(student)}
            className="flex items-center justify-center px-4 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors duration-200"
          >
            <TrashIcon className="h-5 w-5 mr-2" />
            Supprimer l'étudiant
          </button>
        </div>
        
        <div className="mt-6 p-4 bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 rounded-lg">
          <h4 className="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2">Actions suggérées</h4>
          <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
            <li>• Vérifier la progression de la candidature</li>
            <li>• Contacter l'étudiant pour les documents manquants</li>
            <li>• Programmer un entretien de suivi</li>
            <li>• Mettre à jour les informations de contact</li>
          </ul>
        </div>
      </div>
    </div>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'profile':
        return renderProfileTab();
      case 'academic':
        return renderAcademicTab();
      case 'progress':
        return renderProgressTab();
      case 'history':
        return renderHistoryTab();
      case 'actions':
        return renderActionsTab();
      default:
        return renderProfileTab();
    }
  };

  return (
    <div className="max-w-6xl mx-auto">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="h-12 w-12 bg-primary-100 dark:bg-primary-800 rounded-full flex items-center justify-center">
              <span className="text-lg font-semibold text-primary-600 dark:text-primary-300">
                {student.firstName?.[0]}{student.lastName?.[0]}
              </span>
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                {student.firstName} {student.lastName}
              </h2>
              <p className="text-gray-500 dark:text-gray-400">
                {student.studentNumber || 'Numéro d\'étudiant non assigné'} • {student.program || 'EMBA'}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <XCircleIcon className="h-6 w-6" />
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700 mb-6">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={cn(
                'flex items-center py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200',
                activeTab === tab.id
                  ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
              )}
            >
              <tab.icon className="h-5 w-5 mr-2" />
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="min-h-96">
        {renderTabContent()}
      </div>
    </div>
  );
};

export default StudentDetails;
