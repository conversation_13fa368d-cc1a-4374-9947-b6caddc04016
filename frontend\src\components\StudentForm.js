import React, { useState, useEffect } from 'react';
import {
  UserIcon,
  EnvelopeIcon,
  PhoneIcon,
  AcademicCapIcon,
  CalendarDaysIcon,
  ExclamationCircleIcon
} from '@heroicons/react/24/outline';
import { cn } from '../utils/cn';

const StudentForm = ({ student, onSubmit, onCancel, isLoading = false }) => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    program: '',
    status: 'Actif',
    enrollmentDate: new Date().toISOString().split('T')[0]
  });

  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});

  // Programmes disponibles
  const programs = [
    'EMBA Finance',
    'EMBA Marketing',
    'EMBA Management',
    'EMBA Digital',
    'EMBA International'
  ];

  const statuses = [
    { value: 'Actif', label: 'Actif', color: 'green' },
    { value: 'Inactif', label: 'Inactif', color: 'red' },
    { value: 'En attente', label: 'En attente', color: 'yellow' },
    { value: 'Suspendu', label: 'Suspendu', color: 'gray' }
  ];

  // Initialiser le formulaire si on modifie un étudiant
  useEffect(() => {
    if (student) {
      setFormData({
        firstName: student.firstName || '',
        lastName: student.lastName || '',
        email: student.email || '',
        phone: student.phone || '',
        program: student.program || '',
        status: student.status || 'Actif',
        enrollmentDate: student.enrollmentDate || new Date().toISOString().split('T')[0]
      });
    }
  }, [student]);

  // Validation
  const validateField = (name, value) => {
    switch (name) {
      case 'firstName':
      case 'lastName':
        return !value.trim() ? 'Ce champ est requis' :
               value.trim().length < 2 ? 'Minimum 2 caractères' : '';

      case 'email':
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return !value.trim() ? 'Email requis' :
               !emailRegex.test(value) ? 'Email invalide' : '';

      case 'phone':
        const phoneRegex = /^(\+216\s?)?[0-9\s-]{8,}$/;
        return !value.trim() ? 'Téléphone requis' :
               !phoneRegex.test(value) ? 'Format: +216 XX XXX XXX' : '';

      case 'program':
        return !value ? 'Programme requis' : '';

      case 'enrollmentDate':
        return !value ? 'Date d\'inscription requise' : '';

      default:
        return '';
    }
  };

  // Gérer les changements
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Validation en temps réel si le champ a été touché
    if (touched[name]) {
      const error = validateField(name, value);
      setErrors(prev => ({ ...prev, [name]: error }));
    }
  };

  // Gérer le blur (quand on quitte un champ)
  const handleBlur = (e) => {
    const { name, value } = e.target;
    setTouched(prev => ({ ...prev, [name]: true }));

    const error = validateField(name, value);
    setErrors(prev => ({ ...prev, [name]: error }));
  };

  // Soumettre le formulaire
  const handleSubmit = (e) => {
    e.preventDefault();

    // Valider tous les champs
    const newErrors = {};
    Object.keys(formData).forEach(key => {
      const error = validateField(key, formData[key]);
      if (error) newErrors[key] = error;
    });

    setErrors(newErrors);
    setTouched(Object.keys(formData).reduce((acc, key) => ({ ...acc, [key]: true }), {}));

    // Si pas d'erreurs, soumettre
    if (Object.keys(newErrors).length === 0) {
      onSubmit(formData);
    }
  };

  const isEditing = !!student;

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Informations personnelles */}
      <div>
        <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4 flex items-center">
          <UserIcon className="h-4 w-4 mr-2" />
          Informations personnelles
        </h4>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          {/* Prénom */}
          <div>
            <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Prénom *
            </label>
            <input
              type="text"
              id="firstName"
              name="firstName"
              value={formData.firstName}
              onChange={handleChange}
              onBlur={handleBlur}
              className={cn(
                'w-full px-3 py-2 border rounded-lg shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors',
                'bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400',
                errors.firstName && touched.firstName
                  ? 'border-red-300 dark:border-red-600'
                  : 'border-gray-300 dark:border-gray-600'
              )}
              placeholder="Entrez le prénom"
            />
            {errors.firstName && touched.firstName && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400 flex items-center">
                <ExclamationCircleIcon className="h-4 w-4 mr-1" />
                {errors.firstName}
              </p>
            )}
          </div>

          {/* Nom */}
          <div>
            <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Nom *
            </label>
            <input
              type="text"
              id="lastName"
              name="lastName"
              value={formData.lastName}
              onChange={handleChange}
              onBlur={handleBlur}
              className={cn(
                'w-full px-3 py-2 border rounded-lg shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors',
                'bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400',
                errors.lastName && touched.lastName
                  ? 'border-red-300 dark:border-red-600'
                  : 'border-gray-300 dark:border-gray-600'
              )}
              placeholder="Entrez le nom"
            />
            {errors.lastName && touched.lastName && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400 flex items-center">
                <ExclamationCircleIcon className="h-4 w-4 mr-1" />
                {errors.lastName}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Contact */}
      <div>
        <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4 flex items-center">
          <EnvelopeIcon className="h-4 w-4 mr-2" />
          Informations de contact
        </h4>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          {/* Email */}
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Email *
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              onBlur={handleBlur}
              className={cn(
                'w-full px-3 py-2 border rounded-lg shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors',
                'bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400',
                errors.email && touched.email
                  ? 'border-red-300 dark:border-red-600'
                  : 'border-gray-300 dark:border-gray-600'
              )}
              placeholder="<EMAIL>"
            />
            {errors.email && touched.email && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400 flex items-center">
                <ExclamationCircleIcon className="h-4 w-4 mr-1" />
                {errors.email}
              </p>
            )}
          </div>

          {/* Téléphone */}
          <div>
            <label htmlFor="phone" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Téléphone *
            </label>
            <input
              type="tel"
              id="phone"
              name="phone"
              value={formData.phone}
              onChange={handleChange}
              onBlur={handleBlur}
              className={cn(
                'w-full px-3 py-2 border rounded-lg shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors',
                'bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400',
                errors.phone && touched.phone
                  ? 'border-red-300 dark:border-red-600'
                  : 'border-gray-300 dark:border-gray-600'
              )}
              placeholder="+216 XX XXX XXX"
            />
            {errors.phone && touched.phone && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400 flex items-center">
                <ExclamationCircleIcon className="h-4 w-4 mr-1" />
                {errors.phone}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Informations académiques */}
      <div>
        <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4 flex items-center">
          <AcademicCapIcon className="h-4 w-4 mr-2" />
          Informations académiques
        </h4>

        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          {/* Programme */}
          <div>
            <label htmlFor="program" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Programme *
            </label>
            <select
              id="program"
              name="program"
              value={formData.program}
              onChange={handleChange}
              onBlur={handleBlur}
              className={cn(
                'w-full px-3 py-2 border rounded-lg shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors',
                'bg-white dark:bg-gray-700 text-gray-900 dark:text-white',
                errors.program && touched.program
                  ? 'border-red-300 dark:border-red-600'
                  : 'border-gray-300 dark:border-gray-600'
              )}
            >
              <option value="">Sélectionner un programme</option>
              {programs.map(program => (
                <option key={program} value={program}>{program}</option>
              ))}
            </select>
            {errors.program && touched.program && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400 flex items-center">
                <ExclamationCircleIcon className="h-4 w-4 mr-1" />
                {errors.program}
              </p>
            )}
          </div>

          {/* Statut */}
          <div>
            <label htmlFor="status" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Statut
            </label>
            <select
              id="status"
              name="status"
              value={formData.status}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              {statuses.map(status => (
                <option key={status.value} value={status.value}>
                  {status.label}
                </option>
              ))}
            </select>
          </div>

          {/* Date d'inscription */}
          <div>
            <label htmlFor="enrollmentDate" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Date d'inscription *
            </label>
            <input
              type="date"
              id="enrollmentDate"
              name="enrollmentDate"
              value={formData.enrollmentDate}
              onChange={handleChange}
              onBlur={handleBlur}
              className={cn(
                'w-full px-3 py-2 border rounded-lg shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors',
                'bg-white dark:bg-gray-700 text-gray-900 dark:text-white',
                errors.enrollmentDate && touched.enrollmentDate
                  ? 'border-red-300 dark:border-red-600'
                  : 'border-gray-300 dark:border-gray-600'
              )}
            />
            {errors.enrollmentDate && touched.enrollmentDate && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400 flex items-center">
                <ExclamationCircleIcon className="h-4 w-4 mr-1" />
                {errors.enrollmentDate}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Boutons d'action */}
      <div className="flex flex-col sm:flex-row sm:justify-end gap-3 pt-6 border-t border-gray-200 dark:border-gray-700">
        <button
          type="button"
          onClick={onCancel}
          disabled={isLoading}
          className="w-full sm:w-auto px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Annuler
        </button>
        <button
          type="submit"
          disabled={isLoading}
          className="w-full sm:w-auto px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-lg focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
        >
          {isLoading ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {isEditing ? 'Modification...' : 'Ajout...'}
            </>
          ) : (
            isEditing ? 'Modifier l\'étudiant' : 'Ajouter l\'étudiant'
          )}
        </button>
      </div>
    </form>
  );
};

export default StudentForm;
