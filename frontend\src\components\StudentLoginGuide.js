import React, { useState } from 'react';
import {
  InformationCircleIcon,
  KeyIcon,
  EnvelopeIcon,
  EyeIcon,
  EyeSlashIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

const StudentLoginGuide = ({ isOpen, onClose }) => {
  const [showPassword, setShowPassword] = useState(false);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <InformationCircleIcon className="h-6 w-6 text-blue-500 mr-2" />
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Guide de Connexion Étudiant
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Introduction */}
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <h3 className="text-lg font-medium text-blue-900 dark:text-blue-100 mb-2">
              Comment se connecter en tant qu'étudiant ?
            </h3>
            <p className="text-blue-800 dark:text-blue-200">
              Une fois votre candidature approuvée par l'administration, un compte étudiant est automatiquement créé pour vous.
            </p>
          </div>

          {/* Step 1: Email */}
          <div className="space-y-3">
            <h4 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
              <EnvelopeIcon className="h-5 w-5 mr-2 text-green-500" />
              1. Adresse Email
            </h4>
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <p className="text-gray-700 dark:text-gray-300 mb-2">
                <strong>Utilisez l'email de votre candidature :</strong>
              </p>
              <div className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 font-mono text-sm">
                <EMAIL>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                C'est la même adresse email que vous avez utilisée lors de votre candidature.
              </p>
            </div>
          </div>

          {/* Step 2: Password */}
          <div className="space-y-3">
            <h4 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
              <KeyIcon className="h-5 w-5 mr-2 text-yellow-500" />
              2. Mot de Passe Temporaire
            </h4>
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <p className="text-gray-700 dark:text-gray-300 mb-3">
                <strong>Votre mot de passe temporaire est :</strong>
              </p>
              
              <div className="space-y-3">
                <div className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 flex items-center justify-between">
                  <span className="font-mono text-sm">
                    {showPassword ? 'EMBA2024' : '••••••••'}
                  </span>
                  <button
                    onClick={() => setShowPassword(!showPassword)}
                    className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                  >
                    {showPassword ? (
                      <EyeSlashIcon className="h-4 w-4" />
                    ) : (
                      <EyeIcon className="h-4 w-4" />
                    )}
                  </button>
                </div>
                
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  <p className="mb-1">
                    <strong>Alternative :</strong> Si "EMBA2024" ne fonctionne pas, essayez votre date de naissance au format :
                  </p>
                  <div className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 font-mono text-sm">
                    DDMMYYYY (ex: 15031990 pour le 15/03/1990)
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Step 3: Login Process */}
          <div className="space-y-3">
            <h4 className="text-lg font-medium text-gray-900 dark:text-white">
              3. Processus de Connexion
            </h4>
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <ol className="list-decimal list-inside space-y-2 text-gray-700 dark:text-gray-300">
                <li>Allez sur la page de connexion</li>
                <li>Entrez votre adresse email</li>
                <li>Entrez votre mot de passe temporaire</li>
                <li>Cliquez sur "Se connecter"</li>
                <li>Vous serez redirigé vers votre tableau de bord étudiant</li>
              </ol>
            </div>
          </div>

          {/* Important Notes */}
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
            <h4 className="text-lg font-medium text-yellow-900 dark:text-yellow-100 mb-2">
              ⚠️ Notes Importantes
            </h4>
            <ul className="list-disc list-inside space-y-1 text-yellow-800 dark:text-yellow-200 text-sm">
              <li>Votre compte est créé automatiquement après l'approbation de votre candidature</li>
              <li>Vous recevrez un email avec vos identifiants de connexion</li>
              <li>Il est recommandé de changer votre mot de passe après la première connexion</li>
              <li>En cas de problème, contactez l'administration</li>
            </ul>
          </div>

          {/* Student Number Info */}
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
            <h4 className="text-lg font-medium text-green-900 dark:text-green-100 mb-2">
              📋 Votre Numéro Étudiant
            </h4>
            <p className="text-green-800 dark:text-green-200 text-sm">
              Après votre première connexion, vous trouverez votre numéro étudiant unique (format: EMBA2024XXX) 
              dans votre profil. Ce numéro vous sera utile pour toutes vos démarches administratives.
            </p>
          </div>

          {/* Troubleshooting */}
          <div className="space-y-3">
            <h4 className="text-lg font-medium text-gray-900 dark:text-white">
              🔧 Problèmes de Connexion ?
            </h4>
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <div className="space-y-3 text-sm text-gray-700 dark:text-gray-300">
                <div>
                  <strong>Erreur "Email ou mot de passe incorrect" :</strong>
                  <ul className="list-disc list-inside ml-4 mt-1">
                    <li>Vérifiez que votre candidature a été approuvée</li>
                    <li>Essayez le mot de passe alternatif (date de naissance)</li>
                    <li>Vérifiez les majuscules/minuscules</li>
                  </ul>
                </div>
                <div>
                  <strong>Compte non trouvé :</strong>
                  <ul className="list-disc list-inside ml-4 mt-1">
                    <li>Votre candidature n'a peut-être pas encore été approuvée</li>
                    <li>Contactez l'administration pour vérifier le statut</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
          <div className="flex justify-end">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors"
            >
              Compris
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StudentLoginGuide;
