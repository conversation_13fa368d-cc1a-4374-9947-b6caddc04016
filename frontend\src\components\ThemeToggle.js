import React from 'react';
import { SunIcon, MoonIcon } from '@heroicons/react/24/outline';
import { useTheme } from '../contexts/ThemeContext';
import { cn } from '../utils/cn';

const ThemeToggle = ({ className }) => {
  const { isDarkMode, toggleTheme } = useTheme();

  return (
    <button
      onClick={toggleTheme}
      className={cn(
        'relative inline-flex h-10 w-10 items-center justify-center rounded-lg',
        'bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700',
        'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100',
        'transition-all duration-200 ease-in-out',
        'focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2',
        'focus:ring-offset-white dark:focus:ring-offset-gray-900',
        className
      )}
      aria-label={isDarkMode ? 'Activer le mode clair' : 'Activer le mode sombre'}
      title={isDarkMode ? 'Activer le mode clair' : 'Activer le mode sombre'}
    >
      <div className="relative">
        {/* Icône Soleil (Mode Clair) */}
        <SunIcon
          className={cn(
            'h-5 w-5 transition-all duration-300 ease-in-out',
            isDarkMode 
              ? 'rotate-90 scale-0 opacity-0' 
              : 'rotate-0 scale-100 opacity-100'
          )}
        />
        
        {/* Icône Lune (Mode Sombre) */}
        <MoonIcon
          className={cn(
            'absolute inset-0 h-5 w-5 transition-all duration-300 ease-in-out',
            isDarkMode 
              ? 'rotate-0 scale-100 opacity-100' 
              : '-rotate-90 scale-0 opacity-0'
          )}
        />
      </div>
    </button>
  );
};

export default ThemeToggle;
