import React, { useState, useEffect } from 'react';
import {
  UserGroupIcon,
  AcademicCapIcon,
  EnvelopeIcon,
  PhoneIcon,
  UserIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  DocumentTextIcon,
  CalendarIcon,
  MapPinIcon
} from '@heroicons/react/24/outline';
import { cn } from '../../utils/cn';

const AdminClassesView = () => {
  const [classes, setClasses] = useState([]);
  const [selectedClass, setSelectedClass] = useState(null);
  const [students, setStudents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [promotionFilter, setPromotionFilter] = useState('all');
  const [classStats, setClassStats] = useState({});

  // Charger toutes les classes
  const loadClasses = async () => {
    try {
      setLoading(true);
      
      // Récupérer les statistiques des classes
      const statsResponse = await fetch('http://localhost:5000/api/admin/classes/stats', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setClassStats(statsData.stats || {});
        
        // Extraire la liste des classes
        const classesArray = Object.keys(statsData.stats).map(className => ({
          name: className,
          promotion: className.substring(0, 5), // EMBA1 ou EMBA2
          studentCount: statsData.stats[className].count,
          capacity: 30 // Capacité maximale par classe
        }));
        
        setClasses(classesArray);
        
        // Sélectionner la première classe par défaut
        if (classesArray.length > 0) {
          setSelectedClass(classesArray[0].name);
          await loadClassStudents(classesArray[0].name);
        }
      }
    } catch (error) {
      console.error('Erreur lors du chargement des classes:', error);
    } finally {
      setLoading(false);
    }
  };

  // Charger les étudiants d'une classe
  const loadClassStudents = async (className) => {
    try {
      const response = await fetch(`http://localhost:5000/api/admin/classes/${className}/students`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setStudents(data.students || []);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des étudiants:', error);
      setStudents([]);
    }
  };

  useEffect(() => {
    loadClasses();
  }, []);

  const handleClassSelect = async (className) => {
    setSelectedClass(className);
    await loadClassStudents(className);
  };

  // Filtrer les classes selon la promotion
  const filteredClasses = classes.filter(classInfo => {
    if (promotionFilter === 'all') return true;
    return classInfo.promotion === promotionFilter;
  });

  // Filtrer les étudiants selon le terme de recherche
  const filteredStudents = students.filter(student => {
    if (!searchTerm) return true;
    const fullName = `${student.user.firstName} ${student.user.lastName}`.toLowerCase();
    const email = student.user.email.toLowerCase();
    const studentNumber = student.studentNumber.toLowerCase();
    const search = searchTerm.toLowerCase();
    
    return fullName.includes(search) || 
           email.includes(search) || 
           studentNumber.includes(search);
  });

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  const totalStudents = classes.reduce((sum, cls) => sum + cls.studentCount, 0);
  const emba1Classes = classes.filter(c => c.promotion === 'EMBA1');
  const emba2Classes = classes.filter(c => c.promotion === 'EMBA2');

  return (
    <div className="space-y-6">
      {/* En-tête avec statistiques */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              📚 Gestion des Classes
            </h2>
            <p className="text-gray-600 dark:text-gray-400">
              Vue d'ensemble de toutes les classes et leurs étudiants
            </p>
          </div>
        </div>

        {/* Statistiques globales */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
            <div className="flex items-center">
              <UserGroupIcon className="h-8 w-8 text-blue-600 dark:text-blue-400" />
              <div className="ml-3">
                <p className="text-sm font-medium text-blue-600 dark:text-blue-400">Total Classes</p>
                <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">{classes.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
            <div className="flex items-center">
              <AcademicCapIcon className="h-8 w-8 text-green-600 dark:text-green-400" />
              <div className="ml-3">
                <p className="text-sm font-medium text-green-600 dark:text-green-400">Total Étudiants</p>
                <p className="text-2xl font-bold text-green-900 dark:text-green-100">{totalStudents}</p>
              </div>
            </div>
          </div>

          <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
            <div className="flex items-center">
              <UserGroupIcon className="h-8 w-8 text-purple-600 dark:text-purple-400" />
              <div className="ml-3">
                <p className="text-sm font-medium text-purple-600 dark:text-purple-400">Classes EMBA1</p>
                <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">{emba1Classes.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4">
            <div className="flex items-center">
              <UserGroupIcon className="h-8 w-8 text-orange-600 dark:text-orange-400" />
              <div className="ml-3">
                <p className="text-sm font-medium text-orange-600 dark:text-orange-400">Classes EMBA2</p>
                <p className="text-2xl font-bold text-orange-900 dark:text-orange-100">{emba2Classes.length}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Liste des classes */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Classes ({filteredClasses.length})
              </h3>
              <select
                value={promotionFilter}
                onChange={(e) => setPromotionFilter(e.target.value)}
                className="text-sm border border-gray-300 dark:border-gray-600 rounded-md px-3 py-1 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="all">Toutes</option>
                <option value="EMBA1">EMBA1</option>
                <option value="EMBA2">EMBA2</option>
              </select>
            </div>
          </div>

          <div className="max-h-96 overflow-y-auto">
            {filteredClasses.map((classInfo) => (
              <button
                key={classInfo.name}
                onClick={() => handleClassSelect(classInfo.name)}
                className={cn(
                  "w-full text-left p-4 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",
                  selectedClass === classInfo.name && "bg-primary-50 dark:bg-primary-900/20 border-primary-200 dark:border-primary-800"
                )}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">
                      {classInfo.name}
                    </h4>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {classInfo.promotion}
                    </p>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {classInfo.studentCount}/{classInfo.capacity}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      étudiants
                    </div>
                  </div>
                </div>
                
                {/* Barre de progression */}
                <div className="mt-2">
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      className={cn(
                        "h-2 rounded-full transition-all",
                        classInfo.studentCount >= classInfo.capacity 
                          ? "bg-red-500" 
                          : classInfo.studentCount >= classInfo.capacity * 0.8 
                          ? "bg-yellow-500" 
                          : "bg-green-500"
                      )}
                      style={{ width: `${Math.min(100, (classInfo.studentCount / classInfo.capacity) * 100)}%` }}
                    ></div>
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Détails de la classe sélectionnée */}
        <div className="lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg shadow">
          {selectedClass ? (
            <>
              <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                      Classe {selectedClass}
                    </h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {filteredStudents.length} étudiant(s)
                    </p>
                  </div>
                </div>

                {/* Barre de recherche */}
                <div className="relative">
                  <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Rechercher un étudiant..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                  />
                </div>
              </div>

              <div className="max-h-96 overflow-y-auto">
                {filteredStudents.length > 0 ? (
                  <div className="divide-y divide-gray-200 dark:divide-gray-700">
                    {filteredStudents.map((student) => (
                      <div key={student._id} className="p-4 hover:bg-gray-50 dark:hover:bg-gray-700">
                        <div className="flex items-center space-x-3">
                          <div className="flex-shrink-0">
                            {student.user.profilePicture ? (
                              <img
                                src={student.user.profilePicture}
                                alt={`${student.user.firstName} ${student.user.lastName}`}
                                className="h-10 w-10 rounded-full"
                              />
                            ) : (
                              <div className="h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                                <UserIcon className="h-6 w-6 text-gray-500 dark:text-gray-400" />
                              </div>
                            )}
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                                {student.user.firstName} {student.user.lastName}
                              </p>
                              <span className={cn(
                                "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",
                                student.enrollmentStatus === 'active' 
                                  ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                                  : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
                              )}>
                                {student.enrollmentStatus === 'active' ? 'Actif' : student.enrollmentStatus}
                              </span>
                            </div>
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                              {student.studentNumber}
                            </p>
                            <div className="flex items-center space-x-4 mt-1">
                              <div className="flex items-center space-x-1">
                                <EnvelopeIcon className="h-3 w-3 text-gray-400" />
                                <span className="text-xs text-gray-500 dark:text-gray-400 truncate">
                                  {student.user.email}
                                </span>
                              </div>
                              {student.user.phone && (
                                <div className="flex items-center space-x-1">
                                  <PhoneIcon className="h-3 w-3 text-gray-400" />
                                  <span className="text-xs text-gray-500 dark:text-gray-400">
                                    {student.user.phone}
                                  </span>
                                </div>
                              )}
                            </div>
                            <div className="flex items-center space-x-2 mt-2">
                              <span className="text-xs text-gray-500 dark:text-gray-400">
                                Programme: {student.program}
                              </span>
                              <span className="text-xs text-gray-500 dark:text-gray-400">
                                • Cohorte: {student.cohort}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="p-8 text-center">
                    <UserGroupIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500 dark:text-gray-400">
                      {searchTerm ? 'Aucun étudiant trouvé pour cette recherche' : 'Aucun étudiant dans cette classe'}
                    </p>
                  </div>
                )}
              </div>
            </>
          ) : (
            <div className="p-8 text-center">
              <UserGroupIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 dark:text-gray-400">
                Sélectionnez une classe pour voir ses étudiants
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdminClassesView;
