import React, { useState, useEffect } from 'react';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  BookOpenIcon,
  AcademicCapIcon,
  UserGroupIcon,
  ClockIcon,
  DocumentTextIcon,
  FolderIcon
} from '@heroicons/react/24/outline';
import CourseFormModal from './CourseFormModal';
import NewCourseAssignmentModal from './NewCourseAssignmentModal';
import CourseAssignmentsModal from './CourseAssignmentsModal';

const AdminCourses = () => {
  const [courses, setCourses] = useState([]);
  const [professors, setProfessors] = useState([]);
  const [students, setStudents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSemester, setSelectedSemester] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [selectedCourse, setSelectedCourse] = useState(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [editingCourse, setEditingCourse] = useState(null);
  const [showCourseModal, setShowCourseModal] = useState(false);
  const [assigningCourse, setAssigningCourse] = useState(null);
  const [showAssignmentModal, setShowAssignmentModal] = useState(false);
  const [showAssignmentsModal, setShowAssignmentsModal] = useState(false);
  const [viewingCourse, setViewingCourse] = useState(null);
  const [showCourseDetails, setShowCourseDetails] = useState(false);
  const [selectedCourseForDetails, setSelectedCourseForDetails] = useState(null);
  const [courseDocuments, setCourseDocuments] = useState([]);
  const [courseSyllabus, setCourseSyllabus] = useState(null);

  // Charger les cours depuis l'API
  const fetchCourses = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/courses', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();

        setCourses(data.courses || []);
      } else {
        console.error('Erreur lors du chargement des cours');
        setCourses([]);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des cours:', error);
      setCourses([]);
    } finally {
      setLoading(false);
    }
  };

  // Charger les professeurs pour les assignations
  const fetchProfessors = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/professors', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setProfessors(data.professors || []);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des professeurs:', error);
      setProfessors([]);
    }
  };

  // Charger les étudiants pour les inscriptions
  const fetchStudents = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/students', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setStudents(data.students || []);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des étudiants:', error);
      setStudents([]);
    }
  };

  // Fonction pour récupérer les détails d'un cours (documents et syllabus)
  const fetchCourseDetails = async (courseId) => {
    try {
      // Récupérer les documents
      const documentsResponse = await fetch(`http://localhost:5000/api/courses/${courseId}/documents`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (documentsResponse.ok) {
        const documentsData = await documentsResponse.json();
        setCourseDocuments(documentsData.documents || []);
      } else {
        setCourseDocuments([]);
      }

      // Récupérer le syllabus
      const syllabusResponse = await fetch(`http://localhost:5000/api/courses/${courseId}/syllabus`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (syllabusResponse.ok) {
        const syllabusData = await syllabusResponse.json();
        setCourseSyllabus(syllabusData.syllabus);
      } else {
        setCourseSyllabus(null);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des détails du cours:', error);
      setCourseDocuments([]);
      setCourseSyllabus(null);
    }
  };

  // Fonction pour ouvrir les détails d'un cours
  const handleViewCourseDetails = async (course) => {
    setSelectedCourseForDetails(course);
    setShowCourseDetails(true);
    await fetchCourseDetails(course._id);
  };

  // Fonctions de gestion des cours
  const handleCreateCourse = () => {
    setEditingCourse(null);
    setShowCourseModal(true);
  };

  const handleEditCourse = (course) => {
    setEditingCourse(course);
    setShowCourseModal(true);
  };

  const handleAssignCourse = (course) => {
    setAssigningCourse(course);
    setShowAssignmentModal(true);
  };

  const handleViewAssignments = (course) => {
    setViewingCourse(course);
    setShowAssignmentsModal(true);
  };

  const handleSubmitAssignment = async (assignmentData) => {
    try {
      setLoading(true);
      console.log('📝 Données d\'affectation:', assignmentData);

      const response = await fetch(`http://localhost:5000/api/courses/${assigningCourse._id}/assign`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(assignmentData)
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Affectation créée avec succès:', result);

        // Recharger les cours
        await fetchCourses();
        setShowAssignmentModal(false);
        setAssigningCourse(null);
        alert('Affectation créée avec succès ! Le planning a été généré automatiquement.');
      } else {
        const error = await response.json();
        console.error('❌ Erreur lors de l\'affectation:', error);

        if (response.status === 409 && error.conflicts) {
          // Gérer les conflits d'horaires
          throw { conflicts: error.conflicts };
        } else {
          alert('Erreur lors de l\'affectation: ' + error.message);
        }
      }
    } catch (error) {
      console.error('❌ Erreur:', error);
      if (error.conflicts) {
        throw error; // Relancer pour que le modal puisse afficher les conflits
      } else {
        alert('Erreur lors de l\'affectation du cours');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitCourse = async (courseData) => {
    try {
      setLoading(true);
      const url = editingCourse
        ? `http://localhost:5000/api/courses/${editingCourse._id}`
        : 'http://localhost:5000/api/courses';

      const method = editingCourse ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(courseData)
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Cours sauvegardé:', result);

        // Afficher un message approprié selon le type de réponse
        if (!editingCourse && result.courses && result.courses.length > 0) {
          // Nouveau cours créé pour plusieurs classes
          alert(`✅ ${result.count} cours créé(s) avec succès pour toutes les classes de ${courseData.promotion}!`);
        } else if (!editingCourse && result.course) {
          // Ancien format de réponse
          alert('✅ Cours créé avec succès!');
        } else {
          // Cours modifié
          alert('✅ Cours modifié avec succès!');
        }

        // Recharger les cours
        await fetchCourses();
        setShowCourseModal(false);
        setEditingCourse(null);
      } else {
        const error = await response.json();
        console.error('❌ Erreur lors de la sauvegarde:', error);
        alert(`❌ Erreur: ${error.message || 'Erreur lors de la sauvegarde du cours'}`);
      }
    } catch (error) {
      console.error('❌ Erreur:', error);
      alert('Erreur lors de la sauvegarde du cours');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteCourse = async (courseId) => {
    if (!window.confirm('Êtes-vous sûr de vouloir supprimer ce cours ?')) {
      return;
    }

    try {
      const response = await fetch(`http://localhost:5000/api/courses/${courseId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        console.log('✅ Cours supprimé');
        await fetchCourses();
      } else {
        console.error('❌ Erreur lors de la suppression');
        alert('Erreur lors de la suppression du cours');
      }
    } catch (error) {
      console.error('❌ Erreur:', error);
      alert('Erreur lors de la suppression du cours');
    }
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await Promise.all([
        fetchCourses(),
        fetchProfessors(),
        fetchStudents()
      ]);
    };

    loadData();
  }, [selectedSemester, selectedStatus]);

  const getStatusBadge = (status) => {
    const badges = {
      active: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      completed: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
      upcoming: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
      cancelled: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
    };
    
    const labels = {
      active: 'Actif',
      completed: 'Terminé',
      upcoming: 'À venir',
      cancelled: 'Annulé'
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${badges[status]}`}>
        {labels[status]}
      </span>
    );
  };



  const filteredCourses = courses.filter(course => {
    const matchesSearch = !searchTerm ||
      course.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      course.courseCode?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (course.instructor?.user?.firstName + ' ' + course.instructor?.user?.lastName)?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesSemester = !selectedSemester || course.semester === selectedSemester;
    const matchesStatus = !selectedStatus || course.status === selectedStatus;

    return matchesSearch && matchesSemester && matchesStatus;
  });

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* En-tête avec statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <div className="flex items-center">
            <BookOpenIcon className="h-8 w-8 text-blue-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Cours</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">{courses.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <div className="flex items-center">
            <AcademicCapIcon className="h-8 w-8 text-green-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Cours Actifs</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {courses.filter(c => c.status === 'active').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <div className="flex items-center">
            <UserGroupIcon className="h-8 w-8 text-purple-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Étudiants Inscrits</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {courses.reduce((total, course) => total + course.enrolledStudents, 0)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <div className="flex items-center">
            <ClockIcon className="h-8 w-8 text-orange-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Heures Totales</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {courses.reduce((total, course) => total + course.hours, 0)}h
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Barre d'outils */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Gestion des Cours
          </h1>
          
          <button
            onClick={handleCreateCourse}
            className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
          >
            <PlusIcon className="h-5 w-5 mr-2" />
            Nouveau Cours
          </button>
        </div>

        {/* Filtres */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Rechercher un cours..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            />
          </div>

          <select
            value={selectedSemester}
            onChange={(e) => setSelectedSemester(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="">Tous les semestres</option>
            <option value="Fall 2024">Automne 2024</option>
            <option value="Spring 2025">Printemps 2025</option>
          </select>

          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="">Tous les statuts</option>
            <option value="active">Actif</option>
            <option value="completed">Terminé</option>
            <option value="upcoming">À venir</option>
            <option value="cancelled">Annulé</option>
          </select>

          <button
            onClick={() => {
              setSearchTerm('');
              setSelectedSemester('');
              setSelectedStatus('');
            }}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            Réinitialiser
          </button>
        </div>
      </div>

      {/* Liste des cours */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Cours
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Professeur
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Classes
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Étudiants
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Statut
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Semestre
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {filteredCourses.map((course) => (
                <tr key={course._id || course.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {course.title}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {course.courseCode} • {course.creditHours} crédits • {course.contactHours}h
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {course.assignments > 0 ? (
                      <div>
                        <div className="text-sm text-gray-900 dark:text-white">
                          {course.professor ? course.professor.name :
                           course.allInstructors?.[0]?.name || 'Professeur non défini'}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {course.assignments} affectation{course.assignments > 1 ? 's' : ''}
                          {course.allInstructors?.length > 1 && ` • ${course.allInstructors.length} professeurs`}
                        </div>
                      </div>
                    ) : (
                      <span className="text-sm text-gray-500 dark:text-gray-400 italic">
                        Non assigné
                      </span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {course.assignments > 0 ? (
                      <div className="flex flex-wrap gap-1">
                        {course.class ? (
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            course.promotion === 'EMBA1'
                              ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                              : 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
                          }`}>
                            {course.class}
                          </span>
                        ) : null}
                        {course.assignments > 1 && (
                          <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300">
                            +{course.assignments - 1} autres
                          </span>
                        )}
                      </div>
                    ) : (
                      <span className="text-sm text-gray-500 dark:text-gray-400 italic">
                        {course.promotion} - Non assigné
                      </span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-white">
                      {course.capacity?.current || 0}/{course.capacity?.maximum || 0}
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-1">
                      <div
                        className="bg-primary-600 h-2 rounded-full"
                        style={{
                          width: `${course.capacity?.maximum ?
                            ((course.capacity.current || 0) / course.capacity.maximum) * 100 : 0}%`
                        }}
                      ></div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      course.status === 'active'
                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                        : course.status === 'draft'
                        ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                        : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
                    }`}>
                      {course.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {course.semester}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex space-x-2 justify-end">
                      {/* Bouton pour voir les documents et syllabus */}
                      <button
                        onClick={() => handleViewCourseDetails(course)}
                        className="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300"
                        title="Voir documents et syllabus"
                      >
                        <DocumentTextIcon className="h-4 w-4" />
                      </button>

                      {/* Bouton pour voir les affectations existantes */}
                      {course.assignments > 0 && (
                        <button
                          onClick={() => handleViewAssignments(course)}
                          className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                          title="Voir toutes les affectations"
                        >
                          <EyeIcon className="h-4 w-4" />
                        </button>
                      )}

                      {/* Bouton d'affectation - toujours disponible pour ajouter des affectations */}
                      <button
                        onClick={() => handleAssignCourse(course)}
                        className="text-purple-600 hover:text-purple-900 dark:text-purple-400 dark:hover:text-purple-300"
                        title={course.assignments > 0 ? "Ajouter une nouvelle affectation" : "Affecter professeur et classe"}
                      >
                        <UserGroupIcon className="h-4 w-4" />
                      </button>

                      <button
                        onClick={() => handleEditCourse(course)}
                        className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                        title="Modifier"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteCourse(course._id)}
                        className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                        title="Supprimer"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredCourses.length === 0 && (
          <div className="text-center py-12">
            <BookOpenIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">Aucun cours</h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              {searchTerm || selectedSemester || selectedStatus 
                ? 'Aucun cours ne correspond aux critères de recherche.'
                : 'Commencez par ajouter un cours.'
              }
            </p>
          </div>
        )}
      </div>

      {/* Modal de création/modification de cours */}
      <CourseFormModal
        isOpen={showCourseModal}
        onClose={() => {
          setShowCourseModal(false);
          setEditingCourse(null);
        }}
        onSubmit={handleSubmitCourse}
        course={editingCourse}
        professors={professors}
        isLoading={loading}
      />

      {/* Modal d'affectation de cours */}
      <NewCourseAssignmentModal
        isOpen={showAssignmentModal}
        onClose={() => {
          setShowAssignmentModal(false);
          setAssigningCourse(null);
        }}
        onSubmit={handleSubmitAssignment}
        course={assigningCourse}
        professors={professors}
        isLoading={loading}
      />

      {/* Modal pour voir toutes les affectations d'un cours */}
      <CourseAssignmentsModal
        isOpen={showAssignmentsModal}
        onClose={() => {
          setShowAssignmentsModal(false);
          setViewingCourse(null);
        }}
        course={viewingCourse}
      />

      {/* Modal pour voir les détails du cours (documents et syllabus) */}
      {showCourseDetails && selectedCourseForDetails && (
        <CourseDetailsModal
          isOpen={showCourseDetails}
          onClose={() => {
            setShowCourseDetails(false);
            setSelectedCourseForDetails(null);
            setCourseDocuments([]);
            setCourseSyllabus(null);
          }}
          course={selectedCourseForDetails}
          documents={courseDocuments}
          syllabus={courseSyllabus}
        />
      )}
    </div>
  );
};

// Composant Modal pour afficher les détails du cours
const CourseDetailsModal = ({ isOpen, onClose, course, documents, syllabus }) => {
  if (!isOpen) return null;

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (mimeType) => {
    if (mimeType?.includes('pdf')) return '📄';
    if (mimeType?.includes('word') || mimeType?.includes('document')) return '📝';
    if (mimeType?.includes('powerpoint') || mimeType?.includes('presentation')) return '📊';
    if (mimeType?.includes('image')) return '🖼️';
    return '📎';
  };

  const getDocumentTypeLabel = (type) => {
    const types = {
      'course_material': 'Matériel de cours',
      'syllabus': 'Syllabus',
      'assignment': 'Devoir',
      'exam': 'Examen',
      'reference': 'Référence',
      'other': 'Autre'
    };
    return types[type] || type;
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white dark:bg-gray-800">
        {/* Header */}
        <div className="flex justify-between items-center pb-4 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Détails du Cours
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {course.title} ({course.courseCode}) - {course.class}
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            ✕
          </button>
        </div>

        <div className="mt-4 space-y-6">
          {/* Section Syllabus */}
          <div>
            <h4 className="text-md font-medium text-gray-900 dark:text-white mb-3 flex items-center">
              <DocumentTextIcon className="h-5 w-5 mr-2" />
              Syllabus
            </h4>
            {syllabus ? (
              <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-green-800 dark:text-green-200">
                      ✅ Syllabus disponible
                    </p>
                    <p className="text-xs text-green-600 dark:text-green-400">
                      Créé le {new Date(syllabus.createdAt).toLocaleDateString('fr-FR')}
                    </p>
                  </div>
                  <div className="text-sm text-green-700 dark:text-green-300">
                    {syllabus.numberOfHours}h • {syllabus.numberOfCredits} crédits
                  </div>
                </div>
              </div>
            ) : (
              <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                <p className="text-sm text-yellow-800 dark:text-yellow-200">
                  ⚠️ Aucun syllabus publié pour ce cours
                </p>
              </div>
            )}
          </div>

          {/* Section Documents */}
          <div>
            <h4 className="text-md font-medium text-gray-900 dark:text-white mb-3 flex items-center">
              <FolderIcon className="h-5 w-5 mr-2" />
              Documents ({documents.length})
            </h4>
            {documents.length > 0 ? (
              <div className="space-y-3">
                {documents.map((document) => (
                  <div key={document._id} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="text-2xl">
                          {getFileIcon(document.mimeType)}
                        </div>
                        <div>
                          <h5 className="text-sm font-medium text-gray-900 dark:text-white">
                            {document.title}
                          </h5>
                          <div className="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                            <span>{document.originalName}</span>
                            <span>•</span>
                            <span>{formatFileSize(document.fileSize)}</span>
                            <span>•</span>
                            <span>{getDocumentTypeLabel(document.documentType)}</span>
                          </div>
                          {document.description && (
                            <p className="text-xs text-gray-600 dark:text-gray-300 mt-1">
                              {document.description}
                            </p>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {document.downloadCount || 0} téléchargements
                        </span>
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          document.visibleToStudents
                            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                            : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                        }`}>
                          {document.visibleToStudents ? 'Public' : 'Privé'}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <p className="text-sm text-gray-500 dark:text-gray-400 text-center">
                  📁 Aucun document ajouté pour ce cours
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={onClose}
            className="w-full px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600"
          >
            Fermer
          </button>
        </div>
      </div>
    </div>
  );
};

export default AdminCourses;
