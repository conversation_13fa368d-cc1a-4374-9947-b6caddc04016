import React, { useState, useEffect } from 'react';
import {
  ChartBarIcon,
  AcademicCapIcon,
  DocumentTextIcon,
  EyeIcon,
  PencilIcon,
  ArrowDownTrayIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  TrophyIcon
} from '@heroicons/react/24/outline';

const AdminGrades = () => {
  const [grades, setGrades] = useState([]);
  const [courses, setCourses] = useState([]);
  const [students, setStudents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCourse, setSelectedCourse] = useState('');
  const [selectedSemester, setSelectedSemester] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');

  // Charger les cours depuis l'API
  const fetchCourses = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/courses', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setCourses(data.courses || []);
      } else {
        console.error('Erreur lors du chargement des cours');
        setCourses([]);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des cours:', error);
      setCourses([]);
    }
  };

  // Charger les notes depuis l'API
  const fetchGrades = async () => {
    try {
      let url = 'http://localhost:5000/api/grades';
      const params = new URLSearchParams();

      if (selectedCourse) params.append('courseId', selectedCourse);
      if (selectedSemester) params.append('semester', selectedSemester);
      if (selectedStatus) params.append('status', selectedStatus);

      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setGrades(data.grades || []);
      } else {
        console.error('Erreur lors du chargement des notes');
        setGrades([]);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des notes:', error);
      setGrades([]);
    } finally {
      setLoading(false);
    }
  };

  // Charger les étudiants depuis l'API
  const fetchStudents = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/students', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setStudents(data.students || []);
      } else {
        console.error('Erreur lors du chargement des étudiants');
        setStudents([]);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des étudiants:', error);
      setStudents([]);
    }
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await Promise.all([
        fetchCourses(),
        fetchGrades(),
        fetchStudents()
      ]);
    };

    loadData();
  }, [selectedCourse, selectedSemester, selectedStatus]);

  const getStatusBadge = (status) => {
    const badges = {
      excellent: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      passed: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
      failed: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
      pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
    };
    
    const labels = {
      excellent: 'Excellent',
      passed: 'Réussi',
      failed: 'Échec',
      pending: 'En attente'
    };

    const icons = {
      excellent: TrophyIcon,
      passed: CheckCircleIcon,
      failed: XCircleIcon,
      pending: ExclamationTriangleIcon
    };

    const Icon = icons[status];

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${badges[status]}`}>
        <Icon className="h-3 w-3 mr-1" />
        {labels[status]}
      </span>
    );
  };

  const getGradeColor = (grade) => {
    if (grade >= 16) return 'text-green-600 dark:text-green-400';
    if (grade >= 12) return 'text-blue-600 dark:text-blue-400';
    if (grade >= 10) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  const calculateCourseAverage = (courseId) => {
    const courseGrades = grades.filter(g => g.course.id === courseId);
    if (courseGrades.length === 0) return 0;
    
    const sum = courseGrades.reduce((acc, grade) => acc + grade.finalGrade, 0);
    return (sum / courseGrades.length).toFixed(2);
  };

  const getPassRate = (courseId) => {
    const courseGrades = grades.filter(g => g.course.id === courseId);
    if (courseGrades.length === 0) return 0;
    
    const passedCount = courseGrades.filter(g => g.status === 'passed' || g.status === 'excellent').length;
    return ((passedCount / courseGrades.length) * 100).toFixed(1);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('fr-FR');
  };

  const filteredGrades = grades.filter(grade => {
    const matchesSearch = grade.student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         grade.student.studentId.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         grade.course.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCourse = !selectedCourse || grade.course.id.toString() === selectedCourse;
    const matchesSemester = !selectedSemester || grade.semester === selectedSemester;
    const matchesStatus = !selectedStatus || grade.status === selectedStatus;
    
    return matchesSearch && matchesCourse && matchesSemester && matchesStatus;
  });

  // Statistiques
  const stats = {
    totalGrades: grades.length,
    averageGrade: grades.length > 0 ? (grades.reduce((sum, g) => sum + g.finalGrade, 0) / grades.length).toFixed(2) : 0,
    passRate: grades.length > 0 ? ((grades.filter(g => g.status === 'passed' || g.status === 'excellent').length / grades.length) * 100).toFixed(1) : 0,
    excellentCount: grades.filter(g => g.status === 'excellent').length
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <div className="flex items-center">
            <ChartBarIcon className="h-8 w-8 text-blue-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Notes</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">{stats.totalGrades}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <div className="flex items-center">
            <AcademicCapIcon className="h-8 w-8 text-green-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Moyenne Générale</p>
              <p className={`text-2xl font-semibold ${getGradeColor(stats.averageGrade)}`}>
                {stats.averageGrade}/20
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <div className="flex items-center">
            <CheckCircleIcon className="h-8 w-8 text-purple-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Taux de Réussite</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">{stats.passRate}%</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <div className="flex items-center">
            <TrophyIcon className="h-8 w-8 text-yellow-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Excellents</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">{stats.excellentCount}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Statistiques par cours */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Statistiques par Cours</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {courses.map((course) => (
            <div key={course.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <h3 className="font-medium text-gray-900 dark:text-white">{course.name}</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">{course.code}</p>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Moyenne:</span>
                  <span className={`text-sm font-medium ${getGradeColor(calculateCourseAverage(course.id))}`}>
                    {calculateCourseAverage(course.id)}/20
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Réussite:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {getPassRate(course.id)}%
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Étudiants:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {grades.filter(g => g.course.id === course.id).length}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Barre d'outils */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Gestion des Notes
          </h1>
          
          <div className="flex space-x-3">
            <button className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
              <ArrowDownTrayIcon className="h-5 w-5 mr-2" />
              Exporter Bulletins
            </button>
            <button className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors">
              <DocumentTextIcon className="h-5 w-5 mr-2" />
              Nouvelle Évaluation
            </button>
          </div>
        </div>

        {/* Filtres */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Rechercher un étudiant..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            />
          </div>

          <select
            value={selectedCourse}
            onChange={(e) => setSelectedCourse(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="">Tous les cours</option>
            {courses.map(course => (
              <option key={course.id} value={course.id}>{course.name}</option>
            ))}
          </select>

          <select
            value={selectedSemester}
            onChange={(e) => setSelectedSemester(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="">Tous les semestres</option>
            <option value="Fall 2024">Automne 2024</option>
            <option value="Spring 2025">Printemps 2025</option>
          </select>

          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="">Tous les statuts</option>
            <option value="excellent">Excellent</option>
            <option value="passed">Réussi</option>
            <option value="failed">Échec</option>
            <option value="pending">En attente</option>
          </select>
        </div>
      </div>

      {/* Liste des notes */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Étudiant
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Cours
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Évaluations
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Note Finale
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Statut
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Dernière MAJ
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {filteredGrades.map((grade) => (
                <tr key={grade.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {grade.student.name}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {grade.student.studentId}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {grade.course.name}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {grade.course.code}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="space-y-1">
                      {grade.evaluations.slice(0, 2).map((evaluation, index) => (
                        <div key={index} className="text-xs text-gray-600 dark:text-gray-400">
                          {evaluation.type}: <span className={getGradeColor(evaluation.grade)}>{evaluation.grade}/{evaluation.maxGrade}</span>
                        </div>
                      ))}
                      {grade.evaluations.length > 2 && (
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          +{grade.evaluations.length - 2} autres...
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className={`text-lg font-semibold ${getGradeColor(grade.finalGrade)}`}>
                      {grade.finalGrade}/20
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(grade.status)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {formatDate(grade.lastUpdated)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex space-x-2 justify-end">
                      <button className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
                        <EyeIcon className="h-4 w-4" />
                      </button>
                      <button className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300">
                        <PencilIcon className="h-4 w-4" />
                      </button>
                      <button className="text-purple-600 hover:text-purple-900 dark:text-purple-400 dark:hover:text-purple-300">
                        <ArrowDownTrayIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredGrades.length === 0 && (
          <div className="text-center py-12">
            <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">Aucune note</h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              {searchTerm || selectedCourse || selectedSemester || selectedStatus 
                ? 'Aucune note ne correspond aux critères de recherche.'
                : 'Aucune note enregistrée.'
              }
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminGrades;
