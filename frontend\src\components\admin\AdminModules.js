import React, { useState, useEffect } from 'react';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  BookOpenIcon,
  AcademicCapIcon,
  UserGroupIcon,
  ClockIcon,
  CogIcon
} from '@heroicons/react/24/outline';
import ModuleFormModal from './ModuleFormModal';

const AdminModules = () => {
  const [modules, setModules] = useState([]);
  const [professors, setProfessors] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPromotion, setSelectedPromotion] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [showModuleModal, setShowModuleModal] = useState(false);
  const [editingModule, setEditingModule] = useState(null);
  const [selectedModule, setSelectedModule] = useState(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);

  // Charger les modules depuis l'API
  const fetchModules = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/modules', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setModules(data.modules || []);
      } else {
        console.error('Erreur lors du chargement des modules');
        setModules([]);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des modules:', error);
      setModules([]);
    } finally {
      setLoading(false);
    }
  };

  // Charger les professeurs
  const fetchProfessors = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/professors', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setProfessors(data.professors || []);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des professeurs:', error);
      setProfessors([]);
    }
  };

  // Fonctions de gestion des modules
  const handleCreateModule = () => {
    setEditingModule(null);
    setShowModuleModal(true);
  };

  const handleEditModule = (module) => {
    setEditingModule(module);
    setShowModuleModal(true);
  };

  const handleSubmitModule = async (moduleData) => {
    try {
      setLoading(true);
      const url = editingModule 
        ? `http://localhost:5000/api/modules/${editingModule._id}`
        : 'http://localhost:5000/api/modules';
      
      const method = editingModule ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(moduleData)
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Module sauvegardé:', result);
        
        // Recharger les modules
        await fetchModules();
        setShowModuleModal(false);
        setEditingModule(null);
      } else {
        const error = await response.json();
        console.error('❌ Erreur lors de la sauvegarde:', error);
        alert('Erreur lors de la sauvegarde du module');
      }
    } catch (error) {
      console.error('❌ Erreur:', error);
      alert('Erreur lors de la sauvegarde du module');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteModule = async (moduleId) => {
    if (!window.confirm('Êtes-vous sûr de vouloir supprimer ce module ?')) {
      return;
    }

    try {
      const response = await fetch(`http://localhost:5000/api/modules/${moduleId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        console.log('✅ Module supprimé');
        await fetchModules();
      } else {
        console.error('❌ Erreur lors de la suppression');
        alert('Erreur lors de la suppression du module');
      }
    } catch (error) {
      console.error('❌ Erreur:', error);
      alert('Erreur lors de la suppression du module');
    }
  };

  // Générer automatiquement les cours pour un module
  const handleGenerateCourses = async (module) => {
    if (!window.confirm(`Générer automatiquement les cours pour le module "${module.title}" pour toutes les classes ${module.promotion} ?`)) {
      return;
    }

    try {
      setLoading(true);
      const response = await fetch('http://localhost:5000/api/courses/bulk-create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          moduleId: module._id,
          promotion: module.promotion
        })
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Cours générés:', result);
        alert(`${result.courses?.length || 0} cours ont été créés avec succès !`);
      } else {
        const error = await response.json();
        console.error('❌ Erreur lors de la génération:', error);
        alert('Erreur lors de la génération des cours');
      }
    } catch (error) {
      console.error('❌ Erreur:', error);
      alert('Erreur lors de la génération des cours');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await Promise.all([
        fetchModules(),
        fetchProfessors()
      ]);
    };

    loadData();
  }, [selectedPromotion, selectedStatus]);

  // Filtrer les modules
  const filteredModules = modules.filter(module => {
    const matchesSearch = !searchTerm || 
      module.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      module.moduleCode?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      module.description?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesPromotion = !selectedPromotion || module.promotion === selectedPromotion;
    const matchesStatus = !selectedStatus || module.status === selectedStatus;
    
    return matchesSearch && matchesPromotion && matchesStatus;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* En-tête avec statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <div className="flex items-center">
            <BookOpenIcon className="h-8 w-8 text-blue-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Modules</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">{modules.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <div className="flex items-center">
            <AcademicCapIcon className="h-8 w-8 text-green-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Modules Actifs</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {modules.filter(m => m.status === 'active').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <div className="flex items-center">
            <UserGroupIcon className="h-8 w-8 text-purple-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">EMBA1</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {modules.filter(m => m.promotion === 'EMBA1').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <div className="flex items-center">
            <ClockIcon className="h-8 w-8 text-orange-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">EMBA2</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {modules.filter(m => m.promotion === 'EMBA2').length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Barre d'outils */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Gestion des Modules
          </h1>
          
          <button
            onClick={handleCreateModule}
            className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
          >
            <PlusIcon className="h-5 w-5 mr-2" />
            Nouveau Module
          </button>
        </div>

        {/* Filtres */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Rechercher un module..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 w-full border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            />
          </div>

          <select
            value={selectedPromotion}
            onChange={(e) => setSelectedPromotion(e.target.value)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
          >
            <option value="">Toutes les promotions</option>
            <option value="EMBA1">EMBA1</option>
            <option value="EMBA2">EMBA2</option>
          </select>

          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
          >
            <option value="">Tous les statuts</option>
            <option value="draft">Brouillon</option>
            <option value="approved">Approuvé</option>
            <option value="active">Actif</option>
            <option value="completed">Terminé</option>
            <option value="cancelled">Annulé</option>
          </select>

          <div className="flex items-center space-x-2">
            <FunnelIcon className="h-5 w-5 text-gray-400" />
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {filteredModules.length} module(s)
            </span>
          </div>
        </div>
      </div>

      {/* Liste des modules */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Module
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Coordinateur
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Promotion
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Statut
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Crédits
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {filteredModules.map((module) => (
                <tr key={module._id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {module.title}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {module.moduleCode} • {module.category}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-white">
                      {module.coordinator?.user ? 
                        `${module.coordinator.user.firstName} ${module.coordinator.user.lastName}` : 
                        'Non assigné'
                      }
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      module.promotion === 'EMBA1' 
                        ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                        : 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
                    }`}>
                      {module.promotion}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      module.status === 'active' 
                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                        : module.status === 'draft'
                        ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                        : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
                    }`}>
                      {module.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {module.creditHours}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end space-x-2">
                      <button 
                        onClick={() => {
                          setSelectedModule(module);
                          setShowDetailsModal(true);
                        }}
                        className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                        title="Voir les détails"
                      >
                        <EyeIcon className="h-4 w-4" />
                      </button>
                      <button 
                        onClick={() => handleEditModule(module)}
                        className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                        title="Modifier"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>
                      <button 
                        onClick={() => handleGenerateCourses(module)}
                        className="text-purple-600 hover:text-purple-900 dark:text-purple-400 dark:hover:text-purple-300"
                        title="Générer les cours"
                      >
                        <CogIcon className="h-4 w-4" />
                      </button>
                      <button 
                        onClick={() => handleDeleteModule(module._id)}
                        className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                        title="Supprimer"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredModules.length === 0 && (
          <div className="text-center py-12">
            <BookOpenIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">Aucun module</h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              {searchTerm || selectedPromotion || selectedStatus 
                ? 'Aucun module ne correspond aux critères de recherche.'
                : 'Commencez par ajouter un module.'
              }
            </p>
          </div>
        )}
      </div>

      {/* Modal de création/modification de module */}
      <ModuleFormModal
        isOpen={showModuleModal}
        onClose={() => {
          setShowModuleModal(false);
          setEditingModule(null);
        }}
        onSubmit={handleSubmitModule}
        module={editingModule}
        professors={professors}
        isLoading={loading}
      />
    </div>
  );
};

export default AdminModules;
