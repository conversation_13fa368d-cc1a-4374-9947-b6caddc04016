import React, { useState, useEffect } from 'react';
import {
  CalendarDaysIcon,
  ClockIcon,
  MapPinIcon,
  UserGroupIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  AcademicCapIcon,
  BuildingOfficeIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

const AdminSchedule = () => {
  const [events, setEvents] = useState([]);
  const [rooms, setRooms] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [selectedRoom, setSelectedRoom] = useState('');
  const [selectedType, setSelectedType] = useState('');
  const [viewMode, setViewMode] = useState('day'); // day, week, month
  const [showAddModal, setShowAddModal] = useState(false);

  // Charger les salles depuis l'API
  const fetchRooms = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/rooms', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setRooms(data.rooms || []);
      } else {
        console.error('Erreur lors du chargement des salles');
        setRooms([]);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des salles:', error);
      setRooms([]);
    }
  };

  // Charger les événements depuis l'API
  const fetchEvents = async () => {
    try {
      let url = 'http://localhost:5000/api/schedule/events';
      const params = new URLSearchParams();

      if (selectedDate) params.append('date', selectedDate);
      if (selectedRoom) params.append('roomId', selectedRoom);
      if (selectedType) params.append('type', selectedType);

      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setEvents(data.events || []);
      } else {
        console.error('Erreur lors du chargement des événements');
        setEvents([]);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des événements:', error);
      setEvents([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await Promise.all([
        fetchRooms(),
        fetchEvents()
      ]);
    };

    loadData();
  }, [selectedDate, selectedRoom, selectedType]);

  const getEventTypeIcon = (type) => {
    const icons = {
      course: AcademicCapIcon,
      exam: ClockIcon,
      meeting: UserGroupIcon,
      maintenance: BuildingOfficeIcon
    };
    return icons[type] || CalendarDaysIcon;
  };

  const getEventTypeBadge = (type) => {
    const badges = {
      course: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
      exam: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
      meeting: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
      maintenance: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300'
    };
    
    const labels = {
      course: 'Cours',
      exam: 'Examen',
      meeting: 'Réunion',
      maintenance: 'Maintenance'
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${badges[type]}`}>
        {labels[type]}
      </span>
    );
  };

  const getStatusBadge = (status) => {
    const badges = {
      confirmed: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      tentative: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
      cancelled: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
    };
    
    const labels = {
      confirmed: 'Confirmé',
      tentative: 'Provisoire',
      cancelled: 'Annulé'
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${badges[status]}`}>
        {labels[status]}
      </span>
    );
  };

  const getRoomTypeBadge = (type) => {
    const badges = {
      classroom: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
      amphitheater: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
      laboratory: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      meeting_room: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300'
    };
    
    const labels = {
      classroom: 'Salle de classe',
      amphitheater: 'Amphithéâtre',
      laboratory: 'Laboratoire',
      meeting_room: 'Salle de réunion'
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${badges[type]}`}>
        {labels[type]}
      </span>
    );
  };

  const formatTime = (time) => {
    return time;
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const checkConflicts = (event) => {
    return events.filter(e => 
      e.id !== event.id &&
      e.date === event.date &&
      e.room.id === event.room.id &&
      (
        (e.startTime <= event.startTime && e.endTime > event.startTime) ||
        (e.startTime < event.endTime && e.endTime >= event.endTime) ||
        (e.startTime >= event.startTime && e.endTime <= event.endTime)
      )
    );
  };

  const filteredEvents = events.filter(event => {
    const matchesDate = !selectedDate || event.date === selectedDate;
    const matchesRoom = !selectedRoom || event.room.id.toString() === selectedRoom;
    const matchesType = !selectedType || event.type === selectedType;
    
    return matchesDate && matchesRoom && matchesType;
  });

  // Statistiques
  const stats = {
    totalEvents: events.length,
    todayEvents: events.filter(e => e.date === new Date().toISOString().split('T')[0]).length,
    totalRooms: rooms.length,
    conflicts: events.filter(event => checkConflicts(event).length > 0).length
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <div className="flex items-center">
            <CalendarDaysIcon className="h-8 w-8 text-blue-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Événements</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">{stats.totalEvents}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <div className="flex items-center">
            <ClockIcon className="h-8 w-8 text-green-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Aujourd'hui</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">{stats.todayEvents}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <div className="flex items-center">
            <BuildingOfficeIcon className="h-8 w-8 text-purple-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Salles</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">{stats.totalRooms}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <div className="flex items-center">
            <ExclamationTriangleIcon className="h-8 w-8 text-red-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Conflits</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">{stats.conflicts}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Barre d'outils */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Planning & Calendrier
          </h1>
          
          <div className="flex space-x-3">
            <div className="flex rounded-lg border border-gray-300 dark:border-gray-600">
              <button
                onClick={() => setViewMode('day')}
                className={`px-3 py-2 text-sm font-medium rounded-l-lg ${
                  viewMode === 'day'
                    ? 'bg-primary-600 text-white'
                    : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600'
                }`}
              >
                Jour
              </button>
              <button
                onClick={() => setViewMode('week')}
                className={`px-3 py-2 text-sm font-medium border-l border-gray-300 dark:border-gray-600 ${
                  viewMode === 'week'
                    ? 'bg-primary-600 text-white'
                    : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600'
                }`}
              >
                Semaine
              </button>
              <button
                onClick={() => setViewMode('month')}
                className={`px-3 py-2 text-sm font-medium rounded-r-lg border-l border-gray-300 dark:border-gray-600 ${
                  viewMode === 'month'
                    ? 'bg-primary-600 text-white'
                    : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600'
                }`}
              >
                Mois
              </button>
            </div>
            
            <button
              onClick={() => setShowAddModal(true)}
              className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
            >
              <PlusIcon className="h-5 w-5 mr-2" />
              Nouvel Événement
            </button>
          </div>
        </div>

        {/* Filtres */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
          <input
            type="date"
            value={selectedDate}
            onChange={(e) => setSelectedDate(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />

          <select
            value={selectedRoom}
            onChange={(e) => setSelectedRoom(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="">Toutes les salles</option>
            {rooms.map(room => (
              <option key={room.id} value={room.id}>{room.name} ({room.capacity} places)</option>
            ))}
          </select>

          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="">Tous les types</option>
            <option value="course">Cours</option>
            <option value="exam">Examen</option>
            <option value="meeting">Réunion</option>
            <option value="maintenance">Maintenance</option>
          </select>

          <button
            onClick={() => {
              setSelectedDate(new Date().toISOString().split('T')[0]);
              setSelectedRoom('');
              setSelectedType('');
            }}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            Réinitialiser
          </button>
        </div>
      </div>

      {/* Liste des événements */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Événement
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Date & Heure
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Salle
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Professeur
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Participants
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Statut
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {filteredEvents.map((event) => {
                const EventIcon = getEventTypeIcon(event.type);
                const conflicts = checkConflicts(event);
                
                return (
                  <tr key={event.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <EventIcon className={`h-8 w-8 text-${event.color}-500 mr-3`} />
                        <div>
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {event.title}
                          </div>
                          <div className="flex items-center space-x-2 mt-1">
                            {getEventTypeBadge(event.type)}
                            {conflicts.length > 0 && (
                              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">
                                <ExclamationTriangleIcon className="h-3 w-3 mr-1" />
                                Conflit
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">
                        {formatDate(event.date)}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {formatTime(event.startTime)} - {formatTime(event.endTime)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {event.room.name}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {rooms.find(r => r.id === event.room.id)?.capacity} places
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {event.professor ? (
                        <div>
                          <div className="text-sm text-gray-900 dark:text-white">
                            {event.professor.name}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {event.professor.email}
                          </div>
                        </div>
                      ) : (
                        <span className="text-sm text-gray-500 dark:text-gray-400">-</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {event.students > 0 ? (
                        <div>
                          <div className="text-sm text-gray-900 dark:text-white">
                            {event.students}/{event.maxStudents}
                          </div>
                          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-1">
                            <div
                              className="bg-primary-600 h-2 rounded-full"
                              style={{ width: `${(event.students / event.maxStudents) * 100}%` }}
                            ></div>
                          </div>
                        </div>
                      ) : (
                        <span className="text-sm text-gray-500 dark:text-gray-400">-</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(event.status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex space-x-2 justify-end">
                        <button className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
                          <EyeIcon className="h-4 w-4" />
                        </button>
                        <button className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300">
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        <button className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>

        {filteredEvents.length === 0 && (
          <div className="text-center py-12">
            <CalendarDaysIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">Aucun événement</h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              {selectedDate || selectedRoom || selectedType 
                ? 'Aucun événement ne correspond aux critères de recherche.'
                : 'Commencez par ajouter un événement au planning.'
              }
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminSchedule;
