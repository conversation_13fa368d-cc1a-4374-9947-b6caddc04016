import React, { useState, useEffect } from 'react';
import {
  Cog6ToothIcon,
  UserIcon,
  AcademicCapIcon,
  CurrencyDollarIcon,
  EnvelopeIcon,
  ShieldCheckIcon,
  DocumentTextIcon,
  CloudArrowUpIcon,
  TrashIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

const AdminSettings = () => {
  const [activeTab, setActiveTab] = useState('general');
  const [settings, setSettings] = useState({
    general: {
      institutionName: 'EMBA ESPRIT',
      institutionEmail: '<EMAIL>',
      institutionPhone: '+216 71 123 456',
      institutionAddress: '<PERSON><PERSON>, Tu<PERSON>ie',
      academicYear: '2024-2025',
      currentSemester: 'Fall 2024'
    },
    academic: {
      passingGrade: 10,
      maxGrade: 20,
      attendanceRequired: 75,
      maxAbsences: 3,
      gradeWeights: {
        homework: 20,
        midterm: 40,
        final: 40
      }
    },
    financial: {
      currency: 'TND',
      tuitionFee: 25000,
      lateFeePercentage: 5,
      paymentMethods: ['bank_transfer', 'credit_card', 'cash'],
      paymentDeadlineDays: 30
    },
    notifications: {
      emailEnabled: true,
      smsEnabled: false,
      pushEnabled: true,
      reminderDays: [7, 3, 1],
      autoReminders: true
    },
    security: {
      passwordMinLength: 8,
      passwordRequireSpecial: true,
      sessionTimeout: 30,
      maxLoginAttempts: 5,
      twoFactorEnabled: false
    },
    backup: {
      autoBackup: true,
      backupFrequency: 'daily',
      retentionDays: 30,
      lastBackup: '2024-01-15T10:00:00Z'
    }
  });

  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState({ type: '', text: '' });

  const tabs = [
    { id: 'general', name: 'Général', icon: Cog6ToothIcon },
    { id: 'academic', name: 'Académique', icon: AcademicCapIcon },
    { id: 'financial', name: 'Financier', icon: CurrencyDollarIcon },
    { id: 'notifications', name: 'Notifications', icon: EnvelopeIcon },
    { id: 'security', name: 'Sécurité', icon: ShieldCheckIcon },
    { id: 'backup', name: 'Sauvegarde', icon: CloudArrowUpIcon }
  ];

  const handleSave = async (section) => {
    setLoading(true);
    try {
      // Simuler l'enregistrement
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setMessage({
        type: 'success',
        text: `Paramètres ${section} sauvegardés avec succès !`
      });
      
      setTimeout(() => setMessage({ type: '', text: '' }), 3000);
    } catch (error) {
      setMessage({
        type: 'error',
        text: 'Erreur lors de la sauvegarde des paramètres.'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (section, field, value) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
  };

  const handleNestedInputChange = (section, parentField, field, value) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [parentField]: {
          ...prev[section][parentField],
          [field]: value
        }
      }
    }));
  };

  const renderGeneralSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Nom de l'institution
          </label>
          <input
            type="text"
            value={settings.general.institutionName}
            onChange={(e) => handleInputChange('general', 'institutionName', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Email institutionnel
          </label>
          <input
            type="email"
            value={settings.general.institutionEmail}
            onChange={(e) => handleInputChange('general', 'institutionEmail', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Téléphone
          </label>
          <input
            type="tel"
            value={settings.general.institutionPhone}
            onChange={(e) => handleInputChange('general', 'institutionPhone', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Adresse
          </label>
          <input
            type="text"
            value={settings.general.institutionAddress}
            onChange={(e) => handleInputChange('general', 'institutionAddress', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Année académique
          </label>
          <input
            type="text"
            value={settings.general.academicYear}
            onChange={(e) => handleInputChange('general', 'academicYear', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Semestre actuel
          </label>
          <select
            value={settings.general.currentSemester}
            onChange={(e) => handleInputChange('general', 'currentSemester', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="Fall 2024">Automne 2024</option>
            <option value="Spring 2025">Printemps 2025</option>
            <option value="Summer 2025">Été 2025</option>
          </select>
        </div>
      </div>

      <button
        onClick={() => handleSave('général')}
        disabled={loading}
        className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 transition-colors"
      >
        {loading ? 'Sauvegarde...' : 'Sauvegarder'}
      </button>
    </div>
  );

  const renderAcademicSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Note de passage
          </label>
          <input
            type="number"
            min="0"
            max="20"
            value={settings.academic.passingGrade}
            onChange={(e) => handleInputChange('academic', 'passingGrade', parseInt(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Note maximale
          </label>
          <input
            type="number"
            min="1"
            max="100"
            value={settings.academic.maxGrade}
            onChange={(e) => handleInputChange('academic', 'maxGrade', parseInt(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Présence requise (%)
          </label>
          <input
            type="number"
            min="0"
            max="100"
            value={settings.academic.attendanceRequired}
            onChange={(e) => handleInputChange('academic', 'attendanceRequired', parseInt(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Absences maximales
          </label>
          <input
            type="number"
            min="0"
            value={settings.academic.maxAbsences}
            onChange={(e) => handleInputChange('academic', 'maxAbsences', parseInt(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Pondération des notes</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Devoirs (%)
            </label>
            <input
              type="number"
              min="0"
              max="100"
              value={settings.academic.gradeWeights.homework}
              onChange={(e) => handleNestedInputChange('academic', 'gradeWeights', 'homework', parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Examen partiel (%)
            </label>
            <input
              type="number"
              min="0"
              max="100"
              value={settings.academic.gradeWeights.midterm}
              onChange={(e) => handleNestedInputChange('academic', 'gradeWeights', 'midterm', parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Examen final (%)
            </label>
            <input
              type="number"
              min="0"
              max="100"
              value={settings.academic.gradeWeights.final}
              onChange={(e) => handleNestedInputChange('academic', 'gradeWeights', 'final', parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            />
          </div>
        </div>
        <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
          Total: {settings.academic.gradeWeights.homework + settings.academic.gradeWeights.midterm + settings.academic.gradeWeights.final}%
        </p>
      </div>

      <button
        onClick={() => handleSave('académique')}
        disabled={loading}
        className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 transition-colors"
      >
        {loading ? 'Sauvegarde...' : 'Sauvegarder'}
      </button>
    </div>
  );

  const renderFinancialSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Devise
          </label>
          <select
            value={settings.financial.currency}
            onChange={(e) => handleInputChange('financial', 'currency', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="TND">Dinar Tunisien (TND)</option>
            <option value="EUR">Euro (EUR)</option>
            <option value="USD">Dollar US (USD)</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Frais de scolarité
          </label>
          <input
            type="number"
            min="0"
            value={settings.financial.tuitionFee}
            onChange={(e) => handleInputChange('financial', 'tuitionFee', parseInt(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Pénalité de retard (%)
          </label>
          <input
            type="number"
            min="0"
            max="100"
            value={settings.financial.lateFeePercentage}
            onChange={(e) => handleInputChange('financial', 'lateFeePercentage', parseInt(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Délai de paiement (jours)
          </label>
          <input
            type="number"
            min="1"
            value={settings.financial.paymentDeadlineDays}
            onChange={(e) => handleInputChange('financial', 'paymentDeadlineDays', parseInt(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Méthodes de paiement acceptées
        </label>
        <div className="space-y-2">
          {[
            { value: 'bank_transfer', label: 'Virement bancaire' },
            { value: 'credit_card', label: 'Carte de crédit' },
            { value: 'cash', label: 'Espèces' },
            { value: 'check', label: 'Chèque' }
          ].map((method) => (
            <label key={method.value} className="flex items-center">
              <input
                type="checkbox"
                checked={settings.financial.paymentMethods.includes(method.value)}
                onChange={(e) => {
                  const methods = e.target.checked
                    ? [...settings.financial.paymentMethods, method.value]
                    : settings.financial.paymentMethods.filter(m => m !== method.value);
                  handleInputChange('financial', 'paymentMethods', methods);
                }}
                className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              />
              <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">{method.label}</span>
            </label>
          ))}
        </div>
      </div>

      <button
        onClick={() => handleSave('financier')}
        disabled={loading}
        className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 transition-colors"
      >
        {loading ? 'Sauvegarde...' : 'Sauvegarder'}
      </button>
    </div>
  );

  const renderCurrentTab = () => {
    switch (activeTab) {
      case 'general':
        return renderGeneralSettings();
      case 'academic':
        return renderAcademicSettings();
      case 'financial':
        return renderFinancialSettings();
      case 'notifications':
        return <div className="text-center py-8 text-gray-500">Paramètres de notifications - À implémenter</div>;
      case 'security':
        return <div className="text-center py-8 text-gray-500">Paramètres de sécurité - À implémenter</div>;
      case 'backup':
        return <div className="text-center py-8 text-gray-500">Paramètres de sauvegarde - À implémenter</div>;
      default:
        return renderGeneralSettings();
    }
  };

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Paramètres Système
        </h1>
        <p className="text-gray-500 dark:text-gray-400 mt-1">
          Configuration et paramètres globaux du système EMBA
        </p>
      </div>

      {/* Message de statut */}
      {message.text && (
        <div className={`p-4 rounded-lg ${
          message.type === 'success' 
            ? 'bg-green-50 dark:bg-green-900/20 text-green-800 dark:text-green-300' 
            : 'bg-red-50 dark:bg-red-900/20 text-red-800 dark:text-red-300'
        }`}>
          <div className="flex items-center">
            {message.type === 'success' ? (
              <CheckCircleIcon className="h-5 w-5 mr-2" />
            ) : (
              <ExclamationTriangleIcon className="h-5 w-5 mr-2" />
            )}
            {message.text}
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Navigation des onglets */}
        <div className="lg:col-span-1">
          <nav className="space-y-1">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                    activeTab === tab.id
                      ? 'bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300'
                      : 'text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white'
                  }`}
                >
                  <Icon className="h-5 w-5 mr-3" />
                  {tab.name}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Contenu de l'onglet actif */}
        <div className="lg:col-span-3">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            {renderCurrentTab()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminSettings;
