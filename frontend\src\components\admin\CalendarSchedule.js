import React, { useState, useEffect } from 'react';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import { 
  FunnelIcon,
  UserIcon,
  AcademicCapIcon,
  BuildingOfficeIcon,
  ClockIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

const CalendarSchedule = () => {
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [showEventModal, setShowEventModal] = useState(false);
  
  // Filtres
  const [filters, setFilters] = useState({
    professor: '',
    class: ''
  });
  const [professors, setProfessors] = useState([]);
  const [classes] = useState(['EMBA1A', 'EMBA1B', 'EMBA1C', 'EMBA1D', 'EMBA2A', 'EMBA2B', 'EMBA2C', 'EMBA2D']);

  useEffect(() => {
    fetchProfessors();
  }, []);

  useEffect(() => {
    fetchEvents();
  }, [filters]);

  const fetchEvents = async (dateInfo = null) => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        view: 'month'
      });

      if (dateInfo) {
        params.append('date', dateInfo.start.toISOString().split('T')[0]);
      }

      if (filters.professor) params.append('professorId', filters.professor);
      if (filters.class) params.append('classId', filters.class);

      const response = await fetch(`http://localhost:5000/api/schedule/events?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        console.log('📅 Événements reçus:', data.events);
        
        // Transformer les événements pour FullCalendar
        const calendarEvents = (data.events || []).map(event => ({
          id: event.id,
          title: `${event.courseCode} - ${event.title}`,
          start: event.start,
          end: event.end,
          backgroundColor: '#dc2626',
          borderColor: '#b91c1c',
          textColor: '#ffffff',
          extendedProps: {
            courseCode: event.courseCode,
            courseTitle: event.title,
            description: event.description,
            instructors: event.instructors || [],
            classes: event.classes || [],
            room: event.room || { name: 'Salle à définir' },
            assignmentId: event.assignmentId,
            courseId: event.courseId
          }
        }));

        setEvents(calendarEvents);
      } else {
        console.error('Erreur lors du chargement des événements');
        setEvents([]);
      }
    } catch (error) {
      console.error('Erreur:', error);
      setEvents([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchProfessors = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/professors', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setProfessors(data.professors || []);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des professeurs:', error);
    }
  };

  const handleEventClick = (clickInfo) => {
    const event = clickInfo.event;
    setSelectedEvent({
      title: event.extendedProps.courseTitle,
      courseCode: event.extendedProps.courseCode,
      description: event.extendedProps.description,
      start: event.start,
      end: event.end,
      instructors: event.extendedProps.instructors,
      classes: event.extendedProps.classes,
      room: event.extendedProps.room
    });
    setShowEventModal(true);
  };

  const handleDatesSet = (dateInfo) => {
    fetchEvents(dateInfo);
  };

  const EventModal = () => {
    if (!selectedEvent) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-lg">
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Détails du cours
            </h3>
            <button
              onClick={() => setShowEventModal(false)}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          <div className="p-6 space-y-4">
            {/* Titre du cours */}
            <div>
              <h4 className="text-xl font-semibold text-gray-900 dark:text-white">
                {selectedEvent.title}
              </h4>
              <p className="text-sm text-red-600 dark:text-red-400 font-medium">
                {selectedEvent.courseCode}
              </p>
            </div>

            {/* Horaires */}
            <div className="flex items-center text-sm bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
              <ClockIcon className="h-5 w-5 mr-3 text-gray-400" />
              <div>
                <div className="font-medium text-gray-900 dark:text-white">
                  {selectedEvent.start?.toLocaleDateString('fr-FR', { 
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </div>
                <div className="text-gray-600 dark:text-gray-300">
                  {selectedEvent.start?.toLocaleTimeString('fr-FR', { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                  })} - {selectedEvent.end?.toLocaleTimeString('fr-FR', { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                  })}
                </div>
              </div>
            </div>

            {/* Professeurs */}
            <div className="flex items-start text-sm">
              <UserIcon className="h-5 w-5 mr-3 text-gray-400 mt-0.5" />
              <div className="flex-1">
                <div className="font-medium text-gray-900 dark:text-white mb-1">
                  Professeur{selectedEvent.instructors?.length > 1 ? 's' : ''}
                </div>
                {selectedEvent.instructors?.length > 0 ? (
                  selectedEvent.instructors.map((instructor, index) => (
                    <div key={index} className="text-gray-600 dark:text-gray-300">
                      <span className="font-medium">{instructor.academicTitle || 'Dr.'} {instructor.name}</span>
                      <span className="ml-2 text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded">
                        {instructor.role === 'primary' ? 'Principal' : 
                         instructor.role === 'secondary' ? 'Secondaire' : 'Invité'}
                      </span>
                    </div>
                  ))
                ) : (
                  <div className="text-gray-500 dark:text-gray-400">Professeur à définir</div>
                )}
              </div>
            </div>

            {/* Classes */}
            <div className="flex items-center text-sm">
              <AcademicCapIcon className="h-5 w-5 mr-3 text-gray-400" />
              <div className="flex-1">
                <div className="font-medium text-gray-900 dark:text-white mb-1">
                  Classe{selectedEvent.classes?.length > 1 ? 's' : ''}
                </div>
                <div className="flex flex-wrap gap-2">
                  {selectedEvent.classes?.length > 0 ? (
                    selectedEvent.classes.map((className, index) => (
                      <span 
                        key={index}
                        className={`px-2 py-1 text-xs font-semibold rounded-full ${
                          className.startsWith('EMBA1') 
                            ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                            : 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
                        }`}
                      >
                        {className}
                      </span>
                    ))
                  ) : (
                    <span className="text-gray-500 dark:text-gray-400">Classe à définir</span>
                  )}
                </div>
              </div>
            </div>

            {/* Salle */}
            <div className="flex items-center text-sm">
              <BuildingOfficeIcon className="h-5 w-5 mr-3 text-gray-400" />
              <div>
                <div className="font-medium text-gray-900 dark:text-white">Salle</div>
                <div className="text-gray-600 dark:text-gray-300">
                  {selectedEvent.room?.name || 'Salle à définir'}
                </div>
              </div>
            </div>

            {/* Description */}
            {selectedEvent.description && (
              <div>
                <div className="font-medium text-gray-900 dark:text-white mb-1">Description</div>
                <p className="text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                  {selectedEvent.description}
                </p>
              </div>
            )}
          </div>

          <div className="flex justify-end p-6 border-t border-gray-200 dark:border-gray-700">
            <button
              onClick={() => setShowEventModal(false)}
              className="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
            >
              Fermer
            </button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="p-6 bg-white dark:bg-gray-900 min-h-screen">
      {/* En-tête */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            📅 Planning des cours EMBA
          </h1>
          
          {/* Filtres */}
          <div className="flex items-center space-x-3">
            {/* Bouton de debug temporaire */}
            <button
              onClick={async () => {
                try {
                  const response = await fetch('http://localhost:5000/api/schedule/debug', {
                    headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` }
                  });
                  const data = await response.json();
                  console.log('🔍 Debug affectations:', data);
                  alert(`${data.totalAssignments} affectations trouvées. Voir console pour détails.`);
                } catch (error) {
                  console.error('Erreur debug:', error);
                }
              }}
              className="px-3 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 text-sm"
            >
              🔍 Debug
            </button>

            <FunnelIcon className="h-5 w-5 text-gray-400" />
            
            <select
              value={filters.professor}
              onChange={(e) => setFilters(prev => ({ ...prev, professor: e.target.value }))}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-red-500 focus:border-red-500"
            >
              <option value="">Tous les professeurs</option>
              {professors.map(prof => (
                <option key={prof._id} value={prof._id}>
                  {prof.user ? `${prof.user.firstName} ${prof.user.lastName}` : 'Nom non disponible'}
                </option>
              ))}
            </select>

            <select
              value={filters.class}
              onChange={(e) => setFilters(prev => ({ ...prev, class: e.target.value }))}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-red-500 focus:border-red-500"
            >
              <option value="">Toutes les classes</option>
              {classes.map(cls => (
                <option key={cls} value={cls}>{cls}</option>
              ))}
            </select>
          </div>
        </div>

        {/* Statistiques rapides */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg">
            <div className="text-2xl font-bold text-red-600 dark:text-red-400">
              {events.length}
            </div>
            <div className="text-sm text-red-600 dark:text-red-400">
              Séances programmées
            </div>
          </div>
          <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {new Set(events.flatMap(e => e.extendedProps?.instructors?.map(i => i.name) || [])).size}
            </div>
            <div className="text-sm text-blue-600 dark:text-blue-400">
              Professeurs actifs
            </div>
          </div>
          <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
            <div className="text-2xl font-bold text-green-600 dark:text-green-400">
              {new Set(events.flatMap(e => e.extendedProps?.classes || [])).size}
            </div>
            <div className="text-sm text-green-600 dark:text-green-400">
              Classes concernées
            </div>
          </div>
        </div>
      </div>

      {/* Calendrier */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4">
        {loading && (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
            <span className="ml-2 text-gray-600 dark:text-gray-300">Chargement du planning...</span>
          </div>
        )}
        
        <FullCalendar
          plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
          initialView="timeGridWeek"
          headerToolbar={{
            left: 'prev,next today',
            center: 'title',
            right: 'dayGridMonth,timeGridWeek,timeGridDay'
          }}
          events={events}
          eventClick={handleEventClick}
          datesSet={handleDatesSet}
          height="auto"
          locale="fr"
          firstDay={1}
          slotMinTime="08:00:00"
          slotMaxTime="19:00:00"
          allDaySlot={false}
          eventDisplay="block"
          eventTextColor="#ffffff"
          eventBackgroundColor="#dc2626"
          eventBorderColor="#b91c1c"
          buttonText={{
            today: "Aujourd'hui",
            month: 'Mois',
            week: 'Semaine',
            day: 'Jour'
          }}
          dayHeaderFormat={{ weekday: 'long' }}
          slotLabelFormat={{
            hour: '2-digit',
            minute: '2-digit',
            hour12: false
          }}
          eventTimeFormat={{
            hour: '2-digit',
            minute: '2-digit',
            hour12: false
          }}
        />
      </div>

      {/* Légende */}
      <div className="mt-6 bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
        <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Légende</h3>
        <div className="flex flex-wrap items-center gap-6 text-sm text-gray-600 dark:text-gray-300">
          <div className="flex items-center">
            <div className="w-4 h-4 bg-red-600 rounded mr-2"></div>
            <span>Cours EMBA</span>
          </div>
          <div className="flex items-center">
            <UserIcon className="h-4 w-4 mr-1" />
            <span>Cliquez sur un cours pour voir les détails</span>
          </div>
          <div className="flex items-center">
            <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-xs mr-2">EMBA1</span>
            <span>Première année</span>
          </div>
          <div className="flex items-center">
            <span className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-2 py-1 rounded text-xs mr-2">EMBA2</span>
            <span>Deuxième année</span>
          </div>
        </div>
      </div>

      {/* Modal des détails */}
      {showEventModal && <EventModal />}
    </div>
  );
};

export default CalendarSchedule;
