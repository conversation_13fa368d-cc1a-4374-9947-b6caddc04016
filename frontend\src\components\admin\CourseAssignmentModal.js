import React, { useState, useEffect } from 'react';
import { XMarkIcon, ExclamationTriangleIcon, PlusIcon, TrashIcon } from '@heroicons/react/24/outline';

const CourseAssignmentModal = ({ isOpen, onClose, onSubmit, course = null, professors = [], isLoading = false }) => {
  const [formData, setFormData] = useState({
    instructors: [{ professorId: '', role: 'primary' }],
    classes: [''],
    startDate: '',
    timeSlot: {
      dayOfWeek: 'Monday',
      startTime: '09:00',
      endTime: '12:15',
      location: {
        building: 'Bâtiment A',
        room: 'Salle 101',
        capacity: 30
      }
    }
  });

  const [errors, setErrors] = useState({});
  const [conflicts, setConflicts] = useState([]);

  // Classes disponibles par promotion
  const classOptions = {
    EMBA1: ['EMBA1A', 'EMBA1B', 'EMBA1C', 'EMBA1D'],
    EMBA2: ['EMBA2A', 'EMBA2B', 'EMBA2C', 'EMBA2D']
  };

  // Créneaux horaires disponibles (3h = 2 créneaux de 1h30 avec pause)
  const timeSlotOptions = [
    { value: { dayOfWeek: 'Monday', startTime: '09:00', endTime: '12:15' }, label: 'Lundi matin (9h-12h15)' },
    { value: { dayOfWeek: 'Tuesday', startTime: '09:00', endTime: '12:15' }, label: 'Mardi matin (9h-12h15)' },
    { value: { dayOfWeek: 'Wednesday', startTime: '09:00', endTime: '12:15' }, label: 'Mercredi matin (9h-12h15)' },
    { value: { dayOfWeek: 'Thursday', startTime: '09:00', endTime: '12:15' }, label: 'Jeudi matin (9h-12h15)' },
    { value: { dayOfWeek: 'Friday', startTime: '09:00', endTime: '12:15' }, label: 'Vendredi matin (9h-12h15)' },
    { value: { dayOfWeek: 'Monday', startTime: '13:30', endTime: '16:45' }, label: 'Lundi après-midi (13h30-16h45)' },
    { value: { dayOfWeek: 'Tuesday', startTime: '13:30', endTime: '16:45' }, label: 'Mardi après-midi (13h30-16h45)' },
    { value: { dayOfWeek: 'Wednesday', startTime: '13:30', endTime: '16:45' }, label: 'Mercredi après-midi (13h30-16h45)' },
    { value: { dayOfWeek: 'Thursday', startTime: '13:30', endTime: '16:45' }, label: 'Jeudi après-midi (13h30-16h45)' },
    { value: { dayOfWeek: 'Friday', startTime: '13:30', endTime: '16:45' }, label: 'Vendredi après-midi (13h30-16h45)' },
    { value: { dayOfWeek: 'Saturday', startTime: '09:00', endTime: '12:15' }, label: 'Samedi matin (9h-12h15) - Rattrapage' }
  ];



  useEffect(() => {
    if (course && isOpen) {
      // Réinitialiser le formulaire pour une nouvelle affectation
      setFormData({
        instructors: [{ professorId: '', role: 'primary' }],
        classes: [''],
        startDate: '',
        timeSlot: {
          dayOfWeek: 'Monday',
          startTime: '09:00',
          endTime: '12:15',
          location: {
            building: 'Bâtiment A',
            room: 'Salle 101',
            capacity: 30
          }
        }
      });
    }
    setErrors({});
    setConflicts([]);
  }, [course, isOpen]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleScheduleChange = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      schedule: prev.schedule.map((item, i) => 
        i === index ? { ...item, [field]: value } : item
      )
    }));
  };

  const handleLocationChange = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      schedule: prev.schedule.map((item, i) => 
        i === index ? { 
          ...item, 
          location: { ...item.location, [field]: value }
        } : item
      )
    }));
  };

  const addScheduleSlot = () => {
    setFormData(prev => ({
      ...prev,
      schedule: [...prev.schedule, {
        dayOfWeek: 'Monday',
        startTime: '09:00',
        endTime: '12:00',
        location: {
          building: 'Bâtiment A',
          room: 'Salle 101',
          capacity: 30
        }
      }]
    }));
  };

  const removeScheduleSlot = (index) => {
    if (formData.schedule.length > 1) {
      setFormData(prev => ({
        ...prev,
        schedule: prev.schedule.filter((_, i) => i !== index)
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.instructor) newErrors.instructor = 'Professeur requis';
    if (!formData.class) newErrors.class = 'Classe requise';

    // Valider les créneaux horaires
    formData.schedule.forEach((slot, index) => {
      if (!slot.startTime) newErrors[`schedule_${index}_start`] = 'Heure de début requise';
      if (!slot.endTime) newErrors[`schedule_${index}_end`] = 'Heure de fin requise';
      if (slot.startTime && slot.endTime && slot.startTime >= slot.endTime) {
        newErrors[`schedule_${index}_end`] = 'L\'heure de fin doit être après l\'heure de début';
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (validateForm()) {
      try {
        await onSubmit(formData);
        setConflicts([]);
      } catch (error) {
        if (error.conflicts) {
          setConflicts(error.conflicts);
        }
      }
    }
  };

  if (!isOpen || !course) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Affecter le cours : {course.title}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* Affichage des conflits */}
        {conflicts.length > 0 && (
          <div className="p-6 bg-red-50 dark:bg-red-900/20 border-b border-red-200 dark:border-red-800">
            <div className="flex items-start">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400 mt-0.5 mr-3" />
              <div>
                <h4 className="text-sm font-medium text-red-800 dark:text-red-200">
                  Conflits d'horaires détectés
                </h4>
                <ul className="mt-2 text-sm text-red-700 dark:text-red-300">
                  {conflicts.map((conflict, index) => (
                    <li key={index} className="mt-1">• {conflict.message}</li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Informations du cours */}
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Informations du cours</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-500 dark:text-gray-400">Code:</span>
                <span className="ml-2 font-medium text-gray-900 dark:text-white">{course.courseCode}</span>
              </div>
              <div>
                <span className="text-gray-500 dark:text-gray-400">Promotion:</span>
                <span className="ml-2 font-medium text-gray-900 dark:text-white">{course.promotion}</span>
              </div>
              <div>
                <span className="text-gray-500 dark:text-gray-400">Crédits:</span>
                <span className="ml-2 font-medium text-gray-900 dark:text-white">{course.creditHours}</span>
              </div>
              <div>
                <span className="text-gray-500 dark:text-gray-400">Statut:</span>
                <span className={`ml-2 px-2 py-1 text-xs rounded-full ${
                  course.status === 'active' 
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                    : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                }`}>
                  {course.status}
                </span>
              </div>
            </div>
          </div>

          {/* Affectation */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Professeur *
              </label>
              <select
                name="instructor"
                value={formData.instructor}
                onChange={handleChange}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                  errors.instructor ? 'border-red-500' : 'border-gray-300'
                }`}
              >
                <option value="">Sélectionner un professeur</option>
                {professors.map(prof => (
                  <option key={prof._id} value={prof._id}>
                    {prof.user ? `${prof.user.firstName} ${prof.user.lastName}` : 'Nom non disponible'} - {prof.department}
                  </option>
                ))}
              </select>
              {errors.instructor && <p className="mt-1 text-sm text-red-600">{errors.instructor}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Classe *
              </label>
              <select
                name="class"
                value={formData.class}
                onChange={handleChange}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                  errors.class ? 'border-red-500' : 'border-gray-300'
                }`}
              >
                <option value="">Sélectionner une classe</option>
                {course.promotion && classOptions[course.promotion] ?
                  classOptions[course.promotion].map(className => (
                    <option key={className} value={className}>{className}</option>
                  )) :
                  <option disabled>Aucune classe disponible pour {course.promotion}</option>
                }
              </select>
              {errors.class && <p className="mt-1 text-sm text-red-600">{errors.class}</p>}
            </div>
          </div>

          {/* Planning */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white">Planning des cours</h4>
              <button
                type="button"
                onClick={addScheduleSlot}
                className="px-3 py-1 text-sm bg-primary-600 text-white rounded hover:bg-primary-700"
              >
                Ajouter un créneau
              </button>
            </div>

            {formData.schedule.map((slot, index) => (
              <div key={index} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4 mb-4">
                <div className="flex items-center justify-between mb-3">
                  <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Créneau {index + 1}
                  </h5>
                  {formData.schedule.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeScheduleSlot(index)}
                      className="text-red-600 hover:text-red-800 text-sm"
                    >
                      Supprimer
                    </button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div>
                    <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Jour
                    </label>
                    <select
                      value={slot.dayOfWeek}
                      onChange={(e) => handleScheduleChange(index, 'dayOfWeek', e.target.value)}
                      className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    >
                      <option value="Monday">Lundi</option>
                      <option value="Tuesday">Mardi</option>
                      <option value="Wednesday">Mercredi</option>
                      <option value="Thursday">Jeudi</option>
                      <option value="Friday">Vendredi</option>
                      <option value="Saturday">Samedi</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Début
                    </label>
                    <input
                      type="time"
                      value={slot.startTime}
                      onChange={(e) => handleScheduleChange(index, 'startTime', e.target.value)}
                      className={`w-full px-2 py-1 text-sm border rounded focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                        errors[`schedule_${index}_start`] ? 'border-red-500' : 'border-gray-300'
                      }`}
                    />
                    {errors[`schedule_${index}_start`] && (
                      <p className="mt-1 text-xs text-red-600">{errors[`schedule_${index}_start`]}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Fin
                    </label>
                    <input
                      type="time"
                      value={slot.endTime}
                      onChange={(e) => handleScheduleChange(index, 'endTime', e.target.value)}
                      className={`w-full px-2 py-1 text-sm border rounded focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                        errors[`schedule_${index}_end`] ? 'border-red-500' : 'border-gray-300'
                      }`}
                    />
                    {errors[`schedule_${index}_end`] && (
                      <p className="mt-1 text-xs text-red-600">{errors[`schedule_${index}_end`]}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Salle
                    </label>
                    <input
                      type="text"
                      value={slot.location?.room || ''}
                      onChange={(e) => handleLocationChange(index, 'room', e.target.value)}
                      placeholder="Salle 101"
                      className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Annuler
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
            >
              {isLoading ? 'Affectation...' : 'Affecter le cours'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CourseAssignmentModal;
