import React, { useState, useEffect } from 'react';
import { XMarkIcon, UserIcon, AcademicCapIcon, CalendarIcon, TrashIcon } from '@heroicons/react/24/outline';

const CourseAssignmentsModal = ({ isOpen, onClose, course = null }) => {
  const [assignments, setAssignments] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (course && isOpen) {
      fetchAssignments();
    }
  }, [course, isOpen]);

  const fetchAssignments = async () => {
    try {
      setLoading(true);
      const response = await fetch(`http://localhost:5000/api/courses/${course._id}/assignments`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setAssignments(data.assignments || []);
      } else {
        console.error('Erreur lors du chargement des affectations');
        setAssignments([]);
      }
    } catch (error) {
      console.error('Erreur:', error);
      setAssignments([]);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteAssignment = async (assignmentId) => {
    if (!window.confirm('Êtes-vous sûr de vouloir supprimer cette affectation ?')) {
      return;
    }

    try {
      const response = await fetch(`http://localhost:5000/api/courses/assignments/${assignmentId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        setAssignments(prev => prev.filter(a => a._id !== assignmentId));
        alert('Affectation supprimée avec succès');
      } else {
        alert('Erreur lors de la suppression');
      }
    } catch (error) {
      console.error('Erreur:', error);
      alert('Erreur lors de la suppression');
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('fr-FR');
  };

  const formatTimeSlot = (timeSlot) => {
    const days = {
      'Monday': 'Lundi',
      'Tuesday': 'Mardi',
      'Wednesday': 'Mercredi',
      'Thursday': 'Jeudi',
      'Friday': 'Vendredi',
      'Saturday': 'Samedi'
    };
    
    return `${days[timeSlot.dayOfWeek]} ${timeSlot.startTime}-${timeSlot.endTime}`;
  };

  if (!isOpen || !course) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Affectations du cours : {course.title}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6">
          {/* Informations du cours */}
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-6">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Informations du cours</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-gray-500 dark:text-gray-400">Code:</span>
                <span className="ml-2 font-medium text-gray-900 dark:text-white">{course.courseCode}</span>
              </div>
              <div>
                <span className="text-gray-500 dark:text-gray-400">Promotion:</span>
                <span className="ml-2 font-medium text-gray-900 dark:text-white">{course.promotion}</span>
              </div>
              <div>
                <span className="text-gray-500 dark:text-gray-400">Crédits:</span>
                <span className="ml-2 font-medium text-gray-900 dark:text-white">{course.creditHours}</span>
              </div>
              <div>
                <span className="text-gray-500 dark:text-gray-400">Semestre:</span>
                <span className="ml-2 font-medium text-gray-900 dark:text-white">{course.semester}</span>
              </div>
            </div>
          </div>

          {/* Liste des affectations */}
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
              <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">Chargement des affectations...</p>
            </div>
          ) : assignments.length === 0 ? (
            <div className="text-center py-8">
              <AcademicCapIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 dark:text-gray-400">Aucune affectation pour ce cours</p>
            </div>
          ) : (
            <div className="space-y-4">
              {assignments.map((assignment, index) => (
                <div key={assignment._id} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                  <div className="flex items-start justify-between mb-4">
                    <h5 className="text-lg font-medium text-gray-900 dark:text-white">
                      Affectation #{index + 1}
                    </h5>
                    <div className="flex items-center space-x-2">
                      <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                        assignment.status === 'active' 
                          ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                          : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                      }`}>
                        {assignment.status}
                      </span>
                      <button
                        onClick={() => handleDeleteAssignment(assignment._id)}
                        className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                        title="Supprimer cette affectation"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {/* Professeurs */}
                    <div>
                      <h6 className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <UserIcon className="h-4 w-4 mr-1" />
                        Professeurs
                      </h6>
                      <div className="space-y-2">
                        {assignment.instructors?.map((instructor, idx) => {
                          console.log('🔍 Debug instructor data:', instructor); // Debug temporaire
                          return (
                            <div key={idx} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded">
                              <div>
                                <div className="text-sm font-medium text-gray-900 dark:text-white">
                                  {instructor.professor?.user?.firstName && instructor.professor?.user?.lastName ?
                                    `${instructor.professor.user.firstName} ${instructor.professor.user.lastName}` :
                                    instructor.professor?.user ?
                                      `${instructor.professor.user.firstName || ''} ${instructor.professor.user.lastName || ''}`.trim() || 'Nom non disponible' :
                                      'Nom non disponible'
                                  }
                                </div>
                                <div className="text-xs text-gray-500 dark:text-gray-400">
                                  {instructor.professor?.department || 'Département non défini'}
                                </div>
                              </div>
                              <span className={`px-2 py-1 text-xs rounded-full ${
                                instructor.role === 'primary'
                                  ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                                  : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
                              }`}>
                                {instructor.role === 'primary' ? 'Principal' :
                                 instructor.role === 'secondary' ? 'Secondaire' : 'Invité'}
                              </span>
                            </div>
                          );
                        })}
                      </div>
                    </div>

                    {/* Classes */}
                    <div>
                      <h6 className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <AcademicCapIcon className="h-4 w-4 mr-1" />
                        Classes
                      </h6>
                      <div className="space-y-2">
                        {assignment.classes?.map((classInfo, idx) => (
                          <div key={idx} className="p-2 bg-gray-50 dark:bg-gray-700 rounded">
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              {classInfo.className}
                            </div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              Max: {classInfo.maxStudents} étudiants
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Planning */}
                    <div>
                      <h6 className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <CalendarIcon className="h-4 w-4 mr-1" />
                        Planning
                      </h6>
                      <div className="space-y-2">
                        <div className="p-2 bg-gray-50 dark:bg-gray-700 rounded">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            Début: {formatDate(assignment.schedule?.startDate)}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {assignment.schedule?.durationWeeks} semaines • {assignment.schedule?.hoursPerWeek}h/semaine
                          </div>
                        </div>
                        {assignment.schedule?.timeSlots?.map((slot, idx) => (
                          <div key={idx} className="p-2 bg-gray-50 dark:bg-gray-700 rounded">
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              {formatTimeSlot(slot)}
                            </div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              {slot.location?.room || 'Salle à définir'}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="flex justify-end p-6 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Fermer
          </button>
        </div>
      </div>
    </div>
  );
};

export default CourseAssignmentsModal;
