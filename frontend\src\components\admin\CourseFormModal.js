import React, { useState, useEffect } from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';

const CourseFormModal = ({ isOpen, onClose, onSubmit, course = null, professors = [], isLoading = false }) => {
  const [formData, setFormData] = useState({
    courseCode: '',
    title: '',
    description: '',
    shortDescription: '',
    creditHours: 6,
    contactHours: 90,
    level: 'Intermidière',
    category: 'Management',
    promotion: '', // Seulement le niveau au début
    academicYear: '2024-2025',
    semester: 'Semestre 1',
    startDate: '',
    endDate: '',
    capacity: {
      maximum: 30,
      minimum: 10
    }
  });

  const [errors, setErrors] = useState({});

  useEffect(() => {
    if (course) {
      setFormData({
        courseCode: course.courseCode || '',
        title: course.title || '',
        description: course.description || '',
        shortDescription: course.shortDescription || '',
        creditHours: course.creditHours || 6,
        contactHours: course.contactHours || 90,
        level: course.level || 'Intermidière',
        category: course.category || 'Management',
        promotion: course.promotion || '',
        academicYear: course.academicYear || '2024-2025',
        semester: course.semester || 'Semestre 1',
        startDate: course.startDate ? course.startDate.split('T')[0] : '',
        endDate: course.endDate ? course.endDate.split('T')[0] : '',
        capacity: {
          maximum: course.capacity?.maximum || 30,
          minimum: course.capacity?.minimum || 10
        },

      });
    } else {
      // Reset form for new course
      setFormData({
        courseCode: '',
        title: '',
        description: '',
        shortDescription: '',
        creditHours: 6,
        contactHours: 90,
        level: 'Intermidière',
        category: 'Management',
        promotion: '',
        academicYear: '2024-2025',
        semester: 'Semestre 1',
        startDate: '',
        endDate: '',
        capacity: {
          maximum: 30,
          minimum: 10
        },

      });
    }
    setErrors({});
  }, [course, isOpen]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };



  const validateForm = () => {
    const newErrors = {};

    if (!formData.courseCode.trim()) newErrors.courseCode = 'Code du cours requis';
    if (!formData.courseCode.match(/^[A-Z]{2,4}\d{3,4}$/)) {
      newErrors.courseCode = 'Code du cours invalide (format: ABC123 ou ABCD1234)';
    }
    if (!formData.title.trim()) newErrors.title = 'Titre du cours requis';
    if (!formData.description.trim()) newErrors.description = 'Description requise';
    if (!formData.promotion) newErrors.promotion = 'Promotion requise';



    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (validateForm()) {
      onSubmit(formData);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            {course ? 'Modifier le cours' : 'Nouveau cours'}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Informations de base */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Code du cours *
              </label>
              <input
                type="text"
                name="courseCode"
                value={formData.courseCode}
                onChange={handleChange}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                  errors.courseCode ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Ex: MGT101"
              />
              {errors.courseCode && <p className="mt-1 text-sm text-red-600">{errors.courseCode}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Titre du cours *
              </label>
              <input
                type="text"
                name="title"
                value={formData.title}
                onChange={handleChange}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                  errors.title ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Ex: Management Stratégique"
              />
              {errors.title && <p className="mt-1 text-sm text-red-600">{errors.title}</p>}
            </div>
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Description *
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleChange}
              rows={4}
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                errors.description ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="Description détaillée du cours..."
            />
            {errors.description && <p className="mt-1 text-sm text-red-600">{errors.description}</p>}
          </div>

          {/* Détails académiques */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Crédits
              </label>
              <input
                type="number"
                name="creditHours"
                value={formData.creditHours}
                onChange={handleChange}
                min="1"
                max="10"
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Heures de contact
              </label>
              <input
                type="number"
                name="contactHours"
                value={formData.contactHours}
                onChange={handleChange}
                min="1"
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Niveau
              </label>
              <select
                name="level"
                value={formData.level}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              >
                <option value="Facile">Facile</option>
                <option value="Intermidière">Intermidière</option>
                <option value="Difficile">Difficile</option>
              </select>
            </div>

            {/* <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Catégorie
              </label>
              <select
                name="category"
                value={formData.category}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              >
                <option value="Management">Management</option>
                <option value="Finance">Finance</option>
                <option value="Marketing">Marketing</option>
                <option value="Operations">Operations</option>
                <option value="Strategy">Strategy</option>
                <option value="Leadership">Leadership</option>
                <option value="Economics">Economics</option>
                <option value="Accounting">Accounting</option>
                <option value="Information Systems">Information Systems</option>
                <option value="Human Resources">Human Resources</option>
                <option value="Entrepreneurship">Entrepreneurship</option>
              </select>
            </div> */}
          </div>

          {/* Promotion */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Promotion *
              </label>
              <select
                name="promotion"
                value={formData.promotion}
                onChange={handleChange}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                  errors.promotion ? 'border-red-500' : 'border-gray-300'
                }`}
              >
                <option value="">Sélectionner une promotion</option>
                <option value="EMBA1">EMBA1</option>
                <option value="EMBA2">EMBA2</option>
              </select>
              {errors.promotion && <p className="mt-1 text-sm text-red-600">{errors.promotion}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Semestre
              </label>
              <select
                name="semester"
                value={formData.semester}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              >
                <option value="Semestre 1">Semestre 1</option>
                <option value="Semestre 2">Semestre 2</option>
              </select>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Annuler
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
            >
              {isLoading ? 'Enregistrement...' : (course ? 'Modifier' : 'Créer')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CourseFormModal;
