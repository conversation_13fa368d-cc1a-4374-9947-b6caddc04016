import React, { useState, useEffect } from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';

const ModuleFormModal = ({ isOpen, onClose, onSubmit, module = null, professors = [], isLoading = false }) => {
  const [formData, setFormData] = useState({
    moduleCode: '',
    title: '',
    description: '',
    creditHours: 6,
    duration: {
      weeks: 12,
      intensiveFormat: false,
      totalContactHours: 90
    },
    category: 'Core',
    level: 'Intermediate',
    theme: 'Management',
    promotion: 'EMBA1',
    semester: 'Fall',
    coordinator: '',
    academicYear: '2024-2025',
    startDate: '',
    endDate: '',
    capacity: {
      maximum: 30,
      minimum: 10
    },
    status: 'draft'
  });

  const [errors, setErrors] = useState({});

  useEffect(() => {
    if (module) {
      setFormData({
        moduleCode: module.moduleCode || '',
        title: module.title || '',
        description: module.description || '',
        creditHours: module.creditHours || 6,
        duration: {
          weeks: module.duration?.weeks || 12,
          intensiveFormat: module.duration?.intensiveFormat || false,
          totalContactHours: module.duration?.totalContactHours || 90
        },
        category: module.category || 'Core',
        level: module.level || 'Intermediate',
        theme: module.theme || 'Management',
        promotion: module.promotion || 'EMBA1',
        semester: module.semester || 'Fall',
        coordinator: module.coordinator?._id || '',
        academicYear: module.academicYear || '2024-2025',
        startDate: module.startDate ? module.startDate.split('T')[0] : '',
        endDate: module.endDate ? module.endDate.split('T')[0] : '',
        capacity: {
          maximum: module.capacity?.maximum || 30,
          minimum: module.capacity?.minimum || 10
        },
        status: module.status || 'draft'
      });
    } else {
      // Reset form for new module
      setFormData({
        moduleCode: '',
        title: '',
        description: '',
        creditHours: 6,
        duration: {
          weeks: 12,
          intensiveFormat: false,
          totalContactHours: 90
        },
        category: 'Core',
        level: 'Intermediate',
        theme: 'Management',
        promotion: 'EMBA1',
        semester: 'Fall',
        coordinator: '',
        academicYear: '2024-2025',
        startDate: '',
        endDate: '',
        capacity: {
          maximum: 30,
          minimum: 10
        },
        status: 'draft'
      });
    }
    setErrors({});
  }, [module, isOpen]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: type === 'checkbox' ? checked : value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : value
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.moduleCode.trim()) newErrors.moduleCode = 'Code du module requis';
    if (!formData.title.trim()) newErrors.title = 'Titre du module requis';
    if (!formData.description.trim()) newErrors.description = 'Description requise';
    if (!formData.coordinator) newErrors.coordinator = 'Coordinateur requis';
    if (!formData.startDate) newErrors.startDate = 'Date de début requise';
    if (!formData.endDate) newErrors.endDate = 'Date de fin requise';

    if (formData.startDate && formData.endDate && new Date(formData.startDate) >= new Date(formData.endDate)) {
      newErrors.endDate = 'La date de fin doit être après la date de début';
    }

    if (formData.capacity.minimum >= formData.capacity.maximum) {
      newErrors.capacity = 'La capacité minimum doit être inférieure à la capacité maximum';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (validateForm()) {
      onSubmit(formData);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            {module ? 'Modifier le module' : 'Nouveau module'}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Informations de base */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Code du module *
              </label>
              <input
                type="text"
                name="moduleCode"
                value={formData.moduleCode}
                onChange={handleChange}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                  errors.moduleCode ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Ex: MOD101"
              />
              {errors.moduleCode && <p className="mt-1 text-sm text-red-600">{errors.moduleCode}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Titre du module *
              </label>
              <input
                type="text"
                name="title"
                value={formData.title}
                onChange={handleChange}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                  errors.title ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Ex: Leadership Stratégique"
              />
              {errors.title && <p className="mt-1 text-sm text-red-600">{errors.title}</p>}
            </div>
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Description *
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleChange}
              rows={4}
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                errors.description ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="Description détaillée du module..."
            />
            {errors.description && <p className="mt-1 text-sm text-red-600">{errors.description}</p>}
          </div>

          {/* Détails académiques */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Crédits
              </label>
              <input
                type="number"
                name="creditHours"
                value={formData.creditHours}
                onChange={handleChange}
                min="1"
                max="20"
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Semaines
              </label>
              <input
                type="number"
                name="duration.weeks"
                value={formData.duration.weeks}
                onChange={handleChange}
                min="1"
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Heures totales
              </label>
              <input
                type="number"
                name="duration.totalContactHours"
                value={formData.duration.totalContactHours}
                onChange={handleChange}
                min="1"
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                name="duration.intensiveFormat"
                checked={formData.duration.intensiveFormat}
                onChange={handleChange}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                Format intensif
              </label>
            </div>
          </div>

          {/* Classification */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Catégorie
              </label>
              <select
                name="category"
                value={formData.category}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              >
                <option value="Core">Core</option>
                <option value="Elective">Elective</option>
                <option value="Specialization">Specialization</option>
                <option value="Foundation">Foundation</option>
                <option value="Capstone">Capstone</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Niveau
              </label>
              <select
                name="level"
                value={formData.level}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              >
                <option value="Beginner">Beginner</option>
                <option value="Intermediate">Intermediate</option>
                <option value="Advanced">Advanced</option>
                <option value="Expert">Expert</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Thème
              </label>
              <select
                name="theme"
                value={formData.theme}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              >
                <option value="Leadership">Leadership</option>
                <option value="Strategy">Strategy</option>
                <option value="Finance">Finance</option>
                <option value="Marketing">Marketing</option>
                <option value="Operations">Operations</option>
                <option value="Innovation">Innovation</option>
                <option value="Digital Transformation">Digital Transformation</option>
                <option value="Sustainability">Sustainability</option>
                <option value="Global Business">Global Business</option>
              </select>
            </div>
          </div>

          {/* Affectation */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Coordinateur *
              </label>
              <select
                name="coordinator"
                value={formData.coordinator}
                onChange={handleChange}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                  errors.coordinator ? 'border-red-500' : 'border-gray-300'
                }`}
              >
                <option value="">Sélectionner un coordinateur</option>
                {professors.map(prof => (
                  <option key={prof._id} value={prof._id}>
                    {prof.user ? `${prof.user.firstName} ${prof.user.lastName}` : 'Nom non disponible'} - {prof.department}
                  </option>
                ))}
              </select>
              {errors.coordinator && <p className="mt-1 text-sm text-red-600">{errors.coordinator}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Promotion
              </label>
              <select
                name="promotion"
                value={formData.promotion}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              >
                <option value="EMBA1">EMBA1</option>
                <option value="EMBA2">EMBA2</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Semestre
              </label>
              <select
                name="semester"
                value={formData.semester}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              >
                <option value="Fall">Fall</option>
                <option value="Spring">Spring</option>
                <option value="Summer">Summer</option>
              </select>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Annuler
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
            >
              {isLoading ? 'Enregistrement...' : (module ? 'Modifier' : 'Créer')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ModuleFormModal;
