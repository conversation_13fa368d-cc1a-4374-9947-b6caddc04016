import React, { useState, useEffect } from 'react';
import { XMarkIcon, ExclamationTriangleIcon, PlusIcon, TrashIcon } from '@heroicons/react/24/outline';

const NewCourseAssignmentModal = ({ isOpen, onClose, onSubmit, course = null, professors = [], isLoading = false }) => {
  const [formData, setFormData] = useState({
    instructors: [{ professorId: '', role: 'primary' }],
    classes: [''],
    sessions: [{
      date: '',
      startTime: '09:00',
      endTime: '12:15',
      location: {
        building: 'Bâtiment A',
        room: 'Salle 101',
        capacity: 30
      },
      notes: ''
    }]
  });

  const [errors, setErrors] = useState({});
  const [conflicts, setConflicts] = useState([]);

  // Classes disponibles par promotion
  const classOptions = {
    EMBA1: ['EMBA1A', 'EMBA1B', 'EMBA1C', 'EMBA1D'],
    EMBA2: ['EMBA2A', 'EMBA2B', 'EMBA2C', 'EMBA2D']
  };

  // Créneaux horaires prédéfinis pour faciliter la sélection
  const timeSlotPresets = [
    { startTime: '09:00', endTime: '12:15', label: 'Matinée (9h-12h15)' },
    { startTime: '13:30', endTime: '16:45', label: 'Après-midi (13h30-16h45)' },
    { startTime: '14:30', endTime: '18:30', label: 'Vendredi (14h30-18h30)' },
    { startTime: '09:00', endTime: '17:00', label: 'Samedi (9h-17h)' }
  ];

  useEffect(() => {
    if (course && isOpen) {
      // Réinitialiser le formulaire pour une nouvelle affectation
      setFormData({
        instructors: [{ professorId: '', role: 'primary' }],
        classes: [''],
        sessions: [{
          date: '',
          startTime: '09:00',
          endTime: '12:15',
          location: {
            building: 'Bâtiment A',
            room: 'Salle 101',
            capacity: 30
          },
          notes: ''
        }]
      });
    }
    setErrors({});
    setConflicts([]);
  }, [course, isOpen]);

  const addInstructor = () => {
    setFormData(prev => ({
      ...prev,
      instructors: [...prev.instructors, { professorId: '', role: 'secondary' }]
    }));
  };

  const removeInstructor = (index) => {
    if (formData.instructors.length > 1) {
      setFormData(prev => ({
        ...prev,
        instructors: prev.instructors.filter((_, i) => i !== index)
      }));
    }
  };

  const updateInstructor = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      instructors: prev.instructors.map((instructor, i) =>
        i === index ? { ...instructor, [field]: value } : instructor
      )
    }));
  };

  const addClass = () => {
    setFormData(prev => ({
      ...prev,
      classes: [...prev.classes, '']
    }));
  };

  const removeClass = (index) => {
    if (formData.classes.length > 1) {
      setFormData(prev => ({
        ...prev,
        classes: prev.classes.filter((_, i) => i !== index)
      }));
    }
  };

  const updateClass = (index, value) => {
    setFormData(prev => ({
      ...prev,
      classes: prev.classes.map((className, i) =>
        i === index ? value : className
      )
    }));
  };

  // Nouvelles fonctions pour gérer les séances
  const addSession = () => {
    setFormData(prev => ({
      ...prev,
      sessions: [...prev.sessions, {
        date: '',
        startTime: '09:00',
        endTime: '12:15',
        location: {
          building: 'Bâtiment A',
          room: 'Salle 101',
          capacity: 30
        },
        notes: ''
      }]
    }));
  };

  const removeSession = (index) => {
    if (formData.sessions.length > 1) {
      setFormData(prev => ({
        ...prev,
        sessions: prev.sessions.filter((_, i) => i !== index)
      }));
    }
  };

  const updateSession = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      sessions: prev.sessions.map((session, i) =>
        i === index ? { ...session, [field]: value } : session
      )
    }));
  };

  const updateSessionLocation = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      sessions: prev.sessions.map((session, i) =>
        i === index ? {
          ...session,
          location: { ...session.location, [field]: value }
        } : session
      )
    }));
  };

  const applyTimeSlotPreset = (sessionIndex, preset) => {
    updateSession(sessionIndex, 'startTime', preset.startTime);
    updateSession(sessionIndex, 'endTime', preset.endTime);
  };

  const validateForm = () => {
    const newErrors = {};

    // Valider les professeurs
    formData.instructors.forEach((instructor, index) => {
      if (!instructor.professorId) {
        newErrors[`instructor_${index}`] = 'Professeur requis';
      }
    });

    // Valider les classes
    formData.classes.forEach((className, index) => {
      if (!className) {
        newErrors[`class_${index}`] = 'Classe requise';
      }
    });

    // Valider les séances
    formData.sessions.forEach((session, index) => {
      if (!session.date) {
        newErrors[`session_${index}_date`] = 'Date de la séance requise';
      }
      if (!session.startTime) {
        newErrors[`session_${index}_startTime`] = 'Heure de début requise';
      }
      if (!session.endTime) {
        newErrors[`session_${index}_endTime`] = 'Heure de fin requise';
      }
      if (session.startTime && session.endTime && session.startTime >= session.endTime) {
        newErrors[`session_${index}_endTime`] = 'L\'heure de fin doit être après l\'heure de début';
      }

      // Vérifier que la date n'est pas dans le passé
      if (session.date) {
        const sessionDate = new Date(session.date);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        if (sessionDate < today) {
          newErrors[`session_${index}_date`] = 'La date ne peut pas être dans le passé';
        }
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (validateForm()) {
      try {
        const submitData = {
          instructors: formData.instructors,
          classes: formData.classes,
          sessions: formData.sessions.map(session => ({
            date: session.date,
            startTime: session.startTime,
            endTime: session.endTime,
            location: session.location,
            notes: session.notes
          }))
        };

        await onSubmit(submitData);
        setConflicts([]);
      } catch (error) {
        if (error.conflicts) {
          setConflicts(error.conflicts);
        }
      }
    }
  };

  if (!isOpen || !course) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Nouvelle affectation : {course.title}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* Affichage des conflits */}
        {conflicts.length > 0 && (
          <div className="p-6 bg-red-50 dark:bg-red-900/20 border-b border-red-200 dark:border-red-800">
            <div className="flex items-start">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400 mt-0.5 mr-3" />
              <div>
                <h4 className="text-sm font-medium text-red-800 dark:text-red-200">
                  Conflits d'horaires détectés
                </h4>
                <ul className="mt-2 text-sm text-red-700 dark:text-red-300">
                  {conflicts.map((conflict, index) => (
                    <li key={index} className="mt-1">• {conflict.message}</li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Informations du cours */}
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Informations du cours</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-500 dark:text-gray-400">Code:</span>
                <span className="ml-2 font-medium text-gray-900 dark:text-white">{course.courseCode}</span>
              </div>
              <div>
                <span className="text-gray-500 dark:text-gray-400">Promotion:</span>
                <span className="ml-2 font-medium text-gray-900 dark:text-white">{course.promotion}</span>
              </div>
              <div>
                <span className="text-gray-500 dark:text-gray-400">Crédits:</span>
                <span className="ml-2 font-medium text-gray-900 dark:text-white">{course.creditHours}</span>
              </div>
              <div>
                <span className="text-gray-500 dark:text-gray-400">Semestre:</span>
                <span className="ml-2 font-medium text-gray-900 dark:text-white">{course.semester}</span>
              </div>
            </div>
          </div>

          {/* Professeurs */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white">Professeurs</h4>
              <button
                type="button"
                onClick={addInstructor}
                className="flex items-center px-3 py-1 text-sm bg-primary-600 text-white rounded hover:bg-primary-700"
              >
                <PlusIcon className="h-4 w-4 mr-1" />
                Ajouter professeur
              </button>
            </div>

            {formData.instructors.map((instructor, index) => (
              <div key={index} className="flex items-center space-x-4 mb-3">
                <div className="flex-1">
                  <select
                    value={instructor.professorId}
                    onChange={(e) => updateInstructor(index, 'professorId', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                      errors[`instructor_${index}`] ? 'border-red-500' : 'border-gray-300'
                    }`}
                  >
                    <option value="">Sélectionner un professeur</option>
                    {professors.map(prof => (
                      <option key={prof._id} value={prof._id}>
                        {prof.user ? `${prof.user.firstName} ${prof.user.lastName}` : 'Nom non disponible'} - {prof.department}
                      </option>
                    ))}
                  </select>
                  {errors[`instructor_${index}`] && (
                    <p className="mt-1 text-sm text-red-600">{errors[`instructor_${index}`]}</p>
                  )}
                </div>

                <div className="w-32">
                  <select
                    value={instructor.role}
                    onChange={(e) => updateInstructor(index, 'role', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  >
                    <option value="primary">Principal</option>
                    <option value="secondary">Secondaire</option>
                    <option value="guest">Invité</option>
                  </select>
                </div>

                {formData.instructors.length > 1 && (
                  <button
                    type="button"
                    onClick={() => removeInstructor(index)}
                    className="text-red-600 hover:text-red-800"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </button>
                )}
              </div>
            ))}
          </div>

          {/* Classes */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white">Classes</h4>
              <button
                type="button"
                onClick={addClass}
                className="flex items-center px-3 py-1 text-sm bg-primary-600 text-white rounded hover:bg-primary-700"
              >
                <PlusIcon className="h-4 w-4 mr-1" />
                Ajouter classe
              </button>
            </div>

            {formData.classes.map((className, index) => (
              <div key={index} className="flex items-center space-x-4 mb-3">
                <div className="flex-1">
                  <select
                    value={className}
                    onChange={(e) => updateClass(index, e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                      errors[`class_${index}`] ? 'border-red-500' : 'border-gray-300'
                    }`}
                  >
                    <option value="">Sélectionner une classe</option>
                    {course.promotion && classOptions[course.promotion]?.map(cls => (
                      <option key={cls} value={cls}>{cls}</option>
                    ))}
                  </select>
                  {errors[`class_${index}`] && (
                    <p className="mt-1 text-sm text-red-600">{errors[`class_${index}`]}</p>
                  )}
                </div>

                {formData.classes.length > 1 && (
                  <button
                    type="button"
                    onClick={() => removeClass(index)}
                    className="text-red-600 hover:text-red-800"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </button>
                )}
              </div>
            ))}
          </div>

          {/* Séances */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white">Séances du cours</h4>
              <button
                type="button"
                onClick={addSession}
                className="flex items-center px-3 py-1 text-sm bg-primary-600 text-white rounded hover:bg-primary-700"
              >
                <PlusIcon className="h-4 w-4 mr-1" />
                Ajouter séance
              </button>
            </div>

            {formData.sessions.map((session, index) => (
              <div key={index} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4 mb-4">
                <div className="flex items-center justify-between mb-3">
                  <h5 className="text-sm font-medium text-gray-900 dark:text-white">
                    Séance {index + 1}
                  </h5>
                  {formData.sessions.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeSession(index)}
                      className="text-red-600 hover:text-red-800"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {/* Date */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Date *
                    </label>
                    <input
                      type="date"
                      value={session.date}
                      onChange={(e) => updateSession(index, 'date', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                        errors[`session_${index}_date`] ? 'border-red-500' : 'border-gray-300'
                      }`}
                    />
                    {errors[`session_${index}_date`] && (
                      <p className="mt-1 text-xs text-red-600">{errors[`session_${index}_date`]}</p>
                    )}
                  </div>

                  {/* Heure de début */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Début *
                    </label>
                    <input
                      type="time"
                      value={session.startTime}
                      onChange={(e) => updateSession(index, 'startTime', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                        errors[`session_${index}_startTime`] ? 'border-red-500' : 'border-gray-300'
                      }`}
                    />
                    {errors[`session_${index}_startTime`] && (
                      <p className="mt-1 text-xs text-red-600">{errors[`session_${index}_startTime`]}</p>
                    )}
                  </div>

                  {/* Heure de fin */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Fin *
                    </label>
                    <input
                      type="time"
                      value={session.endTime}
                      onChange={(e) => updateSession(index, 'endTime', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                        errors[`session_${index}_endTime`] ? 'border-red-500' : 'border-gray-300'
                      }`}
                    />
                    {errors[`session_${index}_endTime`] && (
                      <p className="mt-1 text-xs text-red-600">{errors[`session_${index}_endTime`]}</p>
                    )}
                  </div>
                </div>

                {/* Créneaux prédéfinis */}
                <div className="mt-3">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Créneaux prédéfinis
                  </label>
                  <div className="flex flex-wrap gap-2">
                    {timeSlotPresets.map((preset, presetIndex) => (
                      <button
                        key={presetIndex}
                        type="button"
                        onClick={() => applyTimeSlotPreset(index, preset)}
                        className="px-3 py-1 text-xs bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-500"
                      >
                        {preset.label}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Lieu et notes */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Salle
                    </label>
                    <input
                      type="text"
                      value={session.location.room}
                      onChange={(e) => updateSessionLocation(index, 'room', e.target.value)}
                      placeholder="Salle 101"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Notes
                    </label>
                    <input
                      type="text"
                      value={session.notes}
                      onChange={(e) => updateSession(index, 'notes', e.target.value)}
                      placeholder="Notes optionnelles"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Annuler
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
            >
              {isLoading ? 'Création...' : 'Créer l\'affectation'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default NewCourseAssignmentModal;
