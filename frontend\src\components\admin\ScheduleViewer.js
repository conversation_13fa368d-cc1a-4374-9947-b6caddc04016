import React, { useState, useEffect } from 'react';
import { CalendarIcon, ClockIcon, MapPinIcon, UserIcon } from '@heroicons/react/24/outline';

const ScheduleViewer = ({ type = 'class', targetId, targetName }) => {
  const [schedule, setSchedule] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedWeek, setSelectedWeek] = useState(new Date());

  const daysOfWeek = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  const dayNames = {
    'Monday': 'Lundi',
    'Tuesday': 'Mardi', 
    'Wednesday': 'Mercredi',
    'Thursday': 'Jeudi',
    'Friday': 'Vendredi',
    'Saturday': 'Samedi'
  };

  useEffect(() => {
    if (targetId) {
      fetchSchedule();
    }
  }, [targetId, selectedWeek, type]);

  const fetchSchedule = async () => {
    try {
      setLoading(true);
      
      // Calculer les dates de début et fin de semaine
      const startOfWeek = new Date(selectedWeek);
      startOfWeek.setDate(selectedWeek.getDate() - selectedWeek.getDay() + 1); // Lundi
      const endOfWeek = new Date(startOfWeek);
      endOfWeek.setDate(startOfWeek.getDate() + 6); // Dimanche
      
      const endpoint = type === 'class' 
        ? `/api/courses/schedule/class/${targetId}`
        : `/api/courses/schedule/professor/${targetId}`;
      
      const response = await fetch(`http://localhost:5000${endpoint}?startDate=${startOfWeek.toISOString()}&endDate=${endOfWeek.toISOString()}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setSchedule(data.schedule || []);
      } else {
        console.error('Erreur lors du chargement du planning');
        setSchedule([]);
      }
    } catch (error) {
      console.error('Erreur:', error);
      setSchedule([]);
    } finally {
      setLoading(false);
    }
  };

  const getScheduleForDay = (day) => {
    return schedule
      .filter(item => item.dayOfWeek === day)
      .sort((a, b) => a.startTime.localeCompare(b.startTime));
  };

  const formatTime = (time) => {
    return time.substring(0, 5); // HH:MM
  };

  const getWeekRange = () => {
    const startOfWeek = new Date(selectedWeek);
    startOfWeek.setDate(selectedWeek.getDate() - selectedWeek.getDay() + 1);
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);
    
    return `${startOfWeek.toLocaleDateString('fr-FR')} - ${endOfWeek.toLocaleDateString('fr-FR')}`;
  };

  const navigateWeek = (direction) => {
    const newDate = new Date(selectedWeek);
    newDate.setDate(selectedWeek.getDate() + (direction * 7));
    setSelectedWeek(newDate);
  };

  if (!targetId) {
    return (
      <div className="text-center py-8 text-gray-500 dark:text-gray-400">
        Sélectionnez une {type === 'class' ? 'classe' : 'professeur'} pour voir le planning
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
      {/* En-tête */}
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Planning - {targetName}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {getWeekRange()}
            </p>
          </div>
          
          {/* Navigation semaine */}
          <div className="flex items-center space-x-2">
            <button
              onClick={() => navigateWeek(-1)}
              className="px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600"
            >
              ← Semaine précédente
            </button>
            <button
              onClick={() => setSelectedWeek(new Date())}
              className="px-3 py-1 text-sm bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300 rounded hover:bg-primary-200 dark:hover:bg-primary-800"
            >
              Aujourd'hui
            </button>
            <button
              onClick={() => navigateWeek(1)}
              className="px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600"
            >
              Semaine suivante →
            </button>
          </div>
        </div>
      </div>

      {/* Planning */}
      <div className="p-6">
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
            <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">Chargement du planning...</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {daysOfWeek.map(day => {
              const daySchedule = getScheduleForDay(day);
              
              return (
                <div key={day} className="border border-gray-200 dark:border-gray-600 rounded-lg">
                  <div className="bg-gray-50 dark:bg-gray-700 px-4 py-2 border-b border-gray-200 dark:border-gray-600">
                    <h4 className="font-medium text-gray-900 dark:text-white">
                      {dayNames[day]}
                    </h4>
                  </div>
                  
                  <div className="p-4 space-y-3">
                    {daySchedule.length === 0 ? (
                      <p className="text-sm text-gray-500 dark:text-gray-400 italic">
                        Aucun cours
                      </p>
                    ) : (
                      daySchedule.map((item, index) => (
                        <div key={index} className="bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-800 rounded-lg p-3">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <h5 className="font-medium text-primary-900 dark:text-primary-100 text-sm">
                                {item.course?.title || item.title}
                              </h5>
                              <p className="text-xs text-primary-700 dark:text-primary-300 mt-1">
                                {item.course?.courseCode}
                              </p>
                            </div>
                          </div>
                          
                          <div className="mt-2 space-y-1">
                            <div className="flex items-center text-xs text-gray-600 dark:text-gray-400">
                              <ClockIcon className="h-3 w-3 mr-1" />
                              {formatTime(item.startTime)} - {formatTime(item.endTime)}
                            </div>
                            
                            {item.location?.room && (
                              <div className="flex items-center text-xs text-gray-600 dark:text-gray-400">
                                <MapPinIcon className="h-3 w-3 mr-1" />
                                {item.location.room}
                              </div>
                            )}
                            
                            {type === 'class' && item.instructor && (
                              <div className="flex items-center text-xs text-gray-600 dark:text-gray-400">
                                <UserIcon className="h-3 w-3 mr-1" />
                                {item.instructor.user ? 
                                  `${item.instructor.user.firstName} ${item.instructor.user.lastName}` :
                                  'Professeur'
                                }
                              </div>
                            )}
                            
                            {type === 'professor' && item.targetAudience?.classes && (
                              <div className="flex items-center text-xs text-gray-600 dark:text-gray-400">
                                <UserIcon className="h-3 w-3 mr-1" />
                                {item.targetAudience.classes.join(', ')}
                              </div>
                            )}
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
      
      {/* Statistiques */}
      {schedule.length > 0 && (
        <div className="px-6 py-4 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600 dark:text-gray-400">
              Total: {schedule.length} cours cette semaine
            </span>
            <span className="text-gray-600 dark:text-gray-400">
              {Math.round(schedule.reduce((total, item) => {
                const start = new Date(`2000-01-01T${item.startTime}:00`);
                const end = new Date(`2000-01-01T${item.endTime}:00`);
                return total + (end - start) / (1000 * 60 * 60);
              }, 0))} heures de cours
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default ScheduleViewer;
