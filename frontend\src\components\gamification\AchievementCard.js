import React from 'react';
import { cn } from '../../utils/cn';

const AchievementCard = ({ achievement, className }) => {
  const progressPercentage = (achievement.progress / achievement.maxProgress) * 100;
  
  const getCategoryColor = (category) => {
    switch (category) {
      case 'academic':
        return 'from-blue-500 to-blue-600';
      case 'attendance':
        return 'from-green-500 to-green-600';
      case 'performance':
        return 'from-purple-500 to-purple-600';
      default:
        return 'from-gray-500 to-gray-600';
    }
  };

  const getCategoryBg = (category) => {
    switch (category) {
      case 'academic':
        return 'bg-blue-50 dark:bg-blue-900/20';
      case 'attendance':
        return 'bg-green-50 dark:bg-green-900/20';
      case 'performance':
        return 'bg-purple-50 dark:bg-purple-900/20';
      default:
        return 'bg-gray-50 dark:bg-gray-900/20';
    }
  };

  return (
    <div className={cn('bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-200', className)}>
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className={cn('w-12 h-12 rounded-lg flex items-center justify-center text-2xl', getCategoryBg(achievement.category))}>
            {achievement.icon}
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-white">
              {achievement.title}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {achievement.description}
            </p>
          </div>
        </div>
        <div className="text-right">
          <div className="text-lg font-bold text-gray-900 dark:text-white">
            {Math.round(progressPercentage)}%
          </div>
        </div>
      </div>

      <div className="space-y-2">
        <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400">
          <span>Progression</span>
          <span>{achievement.progress}/{achievement.maxProgress}</span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div 
            className={cn('h-2 rounded-full transition-all duration-500 ease-out bg-gradient-to-r', getCategoryColor(achievement.category))}
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
      </div>
    </div>
  );
};

export default AchievementCard;
