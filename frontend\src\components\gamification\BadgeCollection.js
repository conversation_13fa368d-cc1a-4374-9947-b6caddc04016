import React from 'react';
import { cn } from '../../utils/cn';

const BadgeCollection = ({ badges, className }) => {
  return (
    <div className={cn('bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg', className)}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Badges Obtenus
        </h3>
        <span className="text-sm text-gray-500 dark:text-gray-400">
          {badges.length} badge{badges.length !== 1 ? 's' : ''}
        </span>
      </div>

      {badges.length === 0 ? (
        <div className="text-center py-8">
          <div className="text-4xl mb-2">🏆</div>
          <p className="text-gray-500 dark:text-gray-400">
            Continuez vos efforts pour débloquer des badges !
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
          {badges.map((badge) => (
            <div
              key={badge.id}
              className="group relative bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 rounded-lg p-4 text-center hover:shadow-md transition-all duration-200 cursor-pointer"
            >
              <div className="text-3xl mb-2">{badge.icon}</div>
              <h4 className="font-medium text-gray-900 dark:text-white text-sm mb-1">
                {badge.name}
              </h4>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                {badge.description}
              </p>
              
              {/* Tooltip */}
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
                {badge.description}
                <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default BadgeCollection;
