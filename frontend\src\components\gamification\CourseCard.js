import React from 'react';
import { cn } from '../../utils/cn';
import { ClockIcon, UserIcon, CalendarIcon } from '@heroicons/react/24/outline';

const CourseCard = ({ course, className }) => {
  const getGradeColor = (grade) => {
    if (!grade) return 'text-gray-500';
    const letter = grade.charAt(0);
    switch (letter) {
      case 'A':
        return 'text-green-600 dark:text-green-400';
      case 'B':
        return 'text-blue-600 dark:text-blue-400';
      case 'C':
        return 'text-yellow-600 dark:text-yellow-400';
      case 'D':
        return 'text-orange-600 dark:text-orange-400';
      case 'F':
        return 'text-red-600 dark:text-red-400';
      default:
        return 'text-gray-500';
    }
  };

  const getProgressColor = (progress) => {
    if (progress >= 80) return 'from-green-500 to-green-600';
    if (progress >= 60) return 'from-blue-500 to-blue-600';
    if (progress >= 40) return 'from-yellow-500 to-yellow-600';
    return 'from-red-500 to-red-600';
  };

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('fr-FR', {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className={cn('bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-200 border border-gray-200 dark:border-gray-700', className)}>
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div>
          <div className="flex items-center space-x-2 mb-1">
            <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
              {course.code}
            </span>
            <span className="text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 px-2 py-1 rounded-full">
              {course.credits} crédits
            </span>
          </div>
          <h3 className="font-semibold text-gray-900 dark:text-white text-lg">
            {course.title}
          </h3>
        </div>
        {course.grade && (
          <div className={cn('text-2xl font-bold', getGradeColor(course.grade))}>
            {course.grade}
          </div>
        )}
      </div>

      {/* Instructor and Schedule */}
      <div className="space-y-2 mb-4">
        <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
          <UserIcon className="w-4 h-4 mr-2" />
          {course.instructor}
        </div>
        <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
          <ClockIcon className="w-4 h-4 mr-2" />
          {course.schedule}
        </div>
        {course.nextSession && (
          <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
            <CalendarIcon className="w-4 h-4 mr-2" />
            Prochaine session: {formatDate(course.nextSession)}
          </div>
        )}
      </div>

      {/* Progress */}
      <div className="mb-4">
        <div className="flex justify-between text-sm mb-2">
          <span className="text-gray-600 dark:text-gray-400">Progression</span>
          <span className="font-medium text-gray-900 dark:text-white">{course.progress}%</span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div 
            className={cn('h-2 rounded-full transition-all duration-500 ease-out bg-gradient-to-r', getProgressColor(course.progress))}
            style={{ width: `${course.progress}%` }}
          />
        </div>
      </div>

      {/* Assignments */}
      {course.assignments && course.assignments.length > 0 && (
        <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
            Devoirs à venir
          </h4>
          <div className="space-y-2">
            {course.assignments.slice(0, 2).map((assignment) => (
              <div key={assignment.id} className="flex items-center justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400 truncate">
                  {assignment.title}
                </span>
                <div className="flex items-center space-x-2">
                  <span className="text-xs text-gray-500">
                    {formatDate(assignment.dueDate)}
                  </span>
                  <span className={cn(
                    'px-2 py-1 rounded-full text-xs font-medium',
                    assignment.status === 'pending' ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400' :
                    assignment.status === 'in_progress' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400' :
                    'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                  )}>
                    {assignment.status === 'pending' ? 'À faire' :
                     assignment.status === 'in_progress' ? 'En cours' : 'Terminé'}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default CourseCard;
