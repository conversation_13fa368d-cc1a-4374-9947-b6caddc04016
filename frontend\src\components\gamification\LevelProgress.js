import React from 'react';
import { cn } from '../../utils/cn';

const LevelProgress = ({ level, xpCurrent, xpRequired, totalPoints, className }) => {
  const progressPercentage = (xpCurrent / xpRequired) * 100;

  return (
    <div className={cn('bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl p-6 text-white', className)}>
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-2xl font-bold">Niveau {level}</h3>
          <p className="text-purple-100">Total: {totalPoints.toLocaleString()} points</p>
        </div>
        <div className="text-right">
          <div className="text-3xl font-bold">🎯</div>
        </div>
      </div>
      
      <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span>XP: {xpCurrent}/{xpRequired}</span>
          <span>{Math.round(progressPercentage)}%</span>
        </div>
        <div className="w-full bg-purple-400 bg-opacity-30 rounded-full h-3">
          <div 
            className="bg-white rounded-full h-3 transition-all duration-500 ease-out"
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
        <p className="text-xs text-purple-100">
          {xpRequired - xpCurrent} XP jusqu'au niveau {level + 1}
        </p>
      </div>
    </div>
  );
};

export default LevelProgress;
