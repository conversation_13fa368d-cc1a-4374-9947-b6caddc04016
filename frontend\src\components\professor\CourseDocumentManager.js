import React, { useState, useEffect } from 'react';
import {
  DocumentTextIcon,
  CloudArrowUpIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  ArrowDownTrayIcon,
  FolderIcon,
  PlusIcon
} from '@heroicons/react/24/outline';
import { cn } from '../../utils/cn';
import DocumentUploadModal from './DocumentUploadModal';
import DocumentEditModal from './DocumentEditModal';

const CourseDocumentManager = ({ courseId, courseName }) => {
  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState(null);
  const [showEditModal, setShowEditModal] = useState(false);

  // Charger les documents du cours
  const fetchDocuments = async () => {
    try {
      const response = await fetch(`http://localhost:5000/api/courses/${courseId}/documents`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setDocuments(data.documents);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des documents:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (courseId) {
      fetchDocuments();
    }
  }, [courseId]);

  const handleFileUpload = async (formData) => {
    setUploading(true);
    try {
      const response = await fetch(`http://localhost:5000/api/courses/${courseId}/documents`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: formData
      });

      if (response.ok) {
        const data = await response.json();
        setDocuments(prev => [...prev, data.document]);
        setShowUploadModal(false);
        alert('Document uploadé avec succès');
      } else {
        const error = await response.json();
        alert(error.message || 'Erreur lors de l\'upload');
      }
    } catch (error) {
      console.error('Erreur lors de l\'upload:', error);
      alert('Erreur lors de l\'upload du document');
    } finally {
      setUploading(false);
    }
  };

  const handleDocumentUpdate = async (documentId, updateData) => {
    try {
      const response = await fetch(`http://localhost:5000/api/courses/${courseId}/documents/${documentId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(updateData)
      });

      if (response.ok) {
        const data = await response.json();
        setDocuments(prev => prev.map(doc => 
          doc._id === documentId ? data.document : doc
        ));
        setShowEditModal(false);
        setSelectedDocument(null);
        alert('Document mis à jour avec succès');
      } else {
        const error = await response.json();
        alert(error.message || 'Erreur lors de la mise à jour');
      }
    } catch (error) {
      console.error('Erreur lors de la mise à jour:', error);
      alert('Erreur lors de la mise à jour du document');
    }
  };

  const handleDocumentDelete = async (documentId) => {
    if (!window.confirm('Êtes-vous sûr de vouloir supprimer ce document ?')) {
      return;
    }

    try {
      const response = await fetch(`http://localhost:5000/api/courses/${courseId}/documents/${documentId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        setDocuments(prev => prev.filter(doc => doc._id !== documentId));
        alert('Document supprimé avec succès');
      } else {
        alert('Erreur lors de la suppression du document');
      }
    } catch (error) {
      console.error('Erreur lors de la suppression:', error);
      alert('Erreur lors de la suppression du document');
    }
  };

  const handleDocumentDownload = async (documentId, filename) => {
    try {
      const response = await fetch(`http://localhost:5000/api/courses/${courseId}/documents/${documentId}/download`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        alert('Erreur lors du téléchargement du document');
      }
    } catch (error) {
      console.error('Erreur lors du téléchargement:', error);
      alert('Erreur lors du téléchargement du document');
    }
  };

  const getFileIcon = (mimeType) => {
    if (mimeType?.includes('pdf')) {
      return '📄';
    } else if (mimeType?.includes('word') || mimeType?.includes('document')) {
      return '📝';
    } else if (mimeType?.includes('powerpoint') || mimeType?.includes('presentation')) {
      return '📊';
    } else if (mimeType?.includes('image')) {
      return '🖼️';
    }
    return '📎';
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-32">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Documents du Cours
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {courseName} • {documents.length} document{documents.length !== 1 ? 's' : ''}
          </p>
        </div>
        <button
          onClick={() => setShowUploadModal(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700"
        >
          <CloudArrowUpIcon className="h-4 w-4 mr-2" />
          Ajouter Document
        </button>
      </div>

      {/* Liste des documents */}
      {documents.length === 0 ? (
        <div className="text-center py-12 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <FolderIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
            Aucun document
          </h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Commencez par ajouter des documents pour ce cours.
          </p>
          <div className="mt-6">
            <button
              onClick={() => setShowUploadModal(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700"
            >
              <CloudArrowUpIcon className="h-4 w-4 mr-2" />
              Ajouter Document
            </button>
          </div>
        </div>
      ) : (
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {documents.map((document) => (
              <div key={document._id} className="p-6 hover:bg-gray-50 dark:hover:bg-gray-700">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="text-2xl">
                      {getFileIcon(document.mimeType)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {document.title}
                      </h4>
                      <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                        <span>{document.originalName}</span>
                        <span>•</span>
                        <span>{formatFileSize(document.fileSize)}</span>
                        <span>•</span>
                        <span>{new Date(document.createdAt).toLocaleDateString('fr-FR')}</span>
                      </div>
                      {document.description && (
                        <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                          {document.description}
                        </p>
                      )}
                      <div className="flex items-center space-x-4 mt-2">
                        <span className={cn(
                          "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",
                          document.documentType === 'course_material' 
                            ? "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                            : document.documentType === 'syllabus'
                            ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                            : "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
                        )}>
                          {document.documentType === 'course_material' ? 'Matériel de cours' :
                           document.documentType === 'syllabus' ? 'Syllabus' :
                           document.documentType === 'assignment' ? 'Devoir' :
                           document.documentType === 'exam' ? 'Examen' :
                           document.documentType === 'reference' ? 'Référence' : 'Autre'}
                        </span>
                        {!document.visibleToStudents && (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                            Privé
                          </span>
                        )}
                        {document.downloadCount > 0 && (
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            {document.downloadCount} téléchargement{document.downloadCount !== 1 ? 's' : ''}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleDocumentDownload(document._id, document.originalName)}
                      className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                      title="Télécharger"
                    >
                      <ArrowDownTrayIcon className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => {
                        setSelectedDocument(document);
                        setShowEditModal(true);
                      }}
                      className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                      title="Modifier"
                    >
                      <PencilIcon className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleDocumentDelete(document._id)}
                      className="p-2 text-red-400 hover:text-red-600"
                      title="Supprimer"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Modal d'upload */}
      {showUploadModal && (
        <DocumentUploadModal
          onUpload={handleFileUpload}
          onClose={() => setShowUploadModal(false)}
          uploading={uploading}
        />
      )}

      {/* Modal d'édition */}
      {showEditModal && selectedDocument && (
        <DocumentEditModal
          document={selectedDocument}
          onUpdate={handleDocumentUpdate}
          onClose={() => {
            setShowEditModal(false);
            setSelectedDocument(null);
          }}
        />
      )}
    </div>
  );
};

export default CourseDocumentManager;
