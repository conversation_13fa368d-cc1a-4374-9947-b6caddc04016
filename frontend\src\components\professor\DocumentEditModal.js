import React, { useState, useEffect } from 'react';
import {
  XMarkIcon,
  CheckIcon
} from '@heroicons/react/24/outline';

const DocumentEditModal = ({ document, onUpdate, onClose }) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    documentType: 'course_material',
    visibleToStudents: true,
    downloadable: true,
    displayOrder: 0,
    tags: ''
  });
  const [updating, setUpdating] = useState(false);

  const documentTypes = [
    { value: 'course_material', label: 'Matériel de cours' },
    { value: 'syllabus', label: 'Syllabus' },
    { value: 'assignment', label: 'Devoir' },
    { value: 'exam', label: 'Examen' },
    { value: 'reference', label: 'Référence' },
    { value: 'other', label: 'Autre' }
  ];

  useEffect(() => {
    if (document) {
      setFormData({
        title: document.title || '',
        description: document.description || '',
        documentType: document.documentType || 'course_material',
        visibleToStudents: document.visibleToStudents !== false,
        downloadable: document.downloadable !== false,
        displayOrder: document.displayOrder || 0,
        tags: document.tags ? document.tags.join(', ') : ''
      });
    }
  }, [document]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.title.trim()) {
      alert('Veuillez saisir un titre pour le document');
      return;
    }

    setUpdating(true);
    
    const updateData = {
      title: formData.title.trim(),
      description: formData.description.trim(),
      documentType: formData.documentType,
      visibleToStudents: formData.visibleToStudents,
      downloadable: formData.downloadable,
      displayOrder: formData.displayOrder,
      tags: formData.tags.trim()
    };

    await onUpdate(document._id, updateData);
    setUpdating(false);
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (type) => {
    if (type?.includes('pdf')) return '📄';
    if (type?.includes('word') || type?.includes('document')) return '📝';
    if (type?.includes('powerpoint') || type?.includes('presentation')) return '📊';
    if (type?.includes('image')) return '🖼️';
    return '📎';
  };

  if (!document) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800">
        {/* Header */}
        <div className="flex justify-between items-center pb-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Modifier le Document
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="mt-4 space-y-6">
          {/* Informations du fichier */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <div className="text-2xl">
                {getFileIcon(document.mimeType)}
              </div>
              <div>
                <div className="text-sm font-medium text-gray-900 dark:text-white">
                  {document.originalName}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  {formatFileSize(document.fileSize)} • Uploadé le {new Date(document.createdAt).toLocaleDateString('fr-FR')}
                </div>
                {document.downloadCount > 0 && (
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {document.downloadCount} téléchargement{document.downloadCount !== 1 ? 's' : ''}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Informations du document */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Titre du document *
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                placeholder="Nom du document"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Description
              </label>
              <textarea
                rows={3}
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                placeholder="Description du document (optionnel)"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Type de document
              </label>
              <select
                value={formData.documentType}
                onChange={(e) => handleInputChange('documentType', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
              >
                {documentTypes.map((type) => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Ordre d'affichage
              </label>
              <input
                type="number"
                min="0"
                value={formData.displayOrder}
                onChange={(e) => handleInputChange('displayOrder', parseInt(e.target.value) || 0)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Tags (séparés par des virgules)
              </label>
              <input
                type="text"
                value={formData.tags}
                onChange={(e) => handleInputChange('tags', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                placeholder="cours, chapitre1, important"
              />
            </div>
          </div>

          {/* Options de visibilité */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white">
              Options de visibilité
            </h4>
            <div className="space-y-2">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.visibleToStudents}
                  onChange={(e) => handleInputChange('visibleToStudents', e.target.checked)}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                  Visible par les étudiants
                </span>
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.downloadable}
                  onChange={(e) => handleInputChange('downloadable', e.target.checked)}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                  Téléchargeable par les étudiants
                </span>
              </label>
            </div>
          </div>

          {/* Statistiques */}
          {document.downloadCount > 0 && (
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md p-3">
              <div className="text-sm text-blue-800 dark:text-blue-200">
                <strong>Statistiques:</strong> Ce document a été téléchargé {document.downloadCount} fois
                {document.lastDownloaded && (
                  <span>, dernière fois le {new Date(document.lastDownloaded).toLocaleDateString('fr-FR')}</span>
                )}
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
            >
              Annuler
            </button>
            <button
              type="submit"
              disabled={updating}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {updating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2 inline-block"></div>
                  Mise à jour...
                </>
              ) : (
                <>
                  <CheckIcon className="h-4 w-4 mr-2 inline-block" />
                  Mettre à jour
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default DocumentEditModal;
