import React, { useState, useRef } from 'react';
import {
  XMarkIcon,
  CloudArrowUpIcon,
  DocumentTextIcon,
  CheckIcon
} from '@heroicons/react/24/outline';
import { cn } from '../../utils/cn';

const DocumentUploadModal = ({ onUpload, onClose, uploading }) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    documentType: 'course_material',
    visibleToStudents: true,
    downloadable: true,
    displayOrder: 0,
    tags: ''
  });
  const [selectedFile, setSelectedFile] = useState(null);
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef(null);

  const documentTypes = [
    { value: 'course_material', label: 'Matériel de cours' },
    { value: 'syllabus', label: 'Syllabus' },
    { value: 'assignment', label: 'Devoir' },
    { value: 'exam', label: 'Examen' },
    { value: 'reference', label: 'Réf<PERSON>rence' },
    { value: 'other', label: 'Autre' }
  ];

  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain',
    'image/jpeg',
    'image/jpg',
    'image/png'
  ];

  const maxFileSize = 50 * 1024 * 1024; // 50MB

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleFileSelect = (file) => {
    if (!file) return;

    // Vérifier le type de fichier
    if (!allowedTypes.includes(file.type)) {
      alert('Type de fichier non autorisé. Seuls les PDF, Word, PowerPoint, images et fichiers texte sont acceptés.');
      return;
    }

    // Vérifier la taille du fichier
    if (file.size > maxFileSize) {
      alert('Le fichier est trop volumineux. Taille maximale autorisée : 50MB');
      return;
    }

    setSelectedFile(file);
    
    // Auto-remplir le titre si vide
    if (!formData.title) {
      const fileName = file.name.replace(/\.[^/.]+$/, ''); // Enlever l'extension
      setFormData(prev => ({
        ...prev,
        title: fileName
      }));
    }
  };

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files[0]);
    }
  };

  const handleFileInputChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      handleFileSelect(e.target.files[0]);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!selectedFile) {
      alert('Veuillez sélectionner un fichier');
      return;
    }

    if (!formData.title.trim()) {
      alert('Veuillez saisir un titre pour le document');
      return;
    }

    // Créer FormData pour l'upload
    const uploadData = new FormData();
    uploadData.append('document', selectedFile);
    uploadData.append('title', formData.title.trim());
    uploadData.append('description', formData.description.trim());
    uploadData.append('documentType', formData.documentType);
    uploadData.append('visibleToStudents', formData.visibleToStudents);
    uploadData.append('downloadable', formData.downloadable);
    uploadData.append('displayOrder', formData.displayOrder);
    uploadData.append('tags', formData.tags.trim());

    onUpload(uploadData);
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (type) => {
    if (type?.includes('pdf')) return '📄';
    if (type?.includes('word') || type?.includes('document')) return '📝';
    if (type?.includes('powerpoint') || type?.includes('presentation')) return '📊';
    if (type?.includes('image')) return '🖼️';
    return '📎';
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800">
        {/* Header */}
        <div className="flex justify-between items-center pb-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Ajouter un Document
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="mt-4 space-y-6">
          {/* Zone de drop */}
          <div
            className={cn(
              "relative border-2 border-dashed rounded-lg p-6 text-center hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",
              dragActive 
                ? "border-primary-500 bg-primary-50 dark:bg-primary-900/20" 
                : "border-gray-300 dark:border-gray-600",
              selectedFile && "border-green-500 bg-green-50 dark:bg-green-900/20"
            )}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            <input
              ref={fileInputRef}
              type="file"
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              onChange={handleFileInputChange}
              accept=".pdf,.doc,.docx,.ppt,.pptx,.txt,.jpg,.jpeg,.png"
            />
            
            {selectedFile ? (
              <div className="space-y-2">
                <div className="text-4xl">{getFileIcon(selectedFile.type)}</div>
                <div className="text-sm font-medium text-gray-900 dark:text-white">
                  {selectedFile.name}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  {formatFileSize(selectedFile.size)}
                </div>
                <div className="flex items-center justify-center text-green-600">
                  <CheckIcon className="h-5 w-5 mr-1" />
                  <span className="text-sm">Fichier sélectionné</span>
                </div>
              </div>
            ) : (
              <div className="space-y-2">
                <CloudArrowUpIcon className="mx-auto h-12 w-12 text-gray-400" />
                <div className="text-sm text-gray-600 dark:text-gray-300">
                  <span className="font-medium text-primary-600 hover:text-primary-500">
                    Cliquez pour sélectionner
                  </span>
                  {' '}ou glissez-déposez un fichier
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  PDF, Word, PowerPoint, Images, Texte (max. 50MB)
                </div>
              </div>
            )}
          </div>

          {/* Informations du document */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Titre du document *
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                placeholder="Nom du document"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Description
              </label>
              <textarea
                rows={3}
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                placeholder="Description du document (optionnel)"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Type de document
              </label>
              <select
                value={formData.documentType}
                onChange={(e) => handleInputChange('documentType', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
              >
                {documentTypes.map((type) => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Ordre d'affichage
              </label>
              <input
                type="number"
                min="0"
                value={formData.displayOrder}
                onChange={(e) => handleInputChange('displayOrder', parseInt(e.target.value) || 0)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Tags (séparés par des virgules)
              </label>
              <input
                type="text"
                value={formData.tags}
                onChange={(e) => handleInputChange('tags', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                placeholder="cours, chapitre1, important"
              />
            </div>
          </div>

          {/* Options de visibilité */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white">
              Options de visibilité
            </h4>
            <div className="space-y-2">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.visibleToStudents}
                  onChange={(e) => handleInputChange('visibleToStudents', e.target.checked)}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                  Visible par les étudiants
                </span>
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.downloadable}
                  onChange={(e) => handleInputChange('downloadable', e.target.checked)}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                  Téléchargeable par les étudiants
                </span>
              </label>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
            >
              Annuler
            </button>
            <button
              type="submit"
              disabled={uploading || !selectedFile}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {uploading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2 inline-block"></div>
                  Upload en cours...
                </>
              ) : (
                'Ajouter Document'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default DocumentUploadModal;
