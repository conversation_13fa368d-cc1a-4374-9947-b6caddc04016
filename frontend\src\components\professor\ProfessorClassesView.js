import React, { useState, useEffect } from 'react';
import {
  UserGroupIcon,
  AcademicCapIcon,
  CalendarIcon,
  ClockIcon,
  MapPinIcon,
  UserIcon,
  EnvelopeIcon,
  PhoneIcon,
  BookOpenIcon
} from '@heroicons/react/24/outline';
import { cn } from '../../utils/cn';

const ProfessorClassesView = () => {
  const [professorData, setProfessorData] = useState(null);
  const [classes, setClasses] = useState([]);
  const [schedule, setSchedule] = useState([]);
  const [selectedClass, setSelectedClass] = useState(null);
  const [students, setStudents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('classes');

  // Charger les données du professeur
  const loadProfessorData = async () => {
    try {
      setLoading(true);
      
      // Récupérer les informations du professeur
      const professorResponse = await fetch('http://localhost:5000/api/professors/me', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!professorResponse.ok) {
        throw new Error('Erreur lors du chargement des informations professeur');
      }

      const professorData = await professorResponse.json();
      setProfessorData(professorData.professor);

      // Récupérer l'emploi du temps du professeur
      const scheduleResponse = await fetch(`http://localhost:5000/api/schedule/professor/${professorData.professor._id}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (scheduleResponse.ok) {
        const scheduleData = await scheduleResponse.json();
        setSchedule(scheduleData.schedule || []);
        
        // Extraire les classes uniques
        const uniqueClasses = [];
        const classNames = new Set();
        
        scheduleData.schedule.forEach(item => {
          const classesArray = item.classes.split(', ');
          classesArray.forEach(className => {
            if (!classNames.has(className)) {
              classNames.add(className);
              uniqueClasses.push({
                name: className,
                courses: [item.courseName],
                courseIds: [item.courseId]
              });
            } else {
              // Ajouter le cours à une classe existante
              const existingClass = uniqueClasses.find(c => c.name === className);
              if (existingClass && !existingClass.courses.includes(item.courseName)) {
                existingClass.courses.push(item.courseName);
                existingClass.courseIds.push(item.courseId);
              }
            }
          });
        });
        
        setClasses(uniqueClasses);
        
        // Sélectionner la première classe par défaut
        if (uniqueClasses.length > 0) {
          setSelectedClass(uniqueClasses[0].name);
          await loadClassStudents(uniqueClasses[0].name);
        }
      }

    } catch (error) {
      console.error('Erreur lors du chargement des données professeur:', error);
    } finally {
      setLoading(false);
    }
  };

  // Charger les étudiants d'une classe
  const loadClassStudents = async (className) => {
    try {
      const response = await fetch(`http://localhost:5000/api/students/class/${className}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setStudents(data.students || []);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des étudiants:', error);
      setStudents([]);
    }
  };

  useEffect(() => {
    loadProfessorData();
  }, []);

  const handleClassSelect = async (className) => {
    setSelectedClass(className);
    await loadClassStudents(className);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  const tabs = [
    { id: 'classes', name: 'Mes Classes', icon: UserGroupIcon, count: classes.length },
    { id: 'schedule', name: 'Mon Planning', icon: CalendarIcon, count: schedule.length }
  ];

  return (
    <div className="space-y-6">
      {/* En-tête professeur */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              👨‍🏫 Mes Classes et Planning
            </h2>
            <p className="text-gray-600 dark:text-gray-400">
              {professorData?.academicTitle} {professorData?.user?.firstName} {professorData?.user?.lastName}
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-500">
              {professorData?.department}
            </p>
          </div>
          <div className="text-right">
            <div className="text-sm text-gray-500 dark:text-gray-400">Total Classes</div>
            <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">{classes.length}</div>
          </div>
        </div>
      </div>

      {/* Onglets */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={cn(
                    "flex items-center py-4 px-1 border-b-2 font-medium text-sm",
                    activeTab === tab.id
                      ? "border-primary-500 text-primary-600 dark:text-primary-400"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"
                  )}
                >
                  <Icon className="h-5 w-5 mr-2" />
                  {tab.name}
                  <span className="ml-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 py-0.5 px-2 rounded-full text-xs">
                    {tab.count}
                  </span>
                </button>
              );
            })}
          </nav>
        </div>

        <div className="p-6">
          {/* Onglet Classes */}
          {activeTab === 'classes' && (
            <div className="space-y-6">
              {/* Sélecteur de classe */}
              <div className="flex flex-wrap gap-2">
                {classes.map((classInfo) => (
                  <button
                    key={classInfo.name}
                    onClick={() => handleClassSelect(classInfo.name)}
                    className={cn(
                      "px-4 py-2 rounded-lg font-medium text-sm transition-colors",
                      selectedClass === classInfo.name
                        ? "bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200"
                        : "bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
                    )}
                  >
                    {classInfo.name}
                    <span className="ml-2 text-xs opacity-75">
                      ({classInfo.courses.length} cours)
                    </span>
                  </button>
                ))}
              </div>

              {/* Informations de la classe sélectionnée */}
              {selectedClass && (
                <div className="space-y-4">
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                      Classe {selectedClass}
                    </h3>
                    <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
                      <div className="flex items-center">
                        <UserGroupIcon className="h-4 w-4 mr-1" />
                        {students.length} étudiants
                      </div>
                      <div className="flex items-center">
                        <BookOpenIcon className="h-4 w-4 mr-1" />
                        {classes.find(c => c.name === selectedClass)?.courses.length || 0} cours
                      </div>
                    </div>
                  </div>

                  {/* Liste des étudiants */}
                  <div>
                    <h4 className="text-md font-medium text-gray-900 dark:text-white mb-3">
                      Étudiants de la classe ({students.length})
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {students.map((student) => (
                        <div key={student._id} className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0">
                              {student.user.profilePicture ? (
                                <img
                                  src={student.user.profilePicture}
                                  alt={`${student.user.firstName} ${student.user.lastName}`}
                                  className="h-10 w-10 rounded-full"
                                />
                              ) : (
                                <div className="h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                                  <UserIcon className="h-6 w-6 text-gray-500 dark:text-gray-400" />
                                </div>
                              )}
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                                {student.user.firstName} {student.user.lastName}
                              </p>
                              <p className="text-xs text-gray-500 dark:text-gray-400">
                                {student.studentNumber}
                              </p>
                              <div className="flex items-center space-x-2 mt-1">
                                <EnvelopeIcon className="h-3 w-3 text-gray-400" />
                                <span className="text-xs text-gray-500 dark:text-gray-400 truncate">
                                  {student.user.email}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Onglet Planning */}
          {activeTab === 'schedule' && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Mon Emploi du Temps ({schedule.length} sessions)
              </h3>
              {schedule.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-700">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Jour
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Horaire
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Cours
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Classes
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Salle
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                      {schedule.map((item, index) => (
                        <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                            {item.dayOfWeek}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            {item.startTime} - {item.endTime}
                            <div className="text-xs text-gray-400">{item.duration}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900 dark:text-white">{item.courseName}</div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">{item.courseCode}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex flex-wrap gap-1">
                              {item.classes.split(', ').map((className, idx) => (
                                <span
                                  key={idx}
                                  className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                                >
                                  {className}
                                </span>
                              ))}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            <div className="flex items-center">
                              <MapPinIcon className="h-4 w-4 mr-1" />
                              {item.location}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8">
                  <ClockIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500 dark:text-gray-400">
                    Aucun cours programmé pour le moment
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProfessorClassesView;
