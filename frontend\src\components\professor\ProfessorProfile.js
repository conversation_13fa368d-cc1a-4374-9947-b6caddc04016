import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import {
  UserIcon,
  AcademicCapIcon,
  BuildingOfficeIcon,
  ClockIcon,
  GlobeAltIcon,
  PencilIcon,
  CheckIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

const ProfessorProfile = () => {
  const { user } = useAuth();
  const [professorData, setProfessorData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [editing, setEditing] = useState(false);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState({ type: '', text: '' });

  const [formData, setFormData] = useState({
    bio: '',
    expertise: [],
    consultingAreas: [],
    linkedinUrl: '',
    personalWebsite: '',
    technicalSkills: [],
    languages: [],
    teachingAreas: [],
    preferredTeachingMethods: [],
    canTravelForTeaching: false,
    officeHours: []
  });

  // Charger les données du professeur
  const fetchProfessorData = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/professors/profile', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setProfessorData(data.professor);
        
        // Remplir le formulaire avec les données existantes
        setFormData({
          bio: data.professor.professionalProfile?.bio || '',
          expertise: data.professor.professionalProfile?.expertise || [],
          consultingAreas: data.professor.professionalProfile?.consultingAreas || [],
          linkedinUrl: data.professor.professionalProfile?.linkedinUrl || '',
          personalWebsite: data.professor.professionalProfile?.personalWebsite || '',
          technicalSkills: data.professor.skills?.technicalSkills || [],
          languages: data.professor.skills?.languages || [],
          teachingAreas: data.professor.teachingExperience?.teachingAreas || [],
          preferredTeachingMethods: data.professor.teachingExperience?.preferredTeachingMethods || [],
          canTravelForTeaching: data.professor.availability?.canTravelForTeaching || false,
          officeHours: data.professor.officeHours || []
        });
      }
    } catch (error) {
      console.error('Erreur lors du chargement du profil:', error);
      setMessage({ type: 'error', text: 'Erreur lors du chargement du profil' });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProfessorData();
  }, []);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleArrayChange = (field, value) => {
    const items = value.split(',').map(item => item.trim()).filter(item => item);
    setFormData(prev => ({
      ...prev,
      [field]: items
    }));
  };

  const handleSave = async () => {
    setSaving(true);
    setMessage({ type: '', text: '' });

    try {
      const updateData = {
        'professionalProfile.bio': formData.bio,
        'professionalProfile.expertise': formData.expertise,
        'professionalProfile.consultingAreas': formData.consultingAreas,
        'professionalProfile.linkedinUrl': formData.linkedinUrl,
        'professionalProfile.personalWebsite': formData.personalWebsite,
        'skills.technicalSkills': formData.technicalSkills,
        'skills.languages': formData.languages,
        'teachingExperience.teachingAreas': formData.teachingAreas,
        'teachingExperience.preferredTeachingMethods': formData.preferredTeachingMethods,
        'availability.canTravelForTeaching': formData.canTravelForTeaching,
        'officeHours': formData.officeHours
      };

      const response = await fetch('http://localhost:5000/api/professors/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(updateData)
      });

      const data = await response.json();

      if (data.success) {
        setProfessorData(data.professor);
        setEditing(false);
        setMessage({ type: 'success', text: 'Profil mis à jour avec succès' });
      } else {
        setMessage({ type: 'error', text: data.message });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Erreur lors de la mise à jour du profil' });
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    setEditing(false);
    // Restaurer les données originales
    if (professorData) {
      setFormData({
        bio: professorData.professionalProfile?.bio || '',
        expertise: professorData.professionalProfile?.expertise || [],
        consultingAreas: professorData.professionalProfile?.consultingAreas || [],
        linkedinUrl: professorData.professionalProfile?.linkedinUrl || '',
        personalWebsite: professorData.professionalProfile?.personalWebsite || '',
        technicalSkills: professorData.skills?.technicalSkills || [],
        languages: professorData.skills?.languages || [],
        teachingAreas: professorData.teachingExperience?.teachingAreas || [],
        preferredTeachingMethods: professorData.teachingExperience?.preferredTeachingMethods || [],
        canTravelForTeaching: professorData.availability?.canTravelForTeaching || false,
        officeHours: professorData.officeHours || []
      });
    }
    setMessage({ type: '', text: '' });
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
              Mon Profil Professeur
            </h1>
            {!editing ? (
              <button
                onClick={() => setEditing(true)}
                className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
              >
                <PencilIcon className="h-4 w-4 mr-2" />
                Modifier
              </button>
            ) : (
              <div className="flex space-x-2">
                <button
                  onClick={handleSave}
                  disabled={saving}
                  className="inline-flex items-center px-3 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50"
                >
                  <CheckIcon className="h-4 w-4 mr-2" />
                  {saving ? 'Enregistrement...' : 'Enregistrer'}
                </button>
                <button
                  onClick={handleCancel}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
                >
                  <XMarkIcon className="h-4 w-4 mr-2" />
                  Annuler
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Messages */}
        {message.text && (
          <div className={`mx-6 mt-4 p-4 rounded-lg ${
            message.type === 'success' 
              ? 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-400' 
              : 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400'
          }`}>
            {message.text}
          </div>
        )}

        {/* Informations de base (non modifiables) */}
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="flex items-center">
              <UserIcon className="h-5 w-5 text-gray-400 mr-3" />
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Nom complet</p>
                <p className="text-sm text-gray-900 dark:text-white">
                  {professorData?.academicTitle} {user?.firstName} {user?.lastName}
                </p>
              </div>
            </div>

            <div className="flex items-center">
              <AcademicCapIcon className="h-5 w-5 text-gray-400 mr-3" />
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Rang académique</p>
                <p className="text-sm text-gray-900 dark:text-white">{professorData?.academicRank}</p>
              </div>
            </div>

            <div className="flex items-center">
              <BuildingOfficeIcon className="h-5 w-5 text-gray-400 mr-3" />
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Département</p>
                <p className="text-sm text-gray-900 dark:text-white">{professorData?.department}</p>
              </div>
            </div>

            <div className="flex items-center">
              <ClockIcon className="h-5 w-5 text-gray-400 mr-3" />
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Numéro d'employé</p>
                <p className="text-sm text-gray-900 dark:text-white">{professorData?.employeeNumber}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Profil professionnel */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">
            Profil Professionnel
          </h2>
        </div>
        <div className="p-6 space-y-6">
          {/* Biographie */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Biographie professionnelle
            </label>
            {editing ? (
              <textarea
                name="bio"
                value={formData.bio}
                onChange={handleChange}
                rows={4}
                maxLength={1000}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                placeholder="Décrivez votre parcours et votre expertise..."
              />
            ) : (
              <p className="text-sm text-gray-900 dark:text-white">
                {professorData?.professionalProfile?.bio || 'Aucune biographie renseignée'}
              </p>
            )}
          </div>

          {/* Domaines d'expertise */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Domaines d'expertise
            </label>
            {editing ? (
              <input
                type="text"
                value={formData.expertise.join(', ')}
                onChange={(e) => handleArrayChange('expertise', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                placeholder="Leadership, Innovation, Digital Transformation..."
              />
            ) : (
              <div className="flex flex-wrap gap-2">
                {professorData?.professionalProfile?.expertise?.length > 0 ? (
                  professorData.professionalProfile.expertise.map((item, index) => (
                    <span key={index} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200">
                      {item}
                    </span>
                  ))
                ) : (
                  <span className="text-sm text-gray-500 dark:text-gray-400">Aucun domaine renseigné</span>
                )}
              </div>
            )}
          </div>

          {/* Domaines d'enseignement */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Domaines d'enseignement
            </label>
            {editing ? (
              <input
                type="text"
                value={formData.teachingAreas.join(', ')}
                onChange={(e) => handleArrayChange('teachingAreas', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                placeholder="Management, Finance, Marketing..."
              />
            ) : (
              <div className="flex flex-wrap gap-2">
                {professorData?.teachingExperience?.teachingAreas?.length > 0 ? (
                  professorData.teachingExperience.teachingAreas.map((item, index) => (
                    <span key={index} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                      {item}
                    </span>
                  ))
                ) : (
                  <span className="text-sm text-gray-500 dark:text-gray-400">Aucun domaine renseigné</span>
                )}
              </div>
            )}
          </div>

          {/* Compétences techniques */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Compétences techniques
            </label>
            {editing ? (
              <input
                type="text"
                value={formData.technicalSkills.join(', ')}
                onChange={(e) => handleArrayChange('technicalSkills', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                placeholder="Excel, SPSS, Python, R..."
              />
            ) : (
              <div className="flex flex-wrap gap-2">
                {professorData?.skills?.technicalSkills?.length > 0 ? (
                  professorData.skills.technicalSkills.map((item, index) => (
                    <span key={index} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                      {item}
                    </span>
                  ))
                ) : (
                  <span className="text-sm text-gray-500 dark:text-gray-400">Aucune compétence renseignée</span>
                )}
              </div>
            )}
          </div>

          {/* Liens professionnels */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                LinkedIn
              </label>
              {editing ? (
                <input
                  type="url"
                  name="linkedinUrl"
                  value={formData.linkedinUrl}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  placeholder="https://linkedin.com/in/..."
                />
              ) : (
                <div className="flex items-center">
                  <GlobeAltIcon className="h-4 w-4 text-gray-400 mr-2" />
                  {professorData?.professionalProfile?.linkedinUrl ? (
                    <a 
                      href={professorData.professionalProfile.linkedinUrl} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-sm text-primary-600 hover:text-primary-800 dark:text-primary-400"
                    >
                      Voir le profil LinkedIn
                    </a>
                  ) : (
                    <span className="text-sm text-gray-500 dark:text-gray-400">Non renseigné</span>
                  )}
                </div>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Site web personnel
              </label>
              {editing ? (
                <input
                  type="url"
                  name="personalWebsite"
                  value={formData.personalWebsite}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  placeholder="https://..."
                />
              ) : (
                <div className="flex items-center">
                  <GlobeAltIcon className="h-4 w-4 text-gray-400 mr-2" />
                  {professorData?.professionalProfile?.personalWebsite ? (
                    <a 
                      href={professorData.professionalProfile.personalWebsite} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-sm text-primary-600 hover:text-primary-800 dark:text-primary-400"
                    >
                      Visiter le site web
                    </a>
                  ) : (
                    <span className="text-sm text-gray-500 dark:text-gray-400">Non renseigné</span>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Disponibilité pour voyager */}
          {editing && (
            <div className="flex items-center">
              <input
                type="checkbox"
                name="canTravelForTeaching"
                checked={formData.canTravelForTeaching}
                onChange={handleChange}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                Disponible pour enseigner en déplacement
              </label>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProfessorProfile;
