import React, { useState, useEffect } from 'react';
import {
  CheckIcon,
  XMarkIcon,
  PlusIcon,
  TrashIcon
} from '@heroicons/react/24/outline';
import { cn } from '../../utils/cn';

const SyllabusForm = ({ syllabus, courseId, courseName, onSubmit, onCancel }) => {
  const [formData, setFormData] = useState({
    courseName: courseName || '',
    numberOfHours: '',
    numberOfCredits: '',
    courseResponsible: '',
    teachingStaff: [{ name: '', email: '' }],
    coursePresentation: {
      description: '',
      programObjectives: [],
      learningOutcomes: [{ outcome: '', code: 'CLO.1' }],
      pedagogicalApproach: ''
    },
    courseSupport: {
      referenceBook: '',
      platformSupports: 'Supports à télécharger sur la plateforme Teams.',
      recommendedBooks: ['']
    },
    courseContent: {
      sessions: [{ 
        week: 'SEMAINE 1', 
        sessionNumber: 1, 
        theme: '', 
        duration: 4, 
        isOnline: false, 
        isFaceToFace: true, 
        preparatoryWork: '' 
      }],
      totalDuration: 12
    },
    evaluations: {
      continuousAssessment: {
        description: '',
        weight: 30
      },
      finalExam: {
        type: '',
        duration: '',
        documentsAllowed: '',
        calculatorAllowed: false,
        weight: 70
      }
    }
  });

  const [errors, setErrors] = useState({});

  // Objectifs du programme EMBA prédéfinis
  const programObjectives = [
    { code: 'PO.1', text: 'Capacité de communiquer efficacement en milieu professionnel tant sur le plan oral que sur le plan écrit.' },
    { code: 'PO.2', text: 'Capacité de pensée critique et de résolution de problèmes.' },
    { code: 'PO.3', text: 'Aptitude à prendre en compte les valeurs de l\'entreprise : éthique, diversité, ouverture aux autres, responsabilité et performance globale.' },
    { code: 'PO.4', text: 'Capacité de contextualiser et d\'appliquer un large champ de connaissances et de compétences interdisciplinaires dans son propre contexte professionnel.' },
    { code: 'PO.5', text: 'Maitrise des méthodes et des outils du management tant sur le plan stratégique que sur le plan opérationnel.' },
    { code: 'PO.6', text: 'Capacité de s\'adapter, d\'innover et de gérer dans des environnements imprévisibles.' },
    { code: 'PO.7', text: 'Capacité d\'évaluer la pertinence des technologies émergentes, de développer une stratégie numérique et de piloter une transformation digitale afin de faire évoluer l\'entreprise et sa performance.' },
    { code: 'PO.8', text: 'Capacité d\'exercer de manière efficace le style de leadership le plus adapté à la situation et de mobiliser des équipes pour atteindre des objectifs communs.' },
    { code: 'PO.9', text: 'Capacité d\'exercer de manière efficace les mécanismes de la négociation et de résolution de conflits.' }
  ];

  // Initialiser le formulaire avec les données existantes
  useEffect(() => {
    if (syllabus) {
      setFormData({
        courseName: syllabus.courseName || courseName || '',
        numberOfHours: syllabus.numberOfHours || '',
        numberOfCredits: syllabus.numberOfCredits || '',
        courseResponsible: syllabus.courseResponsible || '',
        teachingStaff: syllabus.teachingStaff?.length > 0 ? syllabus.teachingStaff : [{ name: '', email: '' }],
        coursePresentation: {
          description: syllabus.coursePresentation?.description || '',
          programObjectives: syllabus.coursePresentation?.programObjectives || [],
          learningOutcomes: syllabus.coursePresentation?.learningOutcomes?.length > 0 
            ? syllabus.coursePresentation.learningOutcomes 
            : [{ outcome: '', code: 'CLO.1' }],
          pedagogicalApproach: syllabus.coursePresentation?.pedagogicalApproach || ''
        },
        courseSupport: {
          referenceBook: syllabus.courseSupport?.referenceBook || '',
          platformSupports: syllabus.courseSupport?.platformSupports || 'Supports à télécharger sur la plateforme Teams.',
          recommendedBooks: syllabus.courseSupport?.recommendedBooks?.length > 0 
            ? syllabus.courseSupport.recommendedBooks 
            : ['']
        },
        courseContent: {
          sessions: syllabus.courseContent?.sessions?.length > 0 
            ? syllabus.courseContent.sessions 
            : [{ week: 'SEMAINE 1', sessionNumber: 1, theme: '', duration: 4, isOnline: false, isFaceToFace: true, preparatoryWork: '' }],
          totalDuration: syllabus.courseContent?.totalDuration || 12
        },
        evaluations: {
          continuousAssessment: {
            description: syllabus.evaluations?.continuousAssessment?.description || '',
            weight: syllabus.evaluations?.continuousAssessment?.weight || 30
          },
          finalExam: {
            type: syllabus.evaluations?.finalExam?.type || '',
            duration: syllabus.evaluations?.finalExam?.duration || '',
            documentsAllowed: syllabus.evaluations?.finalExam?.documentsAllowed || '',
            calculatorAllowed: syllabus.evaluations?.finalExam?.calculatorAllowed || false,
            weight: syllabus.evaluations?.finalExam?.weight || 70
          }
        }
      });
    }
  }, [syllabus, courseName]);

  const handleInputChange = (path, value) => {
    setFormData(prev => {
      const newData = { ...prev };
      const keys = path.split('.');
      let current = newData;
      
      for (let i = 0; i < keys.length - 1; i++) {
        if (!current[keys[i]]) current[keys[i]] = {};
        current = current[keys[i]];
      }
      
      current[keys[keys.length - 1]] = value;
      return newData;
    });
  };

  const handleArrayChange = (path, index, value) => {
    setFormData(prev => {
      const newData = { ...prev };
      const keys = path.split('.');
      let current = newData;
      
      for (let i = 0; i < keys.length - 1; i++) {
        current = current[keys[i]];
      }
      
      current[keys[keys.length - 1]][index] = value;
      return newData;
    });
  };

  const addArrayItem = (path, defaultValue) => {
    setFormData(prev => {
      const newData = { ...prev };
      const keys = path.split('.');
      let current = newData;
      
      for (let i = 0; i < keys.length - 1; i++) {
        current = current[keys[i]];
      }
      
      current[keys[keys.length - 1]].push(defaultValue);
      return newData;
    });
  };

  const removeArrayItem = (path, index) => {
    setFormData(prev => {
      const newData = { ...prev };
      const keys = path.split('.');
      let current = newData;
      
      for (let i = 0; i < keys.length - 1; i++) {
        current = current[keys[i]];
      }
      
      current[keys[keys.length - 1]].splice(index, 1);
      return newData;
    });
  };

  const handleProgramObjectiveToggle = (objectiveCode) => {
    const currentObjectives = formData.coursePresentation.programObjectives;
    const newObjectives = currentObjectives.includes(objectiveCode)
      ? currentObjectives.filter(code => code !== objectiveCode)
      : [...currentObjectives, objectiveCode];
    
    handleInputChange('coursePresentation.programObjectives', newObjectives);
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.courseName.trim()) newErrors.courseName = 'Le nom du cours est requis';
    if (!formData.numberOfHours || formData.numberOfHours <= 0) newErrors.numberOfHours = 'Le nombre d\'heures doit être supérieur à 0';
    if (!formData.numberOfCredits || formData.numberOfCredits <= 0) newErrors.numberOfCredits = 'Le nombre de crédits doit être supérieur à 0';
    if (!formData.courseResponsible.trim()) newErrors.courseResponsible = 'Le responsable du cours est requis';
    if (!formData.coursePresentation.description.trim()) newErrors.description = 'La description du cours est requise';
    
    // Vérifier que les poids d'évaluation totalisent 100%
    const totalWeight = formData.evaluations.continuousAssessment.weight + formData.evaluations.finalExam.weight;
    if (totalWeight !== 100) {
      newErrors.evaluationWeights = 'Les poids d\'évaluation doivent totaliser 100%';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (validateForm()) {
      onSubmit(formData);
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          {syllabus ? 'Modifier le Syllabus' : 'Créer un Syllabus'}
        </h3>
      </div>

      <form onSubmit={handleSubmit} className="p-6 space-y-8">
        {/* Informations de base */}
        <div>
          <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Informations Générales
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Nom du cours *
              </label>
              <input
                type="text"
                value={formData.courseName}
                onChange={(e) => handleInputChange('courseName', e.target.value)}
                className={cn(
                  "w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500",
                  errors.courseName ? "border-red-300" : "border-gray-300 dark:border-gray-600",
                  "dark:bg-gray-700 dark:text-white"
                )}
              />
              {errors.courseName && (
                <p className="mt-1 text-sm text-red-600">{errors.courseName}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Nombre d'heures *
              </label>
              <input
                type="number"
                min="1"
                value={formData.numberOfHours}
                onChange={(e) => handleInputChange('numberOfHours', parseInt(e.target.value) || '')}
                className={cn(
                  "w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500",
                  errors.numberOfHours ? "border-red-300" : "border-gray-300 dark:border-gray-600",
                  "dark:bg-gray-700 dark:text-white"
                )}
              />
              {errors.numberOfHours && (
                <p className="mt-1 text-sm text-red-600">{errors.numberOfHours}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Nombre de crédits *
              </label>
              <input
                type="number"
                min="1"
                value={formData.numberOfCredits}
                onChange={(e) => handleInputChange('numberOfCredits', parseInt(e.target.value) || '')}
                className={cn(
                  "w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500",
                  errors.numberOfCredits ? "border-red-300" : "border-gray-300 dark:border-gray-600",
                  "dark:bg-gray-700 dark:text-white"
                )}
              />
              {errors.numberOfCredits && (
                <p className="mt-1 text-sm text-red-600">{errors.numberOfCredits}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Responsable du cours *
              </label>
              <input
                type="text"
                value={formData.courseResponsible}
                onChange={(e) => handleInputChange('courseResponsible', e.target.value)}
                className={cn(
                  "w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500",
                  errors.courseResponsible ? "border-red-300" : "border-gray-300 dark:border-gray-600",
                  "dark:bg-gray-700 dark:text-white"
                )}
              />
              {errors.courseResponsible && (
                <p className="mt-1 text-sm text-red-600">{errors.courseResponsible}</p>
              )}
            </div>
          </div>
        </div>

        {/* Équipe pédagogique */}
        <div>
          <div className="flex justify-between items-center mb-4">
            <h4 className="text-lg font-medium text-gray-900 dark:text-white">
              Équipe Pédagogique
            </h4>
            <button
              type="button"
              onClick={() => addArrayItem('teachingStaff', { name: '', email: '' })}
              className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200"
            >
              <PlusIcon className="h-4 w-4 mr-1" />
              Ajouter
            </button>
          </div>
          
          {formData.teachingStaff.map((staff, index) => (
            <div key={index} className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4 p-4 border border-gray-200 dark:border-gray-600 rounded-md">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Nom
                </label>
                <input
                  type="text"
                  value={staff.name}
                  onChange={(e) => handleArrayChange('teachingStaff', index, { ...staff, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
              <div className="flex space-x-2">
                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Email
                  </label>
                  <input
                    type="email"
                    value={staff.email}
                    onChange={(e) => handleArrayChange('teachingStaff', index, { ...staff, email: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
                {formData.teachingStaff.length > 1 && (
                  <button
                    type="button"
                    onClick={() => removeArrayItem('teachingStaff', index)}
                    className="mt-6 p-2 text-red-600 hover:text-red-800"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Description du cours */}
        <div>
          <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            1. Présentation du Cours
          </h4>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Description du cours *
              </label>
              <textarea
                rows={4}
                value={formData.coursePresentation.description}
                onChange={(e) => handleInputChange('coursePresentation.description', e.target.value)}
                placeholder="Ce séminaire permet aux participants de..."
                className={cn(
                  "w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500",
                  errors.description ? "border-red-300" : "border-gray-300 dark:border-gray-600",
                  "dark:bg-gray-700 dark:text-white"
                )}
              />
              {errors.description && (
                <p className="mt-1 text-sm text-red-600">{errors.description}</p>
              )}
            </div>

            {/* Objectifs du programme */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Objectifs du programme EMBA couverts par ce cours
              </label>
              <div className="space-y-2 max-h-60 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded-md p-3">
                {programObjectives.map((objective) => (
                  <label key={objective.code} className="flex items-start space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={formData.coursePresentation.programObjectives.includes(objective.code)}
                      onChange={() => handleProgramObjectiveToggle(objective.code)}
                      className="mt-1 h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    />
                    <div className="flex-1">
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {objective.code}
                      </span>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {objective.text}
                      </p>
                    </div>
                  </label>
                ))}
              </div>
            </div>

            {/* Acquis d'apprentissage */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Acquis d'apprentissage du cours (CLO)
                </label>
                <button
                  type="button"
                  onClick={() => addArrayItem('coursePresentation.learningOutcomes', { outcome: '', code: 'CLO.1' })}
                  className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-primary-700 bg-primary-100 hover:bg-primary-200"
                >
                  <PlusIcon className="h-3 w-3 mr-1" />
                  Ajouter CLO
                </button>
              </div>

              {formData.coursePresentation.learningOutcomes.map((outcome, index) => (
                <div key={index} className="flex space-x-2 mb-2">
                  <select
                    value={outcome.code}
                    onChange={(e) => handleArrayChange('coursePresentation.learningOutcomes', index, { ...outcome, code: e.target.value })}
                    className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="CLO.1">CLO.1</option>
                    <option value="CLO.2">CLO.2</option>
                    <option value="CLO.3">CLO.3</option>
                    <option value="CLO.4">CLO.4</option>
                    <option value="CLO.5">CLO.5</option>
                  </select>
                  <input
                    type="text"
                    value={outcome.outcome}
                    onChange={(e) => handleArrayChange('coursePresentation.learningOutcomes', index, { ...outcome, outcome: e.target.value })}
                    placeholder="A l'issue de ce cours, le participant sera en mesure de..."
                    className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                  />
                  {formData.coursePresentation.learningOutcomes.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeArrayItem('coursePresentation.learningOutcomes', index)}
                      className="p-2 text-red-600 hover:text-red-800"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  )}
                </div>
              ))}
            </div>

            {/* Approche pédagogique */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Approche pédagogique
              </label>
              <textarea
                rows={3}
                value={formData.coursePresentation.pedagogicalApproach}
                onChange={(e) => handleInputChange('coursePresentation.pedagogicalApproach', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>
        </div>

        {/* Support de cours */}
        <div>
          <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            2. Support de Cours
          </h4>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Ouvrage de référence
              </label>
              <input
                type="text"
                value={formData.courseSupport.referenceBook}
                onChange={(e) => handleInputChange('courseSupport.referenceBook', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Supports plateforme
              </label>
              <input
                type="text"
                value={formData.courseSupport.platformSupports}
                onChange={(e) => handleInputChange('courseSupport.platformSupports', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
              />
            </div>

            <div>
              <div className="flex justify-between items-center mb-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Ouvrages conseillés
                </label>
                <button
                  type="button"
                  onClick={() => addArrayItem('courseSupport.recommendedBooks', '')}
                  className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-primary-700 bg-primary-100 hover:bg-primary-200"
                >
                  <PlusIcon className="h-3 w-3 mr-1" />
                  Ajouter
                </button>
              </div>

              {formData.courseSupport.recommendedBooks.map((book, index) => (
                <div key={index} className="flex space-x-2 mb-2">
                  <input
                    type="text"
                    value={book}
                    onChange={(e) => handleArrayChange('courseSupport.recommendedBooks', index, e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                  />
                  {formData.courseSupport.recommendedBooks.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeArrayItem('courseSupport.recommendedBooks', index)}
                      className="p-2 text-red-600 hover:text-red-800"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Évaluations */}
        <div>
          <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            4. Évaluations
          </h4>

          <div className="space-y-4">
            {/* Contrôle continu */}
            <div className="border border-gray-200 dark:border-gray-600 rounded-md p-4">
              <h5 className="text-md font-medium text-gray-900 dark:text-white mb-3">
                Contrôle Continu
              </h5>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="md:col-span-1">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Pondération (%)
                  </label>
                  <input
                    type="number"
                    min="0"
                    max="100"
                    value={formData.evaluations.continuousAssessment.weight}
                    onChange={(e) => handleInputChange('evaluations.continuousAssessment.weight', parseInt(e.target.value) || 0)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div className="md:col-span-1">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Description
                  </label>
                  <textarea
                    rows={3}
                    value={formData.evaluations.continuousAssessment.description}
                    onChange={(e) => handleInputChange('evaluations.continuousAssessment.description', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
              </div>
            </div>

            {/* Examen final */}
            <div className="border border-gray-200 dark:border-gray-600 rounded-md p-4">
              <h5 className="text-md font-medium text-gray-900 dark:text-white mb-3">
                Examen Final
              </h5>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Type d'examen
                  </label>
                  <input
                    type="text"
                    value={formData.evaluations.finalExam.type}
                    onChange={(e) => handleInputChange('evaluations.finalExam.type', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Durée d'examen
                  </label>
                  <input
                    type="text"
                    value={formData.evaluations.finalExam.duration}
                    onChange={(e) => handleInputChange('evaluations.finalExam.duration', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Documents autorisés
                  </label>
                  <input
                    type="text"
                    value={formData.evaluations.finalExam.documentsAllowed}
                    onChange={(e) => handleInputChange('evaluations.finalExam.documentsAllowed', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Pondération (%)
                  </label>
                  <input
                    type="number"
                    min="0"
                    max="100"
                    value={formData.evaluations.finalExam.weight}
                    onChange={(e) => handleInputChange('evaluations.finalExam.weight', parseInt(e.target.value) || 0)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div className="md:col-span-2">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={formData.evaluations.finalExam.calculatorAllowed}
                      onChange={(e) => handleInputChange('evaluations.finalExam.calculatorAllowed', e.target.checked)}
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    />
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      Calculatrice autorisée
                    </span>
                  </label>
                </div>
              </div>
            </div>

            {/* Validation des poids */}
            {errors.evaluationWeights && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-600">{errors.evaluationWeights}</p>
              </div>
            )}

            {/* Résumé des poids */}
            <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
              <div className="text-sm text-gray-700 dark:text-gray-300">
                <strong>Total des pondérations:</strong> {formData.evaluations.continuousAssessment.weight + formData.evaluations.finalExam.weight}%
              </div>
            </div>
          </div>
        </div>

        {/* Contenu du cours */}
        <div>
          <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            3. Contenu, Activités et Organisation
          </h4>

          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Sessions de cours
              </label>
              <button
                type="button"
                onClick={() => addArrayItem('courseContent.sessions', {
                  week: `SEMAINE ${formData.courseContent.sessions.length + 1}`,
                  sessionNumber: formData.courseContent.sessions.length + 1,
                  theme: '',
                  duration: 4,
                  isOnline: false,
                  isFaceToFace: true,
                  preparatoryWork: ''
                })}
                className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200"
              >
                <PlusIcon className="h-4 w-4 mr-1" />
                Ajouter Session
              </button>
            </div>

            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Séance
                    </th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Thème
                    </th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Durée (h)
                    </th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Format
                    </th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600">
                  {formData.courseContent.sessions.map((session, index) => (
                    <tr key={index}>
                      <td className="px-3 py-2">
                        <div className="space-y-1">
                          <input
                            type="text"
                            value={session.week}
                            onChange={(e) => handleArrayChange('courseContent.sessions', index, { ...session, week: e.target.value })}
                            className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-1 focus:ring-primary-500 dark:bg-gray-700 dark:text-white"
                          />
                          <input
                            type="number"
                            min="1"
                            value={session.sessionNumber}
                            onChange={(e) => handleArrayChange('courseContent.sessions', index, { ...session, sessionNumber: parseInt(e.target.value) || 1 })}
                            className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-1 focus:ring-primary-500 dark:bg-gray-700 dark:text-white"
                          />
                        </div>
                      </td>
                      <td className="px-3 py-2">
                        <input
                          type="text"
                          value={session.theme}
                          onChange={(e) => handleArrayChange('courseContent.sessions', index, { ...session, theme: e.target.value })}
                          className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-1 focus:ring-primary-500 dark:bg-gray-700 dark:text-white"
                        />
                      </td>
                      <td className="px-3 py-2">
                        <input
                          type="number"
                          min="1"
                          value={session.duration}
                          onChange={(e) => handleArrayChange('courseContent.sessions', index, { ...session, duration: parseInt(e.target.value) || 1 })}
                          className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-1 focus:ring-primary-500 dark:bg-gray-700 dark:text-white"
                        />
                      </td>
                      <td className="px-3 py-2">
                        <div className="space-y-1">
                          <label className="flex items-center text-xs">
                            <input
                              type="checkbox"
                              checked={session.isOnline}
                              onChange={(e) => handleArrayChange('courseContent.sessions', index, { ...session, isOnline: e.target.checked })}
                              className="h-3 w-3 text-primary-600 focus:ring-primary-500 border-gray-300 rounded mr-1"
                            />
                            En ligne
                          </label>
                          <label className="flex items-center text-xs">
                            <input
                              type="checkbox"
                              checked={session.isFaceToFace}
                              onChange={(e) => handleArrayChange('courseContent.sessions', index, { ...session, isFaceToFace: e.target.checked })}
                              className="h-3 w-3 text-primary-600 focus:ring-primary-500 border-gray-300 rounded mr-1"
                            />
                            Présentiel
                          </label>
                        </div>
                      </td>
                      <td className="px-3 py-2">
                        {formData.courseContent.sessions.length > 1 && (
                          <button
                            type="button"
                            onClick={() => removeArrayItem('courseContent.sessions', index)}
                            className="p-1 text-red-600 hover:text-red-800"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Durée totale (heures)
              </label>
              <input
                type="number"
                min="1"
                value={formData.courseContent.totalDuration}
                onChange={(e) => handleInputChange('courseContent.totalDuration', parseInt(e.target.value) || 0)}
                className="w-32 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
          <button
            type="button"
            onClick={onCancel}
            className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            <XMarkIcon className="h-4 w-4 mr-2" />
            Annuler
          </button>
          <button
            type="submit"
            className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
          >
            <CheckIcon className="h-4 w-4 mr-2" />
            {syllabus ? 'Mettre à jour' : 'Créer'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default SyllabusForm;
