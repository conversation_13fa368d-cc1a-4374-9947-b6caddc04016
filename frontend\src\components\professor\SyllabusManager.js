import React, { useState, useEffect, useCallback } from 'react';
import {
  DocumentTextIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon
} from '@heroicons/react/24/outline';
import SyllabusForm from './SyllabusForm';

const SyllabusManager = ({ courseId, courseName }) => {
  const [syllabus, setSyllabus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [isEditing, setIsEditing] = useState(false);

  // Charger le syllabus existant
  const fetchSyllabus = useCallback(async () => {
    try {
      const response = await fetch(`http://localhost:5000/api/courses/${courseId}/syllabus`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setSyllabus(data.syllabus);
      } else if (response.status === 404) {
        setSyllabus(null);
      }
    } catch (error) {
      console.error('Erreur lors du chargement du syllabus:', error);
    } finally {
      setLoading(false);
    }
  }, [courseId]);

  useEffect(() => {
    if (courseId) {
      fetchSyllabus();
    }
  }, [courseId, fetchSyllabus]);

  const handleCreateSyllabus = () => {
    setIsEditing(false);
    setShowForm(true);
  };

  const handleEditSyllabus = () => {
    setIsEditing(true);
    setShowForm(true);
  };

  const handleDeleteSyllabus = async () => {
    if (!window.confirm('Êtes-vous sûr de vouloir supprimer ce syllabus ?')) {
      return;
    }

    try {
      const response = await fetch(`http://localhost:5000/api/courses/${courseId}/syllabus`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        setSyllabus(null);
        alert('Syllabus supprimé avec succès');
      } else {
        alert('Erreur lors de la suppression du syllabus');
      }
    } catch (error) {
      console.error('Erreur lors de la suppression:', error);
      alert('Erreur lors de la suppression du syllabus');
    }
  };

  const handleFormSubmit = async (formData) => {
    try {
      const url = `http://localhost:5000/api/courses/${courseId}/syllabus`;
      const method = isEditing ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(formData)
      });

      if (response.ok) {
        const data = await response.json();
        setSyllabus(data.syllabus);
        setShowForm(false);
        alert(isEditing ? 'Syllabus mis à jour avec succès' : 'Syllabus créé avec succès');
      } else {
        const error = await response.json();
        alert(error.message || 'Erreur lors de la sauvegarde');
      }
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      alert('Erreur lors de la sauvegarde du syllabus');
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-32">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Syllabus du Cours
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {courseName}
          </p>
        </div>
        <div className="flex space-x-2">
          {syllabus ? (
            <>
              <button
                onClick={handleEditSyllabus}
                className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                <PencilIcon className="h-4 w-4 mr-1" />
                Modifier
              </button>
              <button
                onClick={handleDeleteSyllabus}
                className="inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50"
              >
                <TrashIcon className="h-4 w-4 mr-1" />
                Supprimer
              </button>
            </>
          ) : (
            <button
              onClick={handleCreateSyllabus}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Créer Syllabus
            </button>
          )}
        </div>
      </div>

      {/* Contenu */}
      {showForm ? (
        <SyllabusForm
          syllabus={isEditing ? syllabus : null}
          courseId={courseId}
          courseName={courseName}
          onSubmit={handleFormSubmit}
          onCancel={() => setShowForm(false)}
        />
      ) : syllabus ? (
        <SyllabusDisplay syllabus={syllabus} />
      ) : (
        <div className="text-center py-12 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
            Aucun syllabus
          </h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Créez un syllabus pour ce cours pour que les étudiants puissent voir le programme.
          </p>
          <div className="mt-6">
            <button
              onClick={handleCreateSyllabus}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Créer Syllabus
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

// Composant d'affichage du syllabus
const SyllabusDisplay = ({ syllabus }) => {
  return (
    <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
      <div className="space-y-6">
        {/* Informations de base */}
        <div>
          <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Informations Générales
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Nom du cours
              </label>
              <p className="mt-1 text-sm text-gray-900 dark:text-white">
                {syllabus.courseName}
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Nombre d'heures
              </label>
              <p className="mt-1 text-sm text-gray-900 dark:text-white">
                {syllabus.numberOfHours}
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Nombre de crédits
              </label>
              <p className="mt-1 text-sm text-gray-900 dark:text-white">
                {syllabus.numberOfCredits}
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Responsable du cours
              </label>
              <p className="mt-1 text-sm text-gray-900 dark:text-white">
                {syllabus.courseResponsible}
              </p>
            </div>
          </div>
        </div>

        {/* Équipe pédagogique */}
        {syllabus.teachingStaff && syllabus.teachingStaff.length > 0 && (
          <div>
            <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Équipe Pédagogique
            </h4>
            <div className="space-y-2">
              {syllabus.teachingStaff.map((staff, index) => (
                <div key={index} className="flex justify-between">
                  <span className="text-sm text-gray-900 dark:text-white">{staff.name}</span>
                  <span className="text-sm text-gray-500 dark:text-gray-400">{staff.email}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Description du cours */}
        {syllabus.coursePresentation?.description && (
          <div>
            <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Description du Cours
            </h4>
            <p className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
              {syllabus.coursePresentation.description}
            </p>
          </div>
        )}

        {/* Objectifs du programme */}
        {syllabus.coursePresentation?.programObjectives && syllabus.coursePresentation.programObjectives.length > 0 && (
          <div>
            <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Objectifs du Programme EMBA
            </h4>
            <ul className="list-disc list-inside space-y-1">
              {syllabus.coursePresentation.programObjectives.map((objective, index) => (
                <li key={index} className="text-sm text-gray-700 dark:text-gray-300">
                  {objective}
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
};

export default SyllabusManager;
