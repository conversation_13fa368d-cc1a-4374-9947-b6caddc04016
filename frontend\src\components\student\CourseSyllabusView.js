import React, { useState, useEffect } from 'react';
import {
  DocumentTextIcon,
  ClockIcon,
  AcademicCapIcon,
  BookOpenIcon,
  UserIcon,
  EnvelopeIcon,
  CalendarDaysIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import { cn } from '../../utils/cn';

const CourseSyllabusView = ({ courseId, courseName }) => {
  const [syllabus, setSyllabus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Charger le syllabus du cours
  const fetchSyllabus = async () => {
    try {
      const response = await fetch(`http://localhost:5000/api/courses/${courseId}/syllabus/preview`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setSyllabus(data.syllabus);
      } else if (response.status === 404) {
        setError('Aucun syllabus disponible pour ce cours');
      } else {
        setError('Erreur lors du chargement du syllabus');
      }
    } catch (error) {
      console.error('Erreur lors du chargement du syllabus:', error);
      setError('Erreur lors du chargement du syllabus');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (courseId) {
      fetchSyllabus();
    }
  }, [courseId]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-32">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
          Syllabus non disponible
        </h3>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          {error}
        </p>
      </div>
    );
  }

  if (!syllabus) {
    return null;
  }

  // Objectifs du programme EMBA avec descriptions
  const programObjectivesDescriptions = {
    'PO.1': 'Capacité de communiquer efficacement en milieu professionnel tant sur le plan oral que sur le plan écrit.',
    'PO.2': 'Capacité de pensée critique et de résolution de problèmes.',
    'PO.3': 'Aptitude à prendre en compte les valeurs de l\'entreprise : éthique, diversité, ouverture aux autres, responsabilité et performance globale.',
    'PO.4': 'Capacité de contextualiser et d\'appliquer un large champ de connaissances et de compétences interdisciplinaires dans son propre contexte professionnel.',
    'PO.5': 'Maitrise des méthodes et des outils du management tant sur le plan stratégique que sur le plan opérationnel.',
    'PO.6': 'Capacité de s\'adapter, d\'innover et de gérer dans des environnements imprévisibles.',
    'PO.7': 'Capacité d\'évaluer la pertinence des technologies émergentes, de développer une stratégie numérique et de piloter une transformation digitale afin de faire évoluer l\'entreprise et sa performance.',
    'PO.8': 'Capacité d\'exercer de manière efficace le style de leadership le plus adapté à la situation et de mobiliser des équipes pour atteindre des objectifs communs.',
    'PO.9': 'Capacité d\'exercer de manière efficace les mécanismes de la négociation et de résolution de conflits.'
  };

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Syllabus du Cours
          </h1>
          <h2 className="text-xl font-semibold text-primary-600 dark:text-primary-400 mt-2">
            {syllabus.courseName}
          </h2>
        </div>
        
        {/* Informations de base */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
          <div className="text-center">
            <ClockIcon className="h-8 w-8 text-blue-600 mx-auto" />
            <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mt-2">Nombre d'heures</p>
            <p className="text-lg font-semibold text-gray-900 dark:text-white">{syllabus.numberOfHours}h</p>
          </div>
          <div className="text-center">
            <AcademicCapIcon className="h-8 w-8 text-green-600 mx-auto" />
            <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mt-2">Nombre de crédits</p>
            <p className="text-lg font-semibold text-gray-900 dark:text-white">{syllabus.numberOfCredits}</p>
          </div>
          <div className="text-center">
            <UserIcon className="h-8 w-8 text-purple-600 mx-auto" />
            <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mt-2">Responsable</p>
            <p className="text-lg font-semibold text-gray-900 dark:text-white">{syllabus.courseResponsible}</p>
          </div>
        </div>

        {/* Équipe pédagogique */}
        {syllabus.teachingStaff && syllabus.teachingStaff.length > 0 && (
          <div className="mt-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
              Équipe Pédagogique
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {syllabus.teachingStaff.map((staff, index) => (
                <div key={index} className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <UserIcon className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">{staff.name}</p>
                    <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                      <EnvelopeIcon className="h-4 w-4 mr-1" />
                      {staff.email}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* 1. Présentation du cours */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          1. Présentation du Cours
        </h2>
        
        {/* Description */}
        {syllabus.coursePresentation?.description && (
          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
              Description du cours
            </h3>
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <p className="text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                {syllabus.coursePresentation.description}
              </p>
            </div>
          </div>
        )}

        {/* Objectifs du programme */}
        {syllabus.coursePresentation?.programObjectives && syllabus.coursePresentation.programObjectives.length > 0 && (
          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
              Objectifs du programme EMBA couverts par ce cours
            </h3>
            <div className="space-y-3">
              {syllabus.coursePresentation.programObjectives.map((objective, index) => (
                <div key={index} className="flex items-start space-x-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <CheckCircleIcon className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
                  <div>
                    <span className="font-medium text-blue-900 dark:text-blue-100">{objective}</span>
                    <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                      {programObjectivesDescriptions[objective]}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Acquis d'apprentissage */}
        {syllabus.coursePresentation?.learningOutcomes && syllabus.coursePresentation.learningOutcomes.length > 0 && (
          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
              Acquis d'apprentissage du cours (CLO)
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
              À l'issue de ce cours, le participant sera en mesure de :
            </p>
            <div className="space-y-2">
              {syllabus.coursePresentation.learningOutcomes.map((outcome, index) => (
                <div key={index} className="flex items-start space-x-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <span className="inline-flex items-center justify-center w-8 h-8 bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-200 rounded-full text-sm font-medium flex-shrink-0">
                    {outcome.code?.replace('CLO.', '') || index + 1}
                  </span>
                  <p className="text-gray-700 dark:text-gray-300">{outcome.outcome}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Approche pédagogique */}
        {syllabus.coursePresentation?.pedagogicalApproach && (
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
              Approche pédagogique
            </h3>
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <p className="text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                {syllabus.coursePresentation.pedagogicalApproach}
              </p>
            </div>
          </div>
        )}
      </div>

      {/* 2. Support de cours */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          2. Support de Cours
        </h2>
        
        <div className="space-y-4">
          {syllabus.courseSupport?.referenceBook && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                Ouvrage de référence
              </h3>
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                <p className="text-gray-700 dark:text-gray-300">{syllabus.courseSupport.referenceBook}</p>
              </div>
            </div>
          )}

          {syllabus.courseSupport?.platformSupports && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                Supports plateforme
              </h3>
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                <p className="text-gray-700 dark:text-gray-300">{syllabus.courseSupport.platformSupports}</p>
              </div>
            </div>
          )}

          {syllabus.courseSupport?.recommendedBooks && syllabus.courseSupport.recommendedBooks.length > 0 && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                Ouvrages conseillés
              </h3>
              <div className="space-y-2">
                {syllabus.courseSupport.recommendedBooks.filter(book => book.trim()).map((book, index) => (
                  <div key={index} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                    <p className="text-gray-700 dark:text-gray-300">{book}</p>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 3. Contenu, activités et organisation */}
      {syllabus.courseContent?.sessions && syllabus.courseContent.sessions.length > 0 && (
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            3. Contenu, Activités et Organisation
          </h2>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Séance
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Thème
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Durée
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Format
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Travaux préparatoires
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600">
                {syllabus.courseContent.sessions.map((session, index) => (
                  <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {session.week}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        Session {session.sessionNumber}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900 dark:text-white">
                        {session.theme}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-sm text-gray-900 dark:text-white">
                        <ClockIcon className="h-4 w-4 mr-1 text-gray-400" />
                        {session.duration}h
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex flex-col space-y-1">
                        {session.isOnline && (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                            En ligne
                          </span>
                        )}
                        {session.isFaceToFace && (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                            Présentiel
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-700 dark:text-gray-300">
                        {session.preparatoryWork || '-'}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {syllabus.courseContent.totalDuration && (
            <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="flex items-center justify-center">
                <CalendarDaysIcon className="h-5 w-5 text-gray-400 mr-2" />
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Durée totale : {syllabus.courseContent.totalDuration} heures
                </span>
              </div>
            </div>
          )}
        </div>
      )}

      {/* 4. Évaluations */}
      {syllabus.evaluations && (
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            4. Évaluations
          </h2>

          <div className="space-y-6">
            {/* Contrôle continu */}
            {syllabus.evaluations.continuousAssessment && (
              <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
                  Contrôle Continu ({syllabus.evaluations.continuousAssessment.weight}%)
                </h3>
                {syllabus.evaluations.continuousAssessment.description && (
                  <p className="text-gray-700 dark:text-gray-300">
                    {syllabus.evaluations.continuousAssessment.description}
                  </p>
                )}
              </div>
            )}

            {/* Examen final */}
            {syllabus.evaluations.finalExam && (
              <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
                  Examen Final ({syllabus.evaluations.finalExam.weight}%)
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {syllabus.evaluations.finalExam.type && (
                    <div>
                      <span className="text-sm font-medium text-gray-500 dark:text-gray-400">Type d'examen :</span>
                      <p className="text-gray-700 dark:text-gray-300">{syllabus.evaluations.finalExam.type}</p>
                    </div>
                  )}
                  {syllabus.evaluations.finalExam.duration && (
                    <div>
                      <span className="text-sm font-medium text-gray-500 dark:text-gray-400">Durée :</span>
                      <p className="text-gray-700 dark:text-gray-300">{syllabus.evaluations.finalExam.duration}</p>
                    </div>
                  )}
                  {syllabus.evaluations.finalExam.documentsAllowed && (
                    <div>
                      <span className="text-sm font-medium text-gray-500 dark:text-gray-400">Documents autorisés :</span>
                      <p className="text-gray-700 dark:text-gray-300">{syllabus.evaluations.finalExam.documentsAllowed}</p>
                    </div>
                  )}
                  <div>
                    <span className="text-sm font-medium text-gray-500 dark:text-gray-400">Calculatrice :</span>
                    <p className="text-gray-700 dark:text-gray-300">
                      {syllabus.evaluations.finalExam.calculatorAllowed ? 'Autorisée' : 'Non autorisée'}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Échelle de notation */}
            {syllabus.evaluations.gradingScale && syllabus.evaluations.gradingScale.length > 0 && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
                  Échelle de Notation ECTS
                </h3>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
                    <thead className="bg-gray-50 dark:bg-gray-700">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Note
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Grade ECTS
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Évaluation
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600">
                      {syllabus.evaluations.gradingScale.map((grade, index) => (
                        <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {grade.minScore}-{grade.maxScore}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={cn(
                              "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",
                              grade.grade === 'A' ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200" :
                              grade.grade === 'B' ? "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200" :
                              grade.grade === 'C' ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200" :
                              grade.grade === 'D' ? "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200" :
                              grade.grade === 'E' ? "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200" :
                              "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
                            )}>
                              {grade.grade}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
                            {grade.evaluation}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 5. Règlements */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          5. Règlements sur la Fraude Académique et l'Utilisation des Calculatrices
        </h2>

        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
              5.1. Intégrité Académique
            </h3>
            <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
              <p className="text-gray-700 dark:text-gray-300">
                Les participants sont priés de prendre connaissance des actes qui sont considérés comme étant de la fraude académique.
                Toute fraude ou tentative de fraude sera sanctionnée par l'attribution du grade F (échec) dans le module concerné.
                Selon la gravité et la fréquence de la fraude commise, d'autres sanctions peuvent être appliquées, y-compris l'exclusion
                du participant du programme. Pour plus d'informations, veuillez consulter le Manuel de Règlement Pédagogique du programme EMBA.
              </p>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
              5.2. Calculatrices
            </h3>
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
              <p className="text-gray-700 dark:text-gray-300">
                Lors des examens qui autorisent l'utilisation d'une calculatrice, les participants sont priés de prendre note que
                seules les calculatrices non programmables et dans lesquelles il est impossible de stocker du texte seront permises.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CourseSyllabusView;
