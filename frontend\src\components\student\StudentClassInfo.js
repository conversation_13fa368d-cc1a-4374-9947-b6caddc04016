import React, { useState, useEffect } from 'react';
import {
  UserGroupIcon,
  AcademicCapIcon,
  EnvelopeIcon,
  PhoneIcon,
  UserIcon,
  CalendarIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { cn } from '../../utils/cn';

const StudentClassInfo = () => {
  const [classInfo, setClassInfo] = useState(null);
  const [classmates, setClassmates] = useState([]);
  const [professors, setProfessors] = useState([]);
  const [schedule, setSchedule] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('classmates');

  // Charger les informations de la classe
  const loadClassInfo = async () => {
    try {
      setLoading(true);
      
      // Récupérer les informations de l'étudiant
      const studentResponse = await fetch('http://localhost:5000/api/students/me', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!studentResponse.ok) {
        throw new Error('Erreur lors du chargement des informations étudiant');
      }

      const studentData = await studentResponse.json();
      const student = studentData.student;
      
      setClassInfo({
        className: student.class,
        promotion: student.promotion,
        program: student.program,
        cohort: student.cohort,
        academicYear: student.academicYear
      });

      // Récupérer les camarades de classe
      const classmatesResponse = await fetch('http://localhost:5000/api/students/me/classmates', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (classmatesResponse.ok) {
        const classmatesData = await classmatesResponse.json();
        setClassmates(classmatesData.students || []);
      }

      // Récupérer les cours et professeurs de la classe
      const coursesResponse = await fetch(`http://localhost:5000/api/courses/class/${student.class}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (coursesResponse.ok) {
        const coursesData = await coursesResponse.json();
        
        // Extraire les professeurs uniques
        const uniqueProfessors = [];
        const professorIds = new Set();
        
        coursesData.courses.forEach(course => {
          if (course.instructor && !professorIds.has(course.instructor._id)) {
            professorIds.add(course.instructor._id);
            uniqueProfessors.push({
              ...course.instructor,
              courses: [course.title]
            });
          } else if (course.instructor && professorIds.has(course.instructor._id)) {
            // Ajouter le cours à un professeur existant
            const existingProf = uniqueProfessors.find(p => p._id === course.instructor._id);
            if (existingProf) {
              existingProf.courses.push(course.title);
            }
          }
        });
        
        setProfessors(uniqueProfessors);
      }

      // Récupérer l'emploi du temps de la classe
      const scheduleResponse = await fetch(`http://localhost:5000/api/schedule/class/${student.class}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (scheduleResponse.ok) {
        const scheduleData = await scheduleResponse.json();
        setSchedule(scheduleData.schedule || []);
      }

    } catch (error) {
      console.error('Erreur lors du chargement des informations de classe:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadClassInfo();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  const tabs = [
    { id: 'classmates', name: 'Camarades de Classe', icon: UserGroupIcon, count: classmates.length },
    { id: 'professors', name: 'Professeurs', icon: AcademicCapIcon, count: professors.length },
    { id: 'schedule', name: 'Emploi du Temps', icon: CalendarIcon, count: schedule.length }
  ];

  return (
    <div className="space-y-6">
      {/* En-tête de la classe */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              Classe {classInfo?.className}
            </h2>
            <p className="text-gray-600 dark:text-gray-400">
              {classInfo?.program} • {classInfo?.promotion} • {classInfo?.academicYear}
            </p>
          </div>
          <div className="text-right">
            <div className="text-sm text-gray-500 dark:text-gray-400">Cohorte</div>
            <div className="font-medium text-gray-900 dark:text-white">{classInfo?.cohort}</div>
          </div>
        </div>
      </div>

      {/* Onglets */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={cn(
                    "flex items-center py-4 px-1 border-b-2 font-medium text-sm",
                    activeTab === tab.id
                      ? "border-primary-500 text-primary-600 dark:text-primary-400"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"
                  )}
                >
                  <Icon className="h-5 w-5 mr-2" />
                  {tab.name}
                  <span className="ml-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 py-0.5 px-2 rounded-full text-xs">
                    {tab.count}
                  </span>
                </button>
              );
            })}
          </nav>
        </div>

        <div className="p-6">
          {/* Onglet Camarades de Classe */}
          {activeTab === 'classmates' && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Camarades de Classe ({classmates.length})
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {classmates.map((classmate) => (
                  <div key={classmate._id} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        {classmate.user.profilePicture ? (
                          <img
                            src={classmate.user.profilePicture}
                            alt={`${classmate.user.firstName} ${classmate.user.lastName}`}
                            className="h-10 w-10 rounded-full"
                          />
                        ) : (
                          <div className="h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                            <UserIcon className="h-6 w-6 text-gray-500 dark:text-gray-400" />
                          </div>
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                          {classmate.user.firstName} {classmate.user.lastName}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {classmate.studentNumber}
                        </p>
                        <div className="flex items-center space-x-2 mt-1">
                          <EnvelopeIcon className="h-3 w-3 text-gray-400" />
                          <span className="text-xs text-gray-500 dark:text-gray-400 truncate">
                            {classmate.user.email}
                          </span>
                        </div>
                        {classmate.user.phone && (
                          <div className="flex items-center space-x-2 mt-1">
                            <PhoneIcon className="h-3 w-3 text-gray-400" />
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              {classmate.user.phone}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Onglet Professeurs */}
          {activeTab === 'professors' && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Professeurs ({professors.length})
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {professors.map((professor) => (
                  <div key={professor._id} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0">
                        <div className="h-12 w-12 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
                          <AcademicCapIcon className="h-6 w-6 text-primary-600 dark:text-primary-400" />
                        </div>
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900 dark:text-white">
                          {professor.user?.firstName} {professor.user?.lastName}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {professor.academicTitle} • {professor.department}
                        </p>
                        <div className="mt-2">
                          <p className="text-xs font-medium text-gray-700 dark:text-gray-300">Cours enseignés:</p>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {professor.courses?.map((course, index) => (
                              <span
                                key={index}
                                className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                              >
                                {course}
                              </span>
                            ))}
                          </div>
                        </div>
                        <div className="flex items-center space-x-2 mt-2">
                          <EnvelopeIcon className="h-3 w-3 text-gray-400" />
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            {professor.user?.email}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Onglet Emploi du Temps */}
          {activeTab === 'schedule' && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Emploi du Temps de la Classe
              </h3>
              {schedule.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-700">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Jour
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Horaire
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Cours
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Professeur
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Salle
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                      {schedule.map((item, index) => (
                        <tr key={index}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                            {item.dayOfWeek}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            {item.startTime} - {item.endTime}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {item.courseName}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            {item.professorName}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            {item.location}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8">
                  <ClockIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500 dark:text-gray-400">
                    Aucun emploi du temps disponible pour le moment
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default StudentClassInfo;
