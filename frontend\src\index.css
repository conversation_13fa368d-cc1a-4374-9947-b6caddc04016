@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
    overflow-x: hidden;
  }

  body {
    @apply font-sans antialiased;
    @apply bg-white dark:bg-gray-900;
    @apply text-gray-900 dark:text-gray-100;
    @apply transition-colors duration-300;
    overflow-x: hidden;
  }

  /* Éviter les débordements horizontaux */
  * {
    box-sizing: border-box;
  }

  #root {
    overflow-x: hidden;
    width: 100%;
  }

  /* Scrollbar personnalisé */
  ::-webkit-scrollbar {
    @apply w-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 dark:bg-gray-600 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400 dark:bg-gray-500;
  }

  /* Focus styles pour l'accessibilité */
  *:focus {
    @apply outline-none ring-2 ring-primary-500 ring-offset-2 ring-offset-white dark:ring-offset-gray-900;
  }

  /* Styles pour les éléments de formulaire */
  input, textarea, select {
    @apply transition-colors duration-200;
  }
}

@layer components {
  /* Boutons */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
    @apply dark:bg-primary-500 dark:hover:bg-primary-600;
  }

  .btn-secondary {
    @apply bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
    @apply dark:bg-gray-700 dark:text-gray-100 dark:hover:bg-gray-600;
  }

  .btn-outline {
    @apply border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-gray-500;
    @apply dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-800;
  }

  .btn-danger {
    @apply bg-error-600 text-white hover:bg-error-700 focus:ring-error-500;
  }

  .btn-success {
    @apply bg-success-600 text-white hover:bg-success-700 focus:ring-success-500;
  }

  /* Cards */
  .card {
    @apply bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700;
    @apply transition-all duration-200;
  }

  .card-hover {
    @apply hover:shadow-medium hover:-translate-y-1;
  }

  /* Inputs */
  .input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm;
    @apply focus:ring-primary-500 focus:border-primary-500;
    @apply dark:bg-gray-700 dark:border-gray-600 dark:text-white;
    @apply transition-colors duration-200;
  }

  /* Navigation */
  .nav-link {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200;
    @apply text-gray-700 hover:text-primary-600 hover:bg-primary-50;
    @apply dark:text-gray-300 dark:hover:text-primary-400 dark:hover:bg-gray-800;
  }

  .nav-link-active {
    @apply bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300;
  }

  /* Badges */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-success {
    @apply bg-success-100 text-success-800 dark:bg-success-900 dark:text-success-300;
  }

  .badge-warning {
    @apply bg-warning-100 text-warning-800 dark:bg-warning-900 dark:text-warning-300;
  }

  .badge-error {
    @apply bg-error-100 text-error-800 dark:bg-error-900 dark:text-error-300;
  }

  .badge-info {
    @apply bg-info-100 text-info-800 dark:bg-info-900 dark:text-info-300;
  }

  /* Tables */
  .table {
    @apply min-w-full divide-y divide-gray-200 dark:divide-gray-700;
  }

  .table-header {
    @apply bg-gray-50 dark:bg-gray-800;
  }

  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm;
  }

  .table-row {
    @apply hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-150;
  }
}

@layer utilities {
  /* Animations personnalisées */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }

  /* Utilitaires pour l'accessibilité */
  .sr-only {
    @apply absolute w-px h-px p-0 -m-px overflow-hidden whitespace-nowrap border-0;
    clip: rect(0, 0, 0, 0);
  }

  /* Gradients personnalisés */
  .gradient-primary {
    background: linear-gradient(135deg, theme('colors.primary.500'), theme('colors.primary.600'));
  }

  .gradient-secondary {
    background: linear-gradient(135deg, theme('colors.gray.100'), theme('colors.gray.200'));
  }

  /* Utilitaires pour le dark mode */
  .dark-mode-transition {
    @apply transition-colors duration-300 ease-in-out;
  }
}
