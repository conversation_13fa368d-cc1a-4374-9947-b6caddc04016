import React, { useState, useRef } from 'react';
import {
  UserIcon,
  AcademicCapIcon,
  BriefcaseIcon,
  DocumentTextIcon,
  CloudArrowUpIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ArrowDownTrayIcon,
  PlusIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import FloatingThemeToggle from '../components/FloatingThemeToggle';
import SignatureCanvas from '../components/SignatureCanvas';
import { TextInput, SelectInput, TextareaInput, FileInput, baseInputClasses, baseLabelClasses } from '../components/FormField';

// Fonction pour télécharger la lettre de recommandation
const downloadRecommendationLetter = () => {
  const link = document.createElement('a');
  link.href = 'http://localhost:5000/api/documents/download/recommendation-letter';
  link.download = 'Lettre_de_recommendation.pdf';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

const Apply = () => {
  const [applicationMode, setApplicationMode] = useState(null); // 'manual' ou 'cv_import'
  const [currentStep, setCurrentStep] = useState(1);
  const [cvExtractionData, setCvExtractionData] = useState(null);
  const [isExtracting, setIsExtracting] = useState(false);
  const [formData, setFormData] = useState({
    // INFORMATIONS GÉNÉRALES
    civilite: '', // 'Mme' ou 'Mr'
    nom: '',
    nomMarital: '',
    prenom: '',
    situationActuelle: '', // 'en_activite', 'recherche_emploi', 'creation_entreprise'
    employeurInforme: '', // 'oui', 'non'
    photo: null,
    dateNaissance: '',
    villeNaissance: '',
    paysNaissance: '',
    nationalite: '',
    telephone: '',
    telPortable: '',
    email: '',

    // PARCOURS ACADÉMIQUE
    parcoursAcademique: [
      { annee: '', diplome: '', etablissement: '', pays: '' }
    ],

    // EXPÉRIENCE PROFESSIONNELLE
    nombreAnneesExperience: '',
    anneesResponsabilite: '',

    // EMPLOI ACTUEL OU DERNIER POSTE OCCUPÉ
    fonctionOccupee: '',
    dureePosteDebut: '', // mois/année
    dureePosteFin: '', // mois/année
    nombreSubordonnes: '',
    tempsPlein: true, // true pour temps plein, false pour temps partiel
    nomEntreprise: '',
    secteurActivite: '',
    adresseEntreprise: '',
    telephoneEntreprise: '',
    descriptionPoste: '',

    // EXPÉRIENCES PROFESSIONNELLES ANTÉRIEURES (dynamique)
    experiencesAnterieures: [],

    // Situation professionnelle satisfaisante
    situationSatisfaisante: '',
    detailsSituation: '',

    // LANGUES - Avec valeurs par défaut valides
    langues: {
      francais: {
        parle: 'courant', // 'courant', 'bon', 'moyen', 'debutant'
        lu: 'courant',
        ecrit: 'courant',
        test: ''
      },
      anglais: {
        parle: 'moyen',
        lu: 'moyen',
        ecrit: 'moyen',
        test: ''
      },
      autre: {
        nom: '',
        parle: '',
        lu: '',
        ecrit: '',
        test: ''
      }
    },
    certificationLangues: '', // Tests reconnus (TOEFL, IELTS, etc.)
    languesEtrangeresPro: '', // 'oui', 'non'
    contexteLangues: '',

    // AUTRES ACTIVITÉS
    vecuEtranger: '', // 'oui', 'non'
    detailsEtranger: '', // pays, circonstances, durée
    passionCentreInteret: '',

    // PROJET PROFESSIONNEL
    projetProfessionnel: '',
    attentesFormation: '',

    // CANDIDATURE
    candidatAutresProgrammes: '', // 'oui', 'non'
    autresProgrammes: '',
    financement: '', // 'personnel', 'employeur_total', 'employeur_partiel', 'autre'
    financementAutre: '',
    commentConnu: '', // 'site_internet', 'publicite', 'presse', 'entreprise', 'ami', 'recherche_internet', 'session_info', 'ancien_eleve', 'autre'
    commentConnuAutre: '',

    // PIÈCES À JOINDRE AU DOSSIER
    cv: null, // Un curriculum vitae détaillé
    cin: null, // Photocopie de votre pièce d'identité
    photo: null, // Une photo d'identité récente
    diplomes: null, // Copies certifiées conformes diplômes
    lettresRecommandation: null, // Une ou deux lettres de recommandation
    attestationExperience: null // Facultatif: Attestation d'expérience professionnelle
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [signatureData, setSignatureData] = useState(null);
  const signatureRef = useRef(null);

  // Fonction pour extraire les données du CV
  const handleCvUpload = async (file) => {
    setIsExtracting(true);
    const formData = new FormData();
    formData.append('cv', file);

    try {
      const response = await fetch('http://localhost:5000/api/cv/extract', {
        method: 'POST',
        body: formData
      });

      if (response.ok) {
        const result = await response.json();
        console.log('📄 Résultat extraction CV:', result);

        if (result.success && result.data) {
          setCvExtractionData(result.data);

          // Pré-remplir le formulaire avec les données extraites
          const mappedData = mapExtractedDataToForm(result.data);
          console.log('🔄 Données mappées:', mappedData);

          setFormData(prevData => ({
            ...prevData,
            ...mappedData
          }));

          setApplicationMode('cv_import');
          alert(`✅ CV analysé avec succès !
📧 Email: ${result.data.personalInfo?.email || 'Non trouvé'}
📞 Téléphone: ${result.data.personalInfo?.phone || 'Non trouvé'}
👤 Nom: ${result.data.personalInfo?.firstName || ''} ${result.data.personalInfo?.lastName || ''}
🎓 ${result.data.education?.length || 0} formation(s) trouvée(s)
💼 ${result.data.experience?.length || 0} expérience(s) trouvée(s)

Vérifiez les informations pré-remplies à chaque étape.`);
        } else {
          throw new Error(result.message || 'Erreur lors de l\'extraction du CV');
        }
      } else {
        const errorResult = await response.json();
        throw new Error(errorResult.message || 'Erreur lors de l\'extraction du CV');
      }
    } catch (error) {
      console.error('Erreur extraction CV:', error);
      alert('❌ Erreur lors de l\'analyse du CV. Veuillez réessayer ou remplir manuellement.');
    } finally {
      setIsExtracting(false);
    }
  };

  // Mapper les données extraites vers le format du formulaire
  const mapExtractedDataToForm = (extractedData) => {
    console.log('🔍 Mapping des données extraites:', extractedData);

    const mappedData = {};

    // Informations personnelles
    if (extractedData.personalInfo) {
      const personal = extractedData.personalInfo;
      if (personal.firstName) mappedData.prenom = personal.firstName;
      if (personal.lastName) mappedData.nom = personal.lastName;
      if (personal.email) mappedData.email = personal.email;
      if (personal.phone) {
        mappedData.telephone = personal.phone;
        mappedData.telPortable = personal.phone;
      }
      if (personal.birthDate) mappedData.dateNaissance = personal.birthDate;
      if (personal.nationality) mappedData.nationalite = personal.nationality;

      // Essayer de deviner la civilité basée sur le prénom
      if (personal.firstName) {
        const femaleNames = ['marie', 'sophie', 'claire', 'anne', 'julie', 'sarah', 'laura', 'emma', 'lea'];
        const firstName = personal.firstName.toLowerCase();
        if (femaleNames.some(name => firstName.includes(name))) {
          mappedData.civilite = 'Mme';
        } else {
          mappedData.civilite = 'Mr';
        }
      }
    }

    // Parcours académique
    if (extractedData.education && extractedData.education.length > 0) {
      mappedData.parcoursAcademique = extractedData.education.map(edu => ({
        annee: edu.year || '',
        diplome: edu.degree || '',
        etablissement: edu.institution || '',
        pays: edu.country || 'France'
      }));

      // S'assurer qu'il y a au moins une entrée vide
      if (mappedData.parcoursAcademique.length === 0) {
        mappedData.parcoursAcademique = [{ annee: '', diplome: '', etablissement: '', pays: '' }];
      }
    }

    // Expériences professionnelles
    if (extractedData.experience && extractedData.experience.length > 0) {
      // Mapper vers la structure attendue par le formulaire (experiencesAnterieures)
      mappedData.experiencesAnterieures = extractedData.experience.map(exp => ({
        fonction: exp.position || '',
        dureeDebut: exp.startDate || '',
        dureeFin: exp.endDate || '',
        nombreSubordonnes: '',
        tempsPlein: true,
        nomEntreprise: exp.company || '',
        secteurActivite: exp.sector || '',
        adresse: '',
        telephone: '',
        description: exp.description || ''
      }));

      console.log('✅ Expériences mappées vers experiencesAnterieures:', mappedData.experiencesAnterieures);

      // Remplir aussi l'emploi actuel avec la première expérience si elle est "Présent"
      const currentJob = extractedData.experience.find(exp =>
        exp.endDate && (exp.endDate.toLowerCase().includes('présent') || exp.endDate.toLowerCase().includes('present'))
      );

      if (currentJob) {
        mappedData.fonctionOccupee = currentJob.position || '';
        mappedData.nomEntreprise = currentJob.company || '';
        mappedData.secteurActivite = currentJob.sector || '';
        mappedData.descriptionPoste = currentJob.description || '';

        console.log('✅ Emploi actuel rempli:', {
          fonction: mappedData.fonctionOccupee,
          entreprise: mappedData.nomEntreprise
        });
      }
    }

    // Langues
    if (extractedData.languages && extractedData.languages.length > 0) {
      // Mapper vers la structure complexe du formulaire
      const languesMappees = extractedData.languages.slice(0, 3); // Max 3 langues

      if (languesMappees[0]) {
        mappedData.langues = {
          ...mappedData.langues,
          premiere: {
            nom: languesMappees[0].language || '',
            parle: languesMappees[0].level || 'Intermédiaire',
            lu: languesMappees[0].level || 'Intermédiaire',
            ecrit: languesMappees[0].level || 'Intermédiaire'
          }
        };
      }

      if (languesMappees[1]) {
        mappedData.langues = {
          ...mappedData.langues,
          deuxieme: {
            nom: languesMappees[1].language || '',
            parle: languesMappees[1].level || 'Intermédiaire',
            lu: languesMappees[1].level || 'Intermédiaire',
            ecrit: languesMappees[1].level || 'Intermédiaire'
          }
        };
      }

      if (languesMappees[2]) {
        mappedData.langues = {
          ...mappedData.langues,
          autre: {
            nom: languesMappees[2].language || '',
            parle: languesMappees[2].level || 'Intermédiaire',
            lu: languesMappees[2].level || 'Intermédiaire',
            ecrit: languesMappees[2].level || 'Intermédiaire'
          }
        };
      }
    }

    // Compétences
    if (extractedData.skills && extractedData.skills.length > 0) {
      mappedData.competences = extractedData.skills.slice(0, 10).join(', ');
    }

    console.log('✅ Données mappées finales:', mappedData);
    return mappedData;
  };

  const steps = [
    { id: 1, name: 'Informations Générales', icon: UserIcon },
    { id: 2, name: 'Parcours Académique', icon: AcademicCapIcon },
    { id: 3, name: 'Expérience Professionnelle', icon: BriefcaseIcon },
    { id: 4, name: 'Langues', icon: DocumentTextIcon },
    { id: 5, name: 'Autres Activités & Projet', icon: DocumentTextIcon },
    { id: 6, name: 'Candidature & Documents', icon: CloudArrowUpIcon },
    { id: 7, name: 'Signature', icon: CheckCircleIcon }
  ];

  // Fonction pour vérifier si un champ a été pré-rempli par le CV
  const isPreFilled = (fieldName) => {
    if (applicationMode !== 'cv_import' || !cvExtractionData) return false;

    // Mapping des noms de champs vers les chemins dans les données extraites
    const fieldMappings = {
      'personalInfo.firstName': 'personalInfo.firstName',
      'personalInfo.lastName': 'personalInfo.lastName',
      'personalInfo.email': 'personalInfo.email',
      'personalInfo.phone': 'personalInfo.phone',
      'personalInfo.birthDate': 'personalInfo.birthDate',
      'education': 'education',
      'experience': 'experience',
      'languages': 'languages',
      'skills': 'skills'
    };

    const mappedPath = fieldMappings[fieldName] || fieldName;
    const value = getNestedValue(cvExtractionData, mappedPath);

    return value !== undefined && value !== null && value !== '';
  };

  // Fonction utilitaire pour accéder aux valeurs imbriquées
  const getNestedValue = (obj, path) => {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  };

  // Composant pour afficher un badge "Pré-rempli"
  const PreFilledBadge = () => (
    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 ml-2">
      <CheckCircleIcon className="h-3 w-3 mr-1" />
      Pré-rempli
    </span>
  );

  const handleInputChange = (e) => {
    const { name, value } = e.target;

    // Gérer les champs imbriqués comme "experienceAnterieure1.fonction"
    if (name.includes('.')) {
      const [parentKey, childKey] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parentKey]: {
          ...prev[parentKey],
          [childKey]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleFileChange = (e) => {
    const { name, files } = e.target;
    if (files[0]) {
      setFormData(prev => ({
        ...prev,
        [name]: files[0]
      }));
    }
  };

  // Gestion des tableaux dynamiques
  const handleArrayChange = (arrayName, index, field, value) => {
    setFormData(prev => ({
      ...prev,
      [arrayName]: prev[arrayName].map((item, i) =>
        i === index ? { ...item, [field]: value } : item
      )
    }));
  };

  const addArrayItem = (arrayName, defaultItem) => {
    setFormData(prev => ({
      ...prev,
      [arrayName]: [...prev[arrayName], defaultItem]
    }));
  };

  const removeArrayItem = (arrayName, index) => {
    setFormData(prev => ({
      ...prev,
      [arrayName]: prev[arrayName].filter((_, i) => i !== index)
    }));
  };

  // Fonctions spécifiques pour les expériences professionnelles
  const addExperienceAnterieure = () => {
    const newExperience = {
      fonction: '',
      dureeDebut: '',
      dureeFin: '',
      nombreSubordonnes: '',
      tempsPlein: true,
      nomEntreprise: '',
      secteurActivite: '',
      adresse: '',
      telephone: '',
      description: ''
    };

    setFormData(prev => ({
      ...prev,
      experiencesAnterieures: [...prev.experiencesAnterieures, newExperience]
    }));
  };

  const removeExperienceAnterieure = (index) => {
    setFormData(prev => ({
      ...prev,
      experiencesAnterieures: prev.experiencesAnterieures.filter((_, i) => i !== index)
    }));
  };

  const handleExperienceChange = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      experiencesAnterieures: prev.experiencesAnterieures.map((exp, i) =>
        i === index ? { ...exp, [field]: value } : exp
      )
    }));
  };

  // Gestion des objets imbriqués (langues, expériences antérieures)
  const handleNestedChange = (objectName, field, value) => {
    setFormData(prev => ({
      ...prev,
      [objectName]: {
        ...prev[objectName],
        [field]: value
      }
    }));
  };

  const handleLangueChange = (langue, competence, value) => {
    setFormData(prev => ({
      ...prev,
      langues: {
        ...prev.langues,
        [langue]: {
          ...prev.langues[langue],
          [competence]: value
        }
      }
    }));
  };

  const validateStep = (step) => {
    const newErrors = {};

    switch (step) {
      case 1:
        // Informations générales - champs obligatoires
        if (!formData.prenom) newErrors.prenom = 'Prénom requis';
        if (!formData.nom) newErrors.nom = 'Nom requis';
        if (!formData.email) newErrors.email = 'Email requis';
        if (!formData.telephone) newErrors.telephone = 'Téléphone requis';
        if (!formData.telPortable) newErrors.telPortable = 'Tél. Portable requis';
        if (!formData.dateNaissance) newErrors.dateNaissance = 'Date de naissance requise';
        if (!formData.civilite) newErrors.civilite = 'Civilité requise';
        if (!formData.situationActuelle) newErrors.situationActuelle = 'Situation actuelle requise';
        if (!formData.villeNaissance) newErrors.villeNaissance = 'Ville de naissance requise';
        if (!formData.paysNaissance) newErrors.paysNaissance = 'Pays de naissance requis';
        if (!formData.nationalite) newErrors.nationalite = 'Nationalité requise';
        break;

      case 2:
        // Parcours académique - au moins un diplôme requis
        if (!formData.parcoursAcademique || formData.parcoursAcademique.length === 0) {
          newErrors.parcoursAcademique = 'Au moins un diplôme requis';
        } else {
          // Vérifier que chaque diplôme a les champs obligatoires
          formData.parcoursAcademique.forEach((diplome, index) => {
            if (!diplome.annee) newErrors[`parcoursAcademique_${index}_annee`] = 'Année requise';
            if (!diplome.diplome) newErrors[`parcoursAcademique_${index}_diplome`] = 'Diplôme requis';
            if (!diplome.etablissement) newErrors[`parcoursAcademique_${index}_etablissement`] = 'Établissement requis';
            if (!diplome.pays) newErrors[`parcoursAcademique_${index}_pays`] = 'Pays requis';
          });
        }
        break;

      case 3:
        // Expérience professionnelle - champs obligatoires de l'emploi actuel seulement
        if (formData.nombreAnneesExperience === undefined || formData.nombreAnneesExperience === '') {
          newErrors.nombreAnneesExperience = 'Nombre d\'années d\'expérience requis';
        }
        if (formData.anneesResponsabilite === undefined || formData.anneesResponsabilite === '') {
          newErrors.anneesResponsabilite = 'Années de responsabilité requises';
        }
        if (!formData.fonctionOccupee) newErrors.fonctionOccupee = 'Fonction occupée requise';
        if (!formData.dureePosteDebut) newErrors.dureePosteDebut = 'Date de début requise';
        if (!formData.nomEntreprise) newErrors.nomEntreprise = 'Nom de l\'entreprise requis';
        if (!formData.secteurActivite) newErrors.secteurActivite = 'Secteur d\'activité requis';
        if (!formData.adresseEntreprise) newErrors.adresseEntreprise = 'Adresse de l\'entreprise requise';
        if (!formData.telephoneEntreprise) newErrors.telephoneEntreprise = 'Téléphone de l\'entreprise requis';
        if (!formData.descriptionPoste) newErrors.descriptionPoste = 'Description du poste requise';

        // Les expériences antérieures sont optionnelles, pas de validation stricte
        break;

      case 4:
        // Langues - validation optionnelle car les valeurs par défaut sont définies
        // Pas de validation stricte nécessaire pour cette étape
        break;

      case 5:
        // Autres activités & Projet professionnel - champs obligatoires
        if (!formData.projetProfessionnel) newErrors.projetProfessionnel = 'Projet professionnel requis';
        if (!formData.attentesFormation) newErrors.attentesFormation = 'Attentes de la formation requises';
        if (!formData.financement) newErrors.financement = 'Mode de financement requis';
        break;

      case 6:
        // Documents - vérifier les documents obligatoires
        if (!formData.cv) newErrors.cv = 'CV requis';
        if (!formData.cin) newErrors.cin = 'CIN requise';
        if (!formData.photo) newErrors.photo = 'Photo requise';
        if (!formData.diplomes) newErrors.diplomes = 'Diplômes requis';
        if (!formData.lettresRecommandation) newErrors.lettresRecommandation = 'Lettres de recommandation requises';
        break;

      case 7:
        // Signature - vérifier que la signature est présente
        if (!signatureData) newErrors.signature = 'Signature manuscrite requise';
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, 7));
    }
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = async () => {
    if (!validateStep(7)) return;

    setIsSubmitting(true);

    try {
      const formDataToSend = new FormData();

      // Ajouter les champs texte avec sérialisation correcte des objets/tableaux
      Object.keys(formData).forEach(key => {
        const value = formData[key];

        if (value !== null && value !== '' && !(value instanceof File)) {
          if (typeof value === 'object') {
            // Sérialiser les objets et tableaux en JSON
            formDataToSend.append(key, JSON.stringify(value));
          } else {
            formDataToSend.append(key, value);
          }
        }
      });

      // Ajouter les fichiers avec les bons noms
      if (formData.cv) formDataToSend.append('cv', formData.cv);
      if (formData.cin) formDataToSend.append('cin', formData.cin);
      if (formData.photo) formDataToSend.append('photo', formData.photo);
      if (formData.diplomes) formDataToSend.append('diplomes', formData.diplomes);
      if (formData.lettresRecommandation) formDataToSend.append('lettresRecommandation', formData.lettresRecommandation);
      if (formData.attestationExperience) formDataToSend.append('attestationExperience', formData.attestationExperience);

      // Ajouter la signature manuscrite
      if (signatureData) {
        formDataToSend.append('signatureData', signatureData);
      }

      console.log('📤 Envoi de la candidature...');
      console.log('📋 Données à envoyer:', Object.fromEntries(formDataToSend.entries()));

      const response = await fetch('http://localhost:5000/api/applications/submit', {
        method: 'POST',
        body: formDataToSend
      });

      const result = await response.json();

      if (result.success) {
        let message = `✅ Candidature soumise avec succès !\nNuméro de candidature: ${result.applicationNumber}`;

        if (signatureData) {
          message += `\n\n✍️ Votre signature manuscrite a été enregistrée.`;
        }

        alert(message);

        // Rediriger vers une page de confirmation
        setTimeout(() => {
          window.location.href = '/';
        }, 2000);
      } else {
        throw new Error(result.message || 'Erreur lors de la soumission');
      }

    } catch (error) {
      console.error('❌ Erreur:', error);
      alert(`Erreur lors de la soumission: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-8">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">INFORMATIONS GÉNÉRALES</h3>

            {/* Message d'information pour le mode CV */}
            {applicationMode === 'cv_import' && (
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <div className="flex items-start">
                  <CheckCircleIcon className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 mr-3 flex-shrink-0" />
                  <div>
                    <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-1">
                      Informations pré-remplies depuis votre CV
                    </h4>
                    <p className="text-sm text-blue-700 dark:text-blue-300">
                      Les champs marqués avec un badge vert ont été automatiquement remplis.
                      Vérifiez et modifiez ces informations si nécessaire.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Civilité */}
            <div>
              <label className={baseLabelClasses}>Civilité *</label>
              <div className="flex space-x-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="civilite"
                    value="Mme"
                    checked={formData.civilite === 'Mme'}
                    onChange={handleInputChange}
                    className="mr-2 text-red-600 focus:ring-red-500"
                  />
                  Mme
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="civilite"
                    value="Mr"
                    checked={formData.civilite === 'Mr'}
                    onChange={handleInputChange}
                    className="mr-2 text-red-600 focus:ring-red-500"
                  />
                  Mr
                </label>
              </div>
            </div>

            {/* Nom, Nom Marital, Prénom */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <TextInput
                label="Nom"
                name="nom"
                value={formData.nom}
                onChange={handleInputChange}
                error={errors.nom}
                required
              />
              <TextInput
                label="Nom Marital"
                name="nomMarital"
                value={formData.nomMarital}
                onChange={handleInputChange}
              />
              <div>
                <label className={baseLabelClasses}>
                  Prénom *
                  {isPreFilled('personalInfo.firstName') && <PreFilledBadge />}
                </label>
                <TextInput
                  name="prenom"
                  value={formData.prenom}
                  onChange={handleInputChange}
                  error={errors.prenom}
                  required
                  className={isPreFilled('personalInfo.firstName') ? 'border-green-300 bg-green-50 dark:bg-green-900/20' : ''}
                />
              </div>
            </div>

            {/* Situation Actuelle */}
            <div>
              <label className={baseLabelClasses}>SITUATION ACTUELLE *</label>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="situationActuelle"
                    value="en_activite"
                    checked={formData.situationActuelle === 'en_activite'}
                    onChange={handleInputChange}
                    className="mr-2 text-red-600 focus:ring-red-500"
                  />
                  En activité
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="situationActuelle"
                    value="recherche_emploi"
                    checked={formData.situationActuelle === 'recherche_emploi'}
                    onChange={handleInputChange}
                    className="mr-2 text-red-600 focus:ring-red-500"
                  />
                  A la recherche d'emploi
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="situationActuelle"
                    value="creation_entreprise"
                    checked={formData.situationActuelle === 'creation_entreprise'}
                    onChange={handleInputChange}
                    className="mr-2 text-red-600 focus:ring-red-500"
                  />
                  En création/ reprise d'entreprise
                </label>
              </div>
            </div>

            {/* Question employeur informé */}
            {formData.situationActuelle === 'en_activite' && (
              <div>
                <label className={baseLabelClasses}>
                  Si vous êtes en activité, votre employeur est-il informé de votre candidature à l'Exécutive MBA ?
                </label>
                <div className="flex space-x-4">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="employeurInforme"
                      value="oui"
                      checked={formData.employeurInforme === 'oui'}
                      onChange={handleInputChange}
                      className="mr-2 text-red-600 focus:ring-red-500"
                    />
                    Oui
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="employeurInforme"
                      value="non"
                      checked={formData.employeurInforme === 'non'}
                      onChange={handleInputChange}
                      className="mr-2 text-red-600 focus:ring-red-500"
                    />
                    Non
                  </label>
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                  ⚠️ Toute modification intervenant au cours de l'année devra être obligatoirement signalée à la direction de l'EMBA.
                </p>
              </div>
            )}

            {/* Photo */}
            <div>
              <FileInput
                label="Photo *"
                name="photo"
                onChange={handleFileChange}
                accept="image/*"
                error={errors.photo}
                required
              />
            </div>

            {/* Date de naissance, ville, pays, nationalité */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <TextInput
                label="Date de naissance *"
                name="dateNaissance"
                type="date"
                value={formData.dateNaissance}
                onChange={handleInputChange}
                error={errors.dateNaissance}
                required
              />
              <TextInput
                label="Ville de naissance *"
                name="villeNaissance"
                value={formData.villeNaissance}
                onChange={handleInputChange}
                error={errors.villeNaissance}
                required
              />
              <TextInput
                label="Pays de naissance *"
                name="paysNaissance"
                value={formData.paysNaissance}
                onChange={handleInputChange}
                error={errors.paysNaissance}
                required
              />
              <TextInput
                label="Nationalité *"
                name="nationalite"
                value={formData.nationalite}
                onChange={handleInputChange}
                error={errors.nationalite}
                required
              />
            </div>

            {/* Contact */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <TextInput
                label="Téléphone"
                name="telephone"
                type="tel"
                value={formData.telephone}
                onChange={handleInputChange}
                error={errors.telephone}
                required
              />
              <TextInput
                label="Tél. Portable"
                name="telPortable"
                type="tel"
                value={formData.telPortable}
                onChange={handleInputChange}
                error={errors.telPortable}
                required
              />
            </div>

            <div>
              <label className={baseLabelClasses}>
                Email (caractères très lisibles) *
                {isPreFilled('personalInfo.email') && <PreFilledBadge />}
              </label>
              <TextInput
                name="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                error={errors.email}
                required
                placeholder="<EMAIL>"
                className={isPreFilled('personalInfo.email') ? 'border-green-300 bg-green-50 dark:bg-green-900/20' : ''}
              />
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">PARCOURS ACADÉMIQUE</h3>

            <div className="overflow-x-auto">
              <table className="w-full border-collapse border border-gray-300 dark:border-gray-600">
                <thead>
                  <tr className="bg-gray-50 dark:bg-gray-700">
                    <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left text-sm font-medium text-gray-900 dark:text-white">
                      Année
                    </th>
                    <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left text-sm font-medium text-gray-900 dark:text-white">
                      Diplôme obtenu
                    </th>
                    <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left text-sm font-medium text-gray-900 dark:text-white">
                      Nom de l'établissement
                    </th>
                    <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left text-sm font-medium text-gray-900 dark:text-white">
                      Pays
                    </th>
                    <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-center text-sm font-medium text-gray-900 dark:text-white">
                      Action
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {formData.parcoursAcademique.map((parcours, index) => (
                    <tr key={index}>
                      <td className="border border-gray-300 dark:border-gray-600 px-2 py-2">
                        <input
                          type="number"
                          value={parcours.annee}
                          onChange={(e) => {
                            const newParcours = [...formData.parcoursAcademique];
                            newParcours[index].annee = e.target.value;
                            setFormData({...formData, parcoursAcademique: newParcours});
                          }}
                          className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          placeholder="2023"
                          min="1950"
                          max="2030"
                        />
                      </td>
                      <td className="border border-gray-300 dark:border-gray-600 px-2 py-2">
                        <input
                          type="text"
                          value={parcours.diplome}
                          onChange={(e) => {
                            const newParcours = [...formData.parcoursAcademique];
                            newParcours[index].diplome = e.target.value;
                            setFormData({...formData, parcoursAcademique: newParcours});
                          }}
                          className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          placeholder="Master en..."
                        />
                      </td>
                      <td className="border border-gray-300 dark:border-gray-600 px-2 py-2">
                        <input
                          type="text"
                          value={parcours.etablissement}
                          onChange={(e) => {
                            const newParcours = [...formData.parcoursAcademique];
                            newParcours[index].etablissement = e.target.value;
                            setFormData({...formData, parcoursAcademique: newParcours});
                          }}
                          className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          placeholder="Université/École"
                        />
                      </td>
                      <td className="border border-gray-300 dark:border-gray-600 px-2 py-2">
                        <input
                          type="text"
                          value={parcours.pays}
                          onChange={(e) => {
                            const newParcours = [...formData.parcoursAcademique];
                            newParcours[index].pays = e.target.value;
                            setFormData({...formData, parcoursAcademique: newParcours});
                          }}
                          className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          placeholder="Tunisie"
                        />
                      </td>
                      <td className="border border-gray-300 dark:border-gray-600 px-2 py-2 text-center">
                        {formData.parcoursAcademique.length > 1 && (
                          <button
                            type="button"
                            onClick={() => {
                              const newParcours = formData.parcoursAcademique.filter((_, i) => i !== index);
                              setFormData({...formData, parcoursAcademique: newParcours});
                            }}
                            className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                          >
                            <XMarkIcon className="h-4 w-4" />
                          </button>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <button
              type="button"
              onClick={() => {
                setFormData({
                  ...formData,
                  parcoursAcademique: [...formData.parcoursAcademique, { annee: '', diplome: '', etablissement: '', pays: '' }]
                });
              }}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              + Ajouter un diplôme
            </button>
          </div>
        );

      case 3:
        return (
          <div className="space-y-8">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">EXPÉRIENCE PROFESSIONNELLE</h3>

            {/* Nombre d'années d'expérience */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <TextInput
                label="Nombre d'années d'expérience professionnelle *"
                name="nombreAnneesExperience"
                type="number"
                value={formData.nombreAnneesExperience}
                onChange={handleInputChange}
                error={errors.nombreAnneesExperience}
                required
                min="0"
                max="50"
              />
              <TextInput
                label="Dont en poste à responsabilité ou avec un statut de cadre/manager *"
                name="anneesResponsabilite"
                type="number"
                value={formData.anneesResponsabilite}
                onChange={handleInputChange}
                error={errors.anneesResponsabilite}
                required
                min="0"
                max="50"
              />
            </div>

            {/* EMPLOI ACTUEL OU DERNIER POSTE OCCUPÉ */}
            <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-6">
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                EMPLOI ACTUEL OU DERNIER POSTE OCCUPÉ
              </h4>

              <div className="space-y-4">
                <TextInput
                  label="Fonction occupée *"
                  name="fonctionOccupee"
                  value={formData.fonctionOccupee}
                  onChange={handleInputChange}
                  error={errors.fonctionOccupee}
                  required
                />

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <TextInput
                    label="Durée de (mois/année) *"
                    name="dureePosteDebut"
                    value={formData.dureePosteDebut}
                    onChange={handleInputChange}
                    error={errors.dureePosteDebut}
                    placeholder="01/2020"
                    required
                  />
                  <TextInput
                    label="à (mois/année)"
                    name="dureePosteFin"
                    value={formData.dureePosteFin}
                    onChange={handleInputChange}
                    placeholder="laisser vide si actuel"
                  />
                  <TextInput
                    label="Nombre de subordonnés"
                    name="nombreSubordonnes"
                    type="number"
                    value={formData.nombreSubordonnes}
                    onChange={handleInputChange}
                    min="0"
                  />
                </div>

                <div>
                  <label className={baseLabelClasses}>Type d'emploi *</label>
                  <div className="flex space-x-4">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="tempsPlein"
                        value="true"
                        checked={formData.tempsPlein === true}
                        onChange={(e) => setFormData({...formData, tempsPlein: e.target.value === 'true'})}
                        className="mr-2 text-red-600 focus:ring-red-500"
                      />
                      Temps plein
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="tempsPlein"
                        value="false"
                        checked={formData.tempsPlein === false}
                        onChange={(e) => setFormData({...formData, tempsPlein: e.target.value === 'true'})}
                        className="mr-2 text-red-600 focus:ring-red-500"
                      />
                      Temps partiel
                    </label>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <TextInput
                    label="Nom de l'entreprise *"
                    name="nomEntreprise"
                    value={formData.nomEntreprise}
                    onChange={handleInputChange}
                    error={errors.nomEntreprise}
                    required
                  />
                  <TextInput
                    label="Secteur d'activité *"
                    name="secteurActivite"
                    value={formData.secteurActivite}
                    onChange={handleInputChange}
                    error={errors.secteurActivite}
                    required
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <TextInput
                    label="Adresse *"
                    name="adresseEntreprise"
                    value={formData.adresseEntreprise}
                    onChange={handleInputChange}
                    error={errors.adresseEntreprise}
                    required
                  />
                  <TextInput
                    label="Téléphone *"
                    name="telephoneEntreprise"
                    type="tel"
                    value={formData.telephoneEntreprise}
                    onChange={handleInputChange}
                    error={errors.telephoneEntreprise}
                    required
                  />
                </div>

                <TextareaInput
                  label="Description précise du poste, responsabilités et évolution depuis votre entrée *"
                  name="descriptionPoste"
                  value={formData.descriptionPoste}
                  onChange={handleInputChange}
                  error={errors.descriptionPoste}
                  required
                  rows={4}
                  placeholder="Décrivez précisément votre poste, vos responsabilités et votre évolution..."
                />
              </div>
            </div>

            {/* EXPÉRIENCES PROFESSIONNELLES ANTÉRIEURES (Dynamique) */}
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                  EXPÉRIENCES PROFESSIONNELLES ANTÉRIEURES
                </h4>
                <button
                  type="button"
                  onClick={addExperienceAnterieure}
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800"
                >
                  <PlusIcon className="h-4 w-4 mr-1" />
                  Ajouter une expérience
                </button>
              </div>

              {formData.experiencesAnterieures.length === 0 && (
                <div className="text-center py-8 text-gray-500 dark:text-gray-400 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
                  <BriefcaseIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <p className="mt-2">Aucune expérience antérieure ajoutée</p>
                  <p className="text-sm">Cliquez sur "Ajouter une expérience" pour commencer</p>
                </div>
              )}

              {formData.experiencesAnterieures.map((experience, index) => (
                <div key={index} className="border border-gray-200 dark:border-gray-600 rounded-lg p-6">
                  <div className="flex justify-between items-center mb-4">
                    <h5 className="text-md font-medium text-gray-900 dark:text-white">
                      Expérience {index + 1}
                    </h5>
                    <button
                      type="button"
                      onClick={() => removeExperienceAnterieure(index)}
                      className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                    >
                      <XMarkIcon className="h-5 w-5" />
                    </button>
                  </div>

                  <div className="space-y-4">
                    <TextInput
                      label="Fonction occupée"
                      value={experience.fonction}
                      onChange={(e) => handleExperienceChange(index, 'fonction', e.target.value)}
                      placeholder="Ex: Responsable Marketing"
                    />

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <TextInput
                        label="Durée de (mois/année)"
                        value={experience.dureeDebut}
                        onChange={(e) => handleExperienceChange(index, 'dureeDebut', e.target.value)}
                        placeholder="01/2018"
                      />
                      <TextInput
                        label="à (mois/année)"
                        value={experience.dureeFin}
                        onChange={(e) => handleExperienceChange(index, 'dureeFin', e.target.value)}
                        placeholder="12/2019"
                      />
                      <TextInput
                        label="Nombre de subordonnés"
                        type="number"
                        value={experience.nombreSubordonnes}
                        onChange={(e) => handleExperienceChange(index, 'nombreSubordonnes', e.target.value)}
                        min="0"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Type d'emploi
                      </label>
                      <div className="flex space-x-4">
                        <label className="flex items-center">
                          <input
                            type="radio"
                            name={`tempsPlein_${index}`}
                            checked={experience.tempsPlein === true}
                            onChange={() => handleExperienceChange(index, 'tempsPlein', true)}
                            className="mr-2 text-red-600 focus:ring-red-500"
                          />
                          Temps plein
                        </label>
                        <label className="flex items-center">
                          <input
                            type="radio"
                            name={`tempsPlein_${index}`}
                            checked={experience.tempsPlein === false}
                            onChange={() => handleExperienceChange(index, 'tempsPlein', false)}
                            className="mr-2 text-red-600 focus:ring-red-500"
                          />
                          Temps partiel
                        </label>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <TextInput
                        label="Nom de l'entreprise"
                        value={experience.nomEntreprise}
                        onChange={(e) => handleExperienceChange(index, 'nomEntreprise', e.target.value)}
                        placeholder="Ex: ABC Corporation"
                      />
                      <TextInput
                        label="Secteur d'activité"
                        value={experience.secteurActivite}
                        onChange={(e) => handleExperienceChange(index, 'secteurActivite', e.target.value)}
                        placeholder="Ex: Technologie"
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <TextInput
                        label="Adresse"
                        value={experience.adresse}
                        onChange={(e) => handleExperienceChange(index, 'adresse', e.target.value)}
                        placeholder="Adresse complète"
                      />
                      <TextInput
                        label="Téléphone"
                        type="tel"
                        value={experience.telephone}
                        onChange={(e) => handleExperienceChange(index, 'telephone', e.target.value)}
                        placeholder="+216 XX XXX XXX"
                      />
                    </div>

                    <TextareaInput
                      label="Description du poste, responsabilités et réalisations"
                      value={experience.description}
                      onChange={(e) => handleExperienceChange(index, 'description', e.target.value)}
                      rows={3}
                      placeholder="Décrivez votre poste, vos responsabilités et vos principales réalisations..."
                    />
                  </div>
                </div>
              ))}
            </div>

            {/* Situation professionnelle satisfaisante */}
            <div>
              <TextareaInput
                label="Décrivez une situation professionnelle dans laquelle vous avez obtenu un résultat dont vous êtes particulièrement satisfait :"
                name="situationSatisfaisante"
                value={formData.situationSatisfaisante}
                onChange={handleInputChange}
                rows={4}
                placeholder="Décrivez une situation professionnelle dont vous êtes particulièrement fier..."
              />
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-8">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">LANGUES</h3>

            {/* Tableau des langues */}
            <div className="overflow-x-auto">
              <table className="w-full border-collapse border border-gray-300 dark:border-gray-600">
                <thead>
                  <tr className="bg-gray-50 dark:bg-gray-700">
                    <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left text-sm font-medium text-gray-900 dark:text-white">
                      Langue
                    </th>
                    <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-center text-sm font-medium text-gray-900 dark:text-white" colSpan="4">
                      NIVEAU D'EXPERTISE
                    </th>
                    <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-center text-sm font-medium text-gray-900 dark:text-white">
                      Test*
                    </th>
                  </tr>
                  <tr className="bg-gray-50 dark:bg-gray-700">
                    <th className="border border-gray-300 dark:border-gray-600 px-4 py-2"></th>
                    <th className="border border-gray-300 dark:border-gray-600 px-2 py-1 text-xs text-gray-900 dark:text-white">Courant</th>
                    <th className="border border-gray-300 dark:border-gray-600 px-2 py-1 text-xs text-gray-900 dark:text-white">Bon</th>
                    <th className="border border-gray-300 dark:border-gray-600 px-2 py-1 text-xs text-gray-900 dark:text-white">Moyen</th>
                    <th className="border border-gray-300 dark:border-gray-600 px-2 py-1 text-xs text-gray-900 dark:text-white">Débutant</th>
                    <th className="border border-gray-300 dark:border-gray-600 px-2 py-1 text-xs text-gray-900 dark:text-white"></th>
                  </tr>
                </thead>
                <tbody>
                  {/* Français */}
                  {['Parlé', 'Lu', 'Écrit'].map((competence, index) => (
                    <tr key={`francais-${competence}`}>
                      {index === 0 && (
                        <td className="border border-gray-300 dark:border-gray-600 px-4 py-2 font-medium text-gray-900 dark:text-white" rowSpan="3">
                          Français
                        </td>
                      )}
                      {index !== 0 && (
                        <td className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-sm text-gray-700 dark:text-gray-300">
                          {competence}
                        </td>
                      )}
                      {index === 0 && (
                        <td className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-sm text-gray-700 dark:text-gray-300">
                          {competence}
                        </td>
                      )}
                      {['courant', 'bon', 'moyen', 'debutant'].map((niveau) => (
                        <td key={niveau} className="border border-gray-300 dark:border-gray-600 px-2 py-2 text-center">
                          <input
                            type="radio"
                            name={`langues.francais.${competence.toLowerCase()}`}
                            value={niveau}
                            checked={formData.langues.francais[competence.toLowerCase()] === niveau}
                            onChange={(e) => {
                              const newLangues = {...formData.langues};
                              newLangues.francais[competence.toLowerCase()] = e.target.value;
                              setFormData({...formData, langues: newLangues});
                            }}
                            className="text-red-600 focus:ring-red-500"
                          />
                        </td>
                      ))}
                      {index === 0 && (
                        <td className="border border-gray-300 dark:border-gray-600 px-2 py-2" rowSpan="3">
                          <input
                            type="text"
                            value={formData.langues.francais.test || ''}
                            onChange={(e) => {
                              const newLangues = {...formData.langues};
                              newLangues.francais.test = e.target.value;
                              setFormData({...formData, langues: newLangues});
                            }}
                            className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-xs bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                            placeholder="Score"
                          />
                        </td>
                      )}
                    </tr>
                  ))}

                  {/* Anglais */}
                  {['Parlé', 'Lu', 'Écrit'].map((competence, index) => (
                    <tr key={`anglais-${competence}`}>
                      {index === 0 && (
                        <td className="border border-gray-300 dark:border-gray-600 px-4 py-2 font-medium text-gray-900 dark:text-white" rowSpan="3">
                          Anglais
                        </td>
                      )}
                      {index !== 0 && (
                        <td className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-sm text-gray-700 dark:text-gray-300">
                          {competence}
                        </td>
                      )}
                      {index === 0 && (
                        <td className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-sm text-gray-700 dark:text-gray-300">
                          {competence}
                        </td>
                      )}
                      {['courant', 'bon', 'moyen', 'debutant'].map((niveau) => (
                        <td key={niveau} className="border border-gray-300 dark:border-gray-600 px-2 py-2 text-center">
                          <input
                            type="radio"
                            name={`langues.anglais.${competence.toLowerCase()}`}
                            value={niveau}
                            checked={formData.langues.anglais[competence.toLowerCase()] === niveau}
                            onChange={(e) => {
                              const newLangues = {...formData.langues};
                              newLangues.anglais[competence.toLowerCase()] = e.target.value;
                              setFormData({...formData, langues: newLangues});
                            }}
                            className="text-red-600 focus:ring-red-500"
                          />
                        </td>
                      ))}
                      {index === 0 && (
                        <td className="border border-gray-300 dark:border-gray-600 px-2 py-2" rowSpan="3">
                          <input
                            type="text"
                            value={formData.langues.anglais.test || ''}
                            onChange={(e) => {
                              const newLangues = {...formData.langues};
                              newLangues.anglais.test = e.target.value;
                              setFormData({...formData, langues: newLangues});
                            }}
                            className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-xs bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                            placeholder="Score"
                          />
                        </td>
                      )}
                    </tr>
                  ))}

                  {/* Autre langue */}
                  {['Parlé', 'Lu', 'Écrit'].map((competence, index) => (
                    <tr key={`autre-${competence}`}>
                      {index === 0 && (
                        <td className="border border-gray-300 dark:border-gray-600 px-2 py-2" rowSpan="3">
                          <input
                            type="text"
                            value={formData.langues.autre.nom}
                            onChange={(e) => {
                              const newLangues = {...formData.langues};
                              newLangues.autre.nom = e.target.value;
                              setFormData({...formData, langues: newLangues});
                            }}
                            className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                            placeholder="Autre langue"
                          />
                        </td>
                      )}
                      {index !== 0 && (
                        <td className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-sm text-gray-700 dark:text-gray-300">
                          {competence}
                        </td>
                      )}
                      {index === 0 && (
                        <td className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-sm text-gray-700 dark:text-gray-300">
                          {competence}
                        </td>
                      )}
                      {['courant', 'bon', 'moyen', 'debutant'].map((niveau) => (
                        <td key={niveau} className="border border-gray-300 dark:border-gray-600 px-2 py-2 text-center">
                          <input
                            type="radio"
                            name={`langues.autre.${competence.toLowerCase()}`}
                            value={niveau}
                            checked={formData.langues.autre[competence.toLowerCase()] === niveau}
                            onChange={(e) => {
                              const newLangues = {...formData.langues};
                              newLangues.autre[competence.toLowerCase()] = e.target.value;
                              setFormData({...formData, langues: newLangues});
                            }}
                            className="text-red-600 focus:ring-red-500"
                          />
                        </td>
                      ))}
                      {index === 0 && (
                        <td className="border border-gray-300 dark:border-gray-600 px-2 py-2" rowSpan="3">
                          <input
                            type="text"
                            value={formData.langues.autre.test || ''}
                            onChange={(e) => {
                              const newLangues = {...formData.langues};
                              newLangues.autre.test = e.target.value;
                              setFormData({...formData, langues: newLangues});
                            }}
                            className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-xs bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                            placeholder="Score"
                          />
                        </td>
                      )}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <p className="text-xs text-gray-500 dark:text-gray-400">
              (*) Certification : merci de préciser dans cette zone les scores obtenus lors de tests reconnus de niveau de langues (TOEFL ou IELTS par exemple pour l'anglais) et l'échelle de notation
            </p>

            {/* Questions sur les langues étrangères */}
            <div className="space-y-4">
              <div>
                <label className={baseLabelClasses}>
                  Pratiquez-vous des langues étrangères dans votre vie professionnelle ?
                </label>
                <div className="flex space-x-4">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="languesEtrangeresPro"
                      value="oui"
                      checked={formData.languesEtrangeresPro === 'oui'}
                      onChange={handleInputChange}
                      className="mr-2 text-red-600 focus:ring-red-500"
                    />
                    Oui
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="languesEtrangeresPro"
                      value="non"
                      checked={formData.languesEtrangeresPro === 'non'}
                      onChange={handleInputChange}
                      className="mr-2 text-red-600 focus:ring-red-500"
                    />
                    Non
                  </label>
                </div>
              </div>

              {formData.languesEtrangeresPro === 'oui' && (
                <TextareaInput
                  label="Si oui, dans quel contexte ?"
                  name="contexteLangues"
                  value={formData.contexteLangues}
                  onChange={handleInputChange}
                  rows={2}
                  placeholder="Décrivez dans quel contexte professionnel vous utilisez les langues étrangères..."
                />
              )}
            </div>
          </div>
        );

      case 5:
        return (
          <div className="space-y-8">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">AUTRES ACTIVITÉS & PROJET PROFESSIONNEL</h3>

            {/* AUTRES ACTIVITÉS */}
            <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-6">
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">AUTRES ACTIVITÉS</h4>

              <div className="space-y-4">
                <div>
                  <label className={baseLabelClasses}>
                    Avez-vous déjà vécu dans un pays étranger pour des raisons professionnelles ?
                  </label>
                  <div className="flex space-x-4">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="vecuEtranger"
                        value="oui"
                        checked={formData.vecuEtranger === 'oui'}
                        onChange={handleInputChange}
                        className="mr-2 text-red-600 focus:ring-red-500"
                      />
                      Oui
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="vecuEtranger"
                        value="non"
                        checked={formData.vecuEtranger === 'non'}
                        onChange={handleInputChange}
                        className="mr-2 text-red-600 focus:ring-red-500"
                      />
                      Non
                    </label>
                  </div>
                </div>

                {formData.vecuEtranger === 'oui' && (
                  <TextareaInput
                    label="Si oui, dans quel(s) pays, dans quelles circonstances, et pendant combien de temps ?"
                    name="detailsEtranger"
                    value={formData.detailsEtranger}
                    onChange={handleInputChange}
                    rows={3}
                    placeholder="Décrivez votre expérience à l'étranger..."
                  />
                )}

                <TextareaInput
                  label="Avez-vous une passion ou un centre d'intérêt ? Que cela vous apporte-t-il ?"
                  name="passionCentreInteret"
                  value={formData.passionCentreInteret}
                  onChange={handleInputChange}
                  rows={3}
                  placeholder="Décrivez vos passions et ce qu'elles vous apportent..."
                />
              </div>
            </div>

            {/* VOTRE PROJET PROFESSIONNEL */}
            <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-6">
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">VOTRE PROJET PROFESSIONNEL</h4>

              <div className="space-y-4">
                <TextareaInput
                  label="Décrivez votre ou vos projets professionnels et de façon détaillée vos objectifs, vos attentes et vos besoins : *"
                  name="projetProfessionnel"
                  value={formData.projetProfessionnel}
                  onChange={handleInputChange}
                  error={errors.projetProfessionnel}
                  required
                  rows={5}
                  placeholder="Décrivez en détail votre projet professionnel, vos objectifs, attentes et besoins..."
                />

                <TextareaInput
                  label="Dans le cadre de ce projet professionnel, qu'attendez-vous de la formation Exécutive MBA ESPRIT ? *"
                  name="attentesFormation"
                  value={formData.attentesFormation}
                  onChange={handleInputChange}
                  error={errors.attentesFormation}
                  required
                  rows={4}
                  placeholder="Expliquez ce que vous attendez spécifiquement de la formation EMBA ESPRIT..."
                />
              </div>
            </div>

            {/* VOTRE CANDIDATURE */}
            <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-6">
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">VOTRE CANDIDATURE</h4>

              <div className="space-y-4">
                <div>
                  <label className={baseLabelClasses}>
                    Êtes-vous candidat(e) à d'autres programmes EMBA ?
                  </label>
                  <div className="flex space-x-4">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="candidatAutresProgrammes"
                        value="oui"
                        checked={formData.candidatAutresProgrammes === 'oui'}
                        onChange={handleInputChange}
                        className="mr-2 text-red-600 focus:ring-red-500"
                      />
                      Oui
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="candidatAutresProgrammes"
                        value="non"
                        checked={formData.candidatAutresProgrammes === 'non'}
                        onChange={handleInputChange}
                        className="mr-2 text-red-600 focus:ring-red-500"
                      />
                      Non
                    </label>
                  </div>
                </div>

                {formData.candidatAutresProgrammes === 'oui' && (
                  <TextareaInput
                    label="Si oui, lesquels ?"
                    name="autresProgrammes"
                    value={formData.autresProgrammes}
                    onChange={handleInputChange}
                    rows={2}
                    placeholder="Listez les autres programmes EMBA auxquels vous candidatez..."
                  />
                )}

                <div>
                  <label className={baseLabelClasses}>Comment financerez-vous votre Exécutive MBA ? *</label>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="financement"
                        value="personnel"
                        checked={formData.financement === 'personnel'}
                        onChange={handleInputChange}
                        className="mr-2 text-red-600 focus:ring-red-500"
                      />
                      Financement personnel
                    </label>
                    <div className="ml-6 space-y-2">
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="financement"
                          value="employeur_total"
                          checked={formData.financement === 'employeur_total'}
                          onChange={handleInputChange}
                          className="mr-2 text-red-600 focus:ring-red-500"
                        />
                        Financement par l'employeur : En totalité
                      </label>
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="financement"
                          value="employeur_partiel"
                          checked={formData.financement === 'employeur_partiel'}
                          onChange={handleInputChange}
                          className="mr-2 text-red-600 focus:ring-red-500"
                        />
                        Financement par l'employeur : Partiellement
                      </label>
                    </div>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="financement"
                        value="autre"
                        checked={formData.financement === 'autre'}
                        onChange={handleInputChange}
                        className="mr-2 text-red-600 focus:ring-red-500"
                      />
                      Autres (précisez) :
                      {formData.financement === 'autre' && (
                        <input
                          type="text"
                          name="financementAutre"
                          value={formData.financementAutre}
                          onChange={handleInputChange}
                          className="ml-2 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          placeholder="Précisez..."
                        />
                      )}
                    </label>
                  </div>
                </div>

                <div>
                  <label className={baseLabelClasses}>Comment avez-vous connu l'Exécutive MBA d'Esprit ?</label>
                  <div className="space-y-2">
                    {[
                      { value: 'site_internet', label: 'Site Internet ESPRIT' },
                      { value: 'publicite', label: 'Publicité' },
                      { value: 'presse', label: 'Un article dans la presse' },
                      { value: 'entreprise', label: 'Quelqu\'un dans votre entreprise' },
                      { value: 'ami', label: 'Un ami' },
                      { value: 'recherche_internet', label: 'Recherche sur Internet' },
                      { value: 'session_info', label: 'Session d\'information' },
                      { value: 'ancien_eleve', label: 'Ancien élève ESPRIT Exécutive MBA' }
                    ].map((option) => (
                      <label key={option.value} className="flex items-center">
                        <input
                          type="radio"
                          name="commentConnu"
                          value={option.value}
                          checked={formData.commentConnu === option.value}
                          onChange={handleInputChange}
                          className="mr-2 text-red-600 focus:ring-red-500"
                        />
                        {option.label}
                      </label>
                    ))}
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="commentConnu"
                        value="autre"
                        checked={formData.commentConnu === 'autre'}
                        onChange={handleInputChange}
                        className="mr-2 text-red-600 focus:ring-red-500"
                      />
                      Autres (précisez) :
                      {formData.commentConnu === 'autre' && (
                        <input
                          type="text"
                          name="commentConnuAutre"
                          value={formData.commentConnuAutre}
                          onChange={handleInputChange}
                          className="ml-2 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          placeholder="Précisez..."
                        />
                      )}
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 6:
        return (
          <div className="space-y-8">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">PIÈCES À JOINDRE AU DOSSIER</h3>

            <div className="bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-lg p-4 mb-6">
              <div className="flex items-start">
                <ExclamationTriangleIcon className="h-5 w-5 text-blue-600 dark:text-blue-400 mr-2 mt-0.5" />
                <div className="text-sm text-blue-800 dark:text-blue-200">
                  <p className="font-medium mb-1">Formats acceptés :</p>
                  <p>PDF, DOC, DOCX, JPG, PNG (max 5MB par fichier)</p>
                </div>
              </div>
            </div>

            {/* Documents obligatoires */}
            <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-6">
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Documents obligatoires</h4>

              <div className="space-y-4">
                <FileInput
                  label="Un curriculum vitae détaillé *"
                  name="cv"
                  onChange={handleFileChange}
                  accept=".pdf,.doc,.docx"
                  error={errors.cv}
                  required
                />
              </div>
            </div>

            {/* Documents en cas d'admissibilité */}
            <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-6">
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                En cas d'admissibilité : Nous faire parvenir le jour de votre entretien d'admission :
              </h4>

              <div className="space-y-4">
                <FileInput
                  label="Une Photocopie de votre pièce d'identité en cours de validité *"
                  name="cin"
                  onChange={handleFileChange}
                  accept=".pdf,.jpg,.jpeg,.png"
                  error={errors.cin}
                  required
                />

                <FileInput
                  label="Une photo d'identité récente *"
                  name="photo"
                  onChange={handleFileChange}
                  accept=".jpg,.jpeg,.png"
                  error={errors.photo}
                  required
                />

                <FileInput
                  label="Les copies certifiées conformes de votre diplôme de Baccalauréat et de vos diplômes universitaires *"
                  name="diplomes"
                  onChange={handleFileChange}
                  accept=".pdf,.jpg,.jpeg,.png"
                  error={errors.diplomes}
                  required
                />

                <FileInput
                  label="Une ou deux lettres de recommandation sur notre modèle ci-joint *"
                  name="lettresRecommandation"
                  onChange={handleFileChange}
                  accept=".pdf,.doc,.docx"
                  error={errors.lettresRecommandation}
                  required
                />
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Ces documents peuvent émaner de votre employeur.
                </p>
              </div>
            </div>

            {/* Documents facultatifs */}
            <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-6">
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Documents facultatifs</h4>

              <div className="space-y-4">
                <FileInput
                  label="Facultatif : Attestation d'expérience professionnelle (délivrée par l'employeur) sur les 3 dernières années, incluant le titre et la durée de l'emploi"
                  name="attestationExperience"
                  onChange={handleFileChange}
                  accept=".pdf,.doc,.docx"
                />
              </div>
            </div>

            {/* Récapitulatif */}
            <div className="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-6">
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Récapitulatif de votre candidature</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-700 dark:text-gray-300">
                <div>
                  <p><span className="font-medium">Nom :</span> {formData.nom} {formData.prenom}</p>
                  <p><span className="font-medium">Email :</span> {formData.email}</p>
                  <p><span className="font-medium">Téléphone :</span> {formData.telPortable || formData.telephone}</p>
                </div>
                <div>
                  <p><span className="font-medium">Fonction :</span> {formData.fonctionOccupee}</p>
                  <p><span className="font-medium">Entreprise :</span> {formData.nomEntreprise}</p>
                  <p><span className="font-medium">Expérience :</span> {formData.nombreAnneesExperience} ans</p>
                </div>
              </div>
            </div>

            {/* Avertissement avant soumission */}
            <div className="bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
              <div className="flex items-start">
                <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mr-2 mt-0.5" />
                <div className="text-sm text-yellow-800 dark:text-yellow-200">
                  <p className="font-medium mb-1">Avant de soumettre votre candidature :</p>
                  <ul className="list-disc list-inside space-y-1">
                    <li>Vérifiez que tous les champs obligatoires sont remplis</li>
                    <li>Assurez-vous que vos documents sont au bon format et lisibles</li>
                    <li>Relisez vos réponses, notamment votre projet professionnel</li>
                    <li>Une fois soumise, votre candidature sera examinée par notre équipe</li>
                    <li>Vous recevrez un email de confirmation avec un lien de signature numérique</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        );

      case 7:
        return (
          <div className="space-y-8">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">SIGNATURE MANUSCRITE</h3>

            <div className="bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-lg p-6 mb-6">
              <div className="flex items-start">
                <CheckCircleIcon className="h-6 w-6 text-blue-600 dark:text-blue-400 mr-3 mt-0.5" />
                <div className="text-sm text-blue-800 dark:text-blue-200">
                  <p className="font-medium mb-2">Finalisation de votre candidature</p>
                  <p>Votre signature manuscrite est requise pour valider définitivement votre candidature au programme EMBA.</p>
                  <p className="mt-2">En signant, vous confirmez :</p>
                  <ul className="list-disc list-inside mt-2 space-y-1">
                    <li>L'exactitude de toutes les informations fournies</li>
                    <li>Votre engagement à respecter les conditions du programme</li>
                    <li>Votre consentement au traitement de vos données personnelles</li>
                  </ul>
                </div>
              </div>
            </div>

            <SignatureCanvas
              ref={signatureRef}
              onSignatureChange={setSignatureData}
              required={true}
              className="mb-6"
            />

            {errors.signature && (
              <p className="text-red-500 text-sm mt-2">{errors.signature}</p>
            )}

            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <p className="text-sm text-gray-600 dark:text-gray-300">
                <strong>Note :</strong> Cette signature électronique a la même valeur légale qu'une signature manuscrite
                selon la réglementation en vigueur. Une fois signée, votre candidature sera définitivement soumise.
              </p>
            </div>
          </div>
        );

      default:
        return <div>Étape {currentStep} en cours de développement...</div>;
    }
  };

  // Écran de choix initial
  const renderChoiceScreen = () => (
    <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-8 transition-colors duration-200">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Comment souhaitez-vous procéder ?
          </h2>
          <p className="text-gray-600 dark:text-gray-300">
            Choisissez votre méthode de candidature préférée
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Option manuelle */}
          <div
            onClick={() => setApplicationMode('manual')}
            className="cursor-pointer group border-2 border-gray-200 dark:border-gray-600 rounded-lg p-6 hover:border-red-500 dark:hover:border-red-400 transition-all duration-200 hover:shadow-lg"
          >
            <div className="text-center">
              <DocumentTextIcon className="h-16 w-16 mx-auto text-gray-400 group-hover:text-red-500 dark:group-hover:text-red-400 transition-colors duration-200" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mt-4 mb-2">
                Remplir manuellement
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                Saisissez vos informations étape par étape dans le formulaire
              </p>
              <div className="mt-4 px-4 py-2 bg-gray-100 dark:bg-gray-700 rounded-lg">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  ⏱️ Temps estimé : 15-20 minutes
                </span>
              </div>
            </div>
          </div>

          {/* Option CV */}
          <div className="cursor-pointer group border-2 border-gray-200 dark:border-gray-600 rounded-lg p-6 hover:border-red-500 dark:hover:border-red-400 transition-all duration-200 hover:shadow-lg">
            <div className="text-center">
              <CloudArrowUpIcon className="h-16 w-16 mx-auto text-gray-400 group-hover:text-red-500 dark:group-hover:text-red-400 transition-colors duration-200" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mt-4 mb-2">
                Importer depuis mon CV
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                Téléchargez votre CV et nous pré-remplirons le formulaire
              </p>
              <div className="mt-4">
                <input
                  type="file"
                  accept=".pdf,.doc,.docx"
                  onChange={(e) => {
                    if (e.target.files[0]) {
                      handleCvUpload(e.target.files[0]);
                    }
                  }}
                  className="hidden"
                  id="cv-upload"
                  disabled={isExtracting}
                />
                <label
                  htmlFor="cv-upload"
                  className="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200 cursor-pointer"
                >
                  {isExtracting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Analyse en cours...
                    </>
                  ) : (
                    <>
                      <CloudArrowUpIcon className="h-4 w-4 mr-2" />
                      Choisir mon CV
                    </>
                  )}
                </label>
              </div>
              <div className="mt-2 px-4 py-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <span className="text-xs text-blue-600 dark:text-blue-400">
                  🚀 Temps estimé : 5-10 minutes
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-8 text-center">
          <p className="text-xs text-gray-500 dark:text-gray-400">
            💡 Avec l'import CV, vous pourrez vérifier et modifier toutes les informations à chaque étape
          </p>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm transition-colors duration-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex justify-between items-center">
            <div className="flex-1 text-center">
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Candidature EMBA</h1>
              <p className="text-lg text-gray-600 dark:text-gray-300 mt-2">
                Rejoignez la prochaine cohorte d'Executive MBA
              </p>
            </div>

            {/* Bouton de téléchargement lettre de recommandation */}
            <div className="flex-shrink-0">
              <button
                onClick={downloadRecommendationLetter}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200"
              >
                <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                Lettre de Recommandation
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Contenu conditionnel */}
      {!applicationMode ? (
        renderChoiceScreen()
      ) : (
        <>
          {/* Progress Steps */}
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center justify-between mb-8">
          {steps.map((step, index) => {
            const Icon = step.icon;
            const isActive = currentStep === step.id;
            const isCompleted = currentStep > step.id;

            return (
              <div key={step.id} className="flex items-center">
                <div className={`flex items-center justify-center w-12 h-12 rounded-full border-2 ${
                  isCompleted
                    ? 'bg-green-500 border-green-500 text-white'
                    : isActive
                      ? 'bg-red-600 border-red-600 text-white'
                      : 'bg-white border-gray-300 text-gray-400'
                }`}>
                  {isCompleted ? (
                    <CheckCircleIcon className="h-6 w-6" />
                  ) : (
                    <Icon className="h-6 w-6" />
                  )}
                </div>
                <div className="ml-3 hidden sm:block">
                  <p className={`text-sm font-medium ${
                    isActive ? 'text-red-600' : isCompleted ? 'text-green-600' : 'text-gray-500'
                  }`}>
                    {step.name}
                  </p>
                </div>
                {index < steps.length - 1 && (
                  <div className={`hidden sm:block w-16 h-0.5 ml-4 ${
                    isCompleted ? 'bg-green-500' : 'bg-gray-300'
                  }`} />
                )}
              </div>
            );
          })}
        </div>

        {/* Form Content */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 transition-colors duration-200">
          {renderStepContent()}

          {/* Navigation Buttons */}
          <div className="flex justify-between mt-8 pt-6 border-t border-gray-200">
            <button
              onClick={prevStep}
              disabled={currentStep === 1}
              className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Précédent
            </button>

            {currentStep < 7 ? (
              <button
                onClick={nextStep}
                className="px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700"
              >
                Suivant
              </button>
            ) : (
              <button
                onClick={handleSubmit}
                disabled={isSubmitting}
                className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
              >
                {isSubmitting ? 'Soumission...' : 'Soumettre la candidature'}
              </button>
            )}
          </div>
        </div>
        </div>
        </>
      )}

      {/* Floating Theme Toggle */}
      <FloatingThemeToggle />
    </div>
  );
};

export default Apply;
