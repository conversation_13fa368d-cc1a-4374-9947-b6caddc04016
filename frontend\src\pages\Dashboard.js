import React, { useState, useEffect } from 'react';
import StatsCard from '../components/Dashboard/StatsCard';

// Icônes de fallback simples en SVG
const AcademicCapIcon = ({ className }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="M4.26 10.147a60.436 60.436 0 00-.491 6.347A48.627 48.627 0 0112 20.904a48.627 48.627 0 018.232-4.41 60.46 60.46 0 00-.491-6.347m-15.482 0a50.57 50.57 0 00-2.658-.813A59.905 59.905 0 0112 3.493a59.902 59.902 0 0110.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.697 50.697 0 0112 13.489a50.702 50.702 0 017.74-3.342M6.75 15a.75.75 0 100-1.5.75.75 0 000 1.5zm0 0v-3.675A55.378 55.378 0 0112 8.443m-7.007 11.55A5.981 5.981 0 006.75 15.75v-1.5" />
  </svg>
);

const UserGroupIcon = ({ className }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z" />
  </svg>
);

const BookOpenIcon = ({ className }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 6.042A8.967 8.967 0 006 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 016 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 016-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0018 18a8.967 8.967 0 00-6 2.292m0-14.25v14.25" />
  </svg>
);

const CurrencyDollarIcon = ({ className }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v12m-3-2.818l.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
);

const ChartBarIcon = ({ className }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 013 19.875v-6.75zM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V8.625zM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V4.125z" />
  </svg>
);

const CalendarDaysIcon = ({ className }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5a2.25 2.25 0 002.25-2.25m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5a2.25 2.25 0 012.25 2.25v7.5M9 12.75h.008v.008H9V12.75zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm4.125 0h.008v.008h-.008V12.75zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
  </svg>
);

const DocumentTextIcon = ({ className }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
  </svg>
);

// Fonction pour mapper les noms d'icônes aux composants
const getIconComponent = (iconName) => {
  const iconMap = {
    'AcademicCapIcon': AcademicCapIcon,
    'UserGroupIcon': UserGroupIcon,
    'BookOpenIcon': BookOpenIcon,
    'CurrencyDollarIcon': CurrencyDollarIcon,
    'DocumentTextIcon': DocumentTextIcon,
    'CalendarDaysIcon': CalendarDaysIcon,
    'ChartBarIcon': ChartBarIcon
  };

  return iconMap[iconName] || DocumentTextIcon; // Icône par défaut
};

// Helper functions pour les classes CSS dynamiques
const getActivityIconClasses = (color) => {
  const classes = {
    primary: 'flex h-8 w-8 items-center justify-center rounded-full bg-primary-100 dark:bg-primary-900',
    success: 'flex h-8 w-8 items-center justify-center rounded-full bg-success-100 dark:bg-success-900',
    info: 'flex h-8 w-8 items-center justify-center rounded-full bg-info-100 dark:bg-info-900',
    warning: 'flex h-8 w-8 items-center justify-center rounded-full bg-warning-100 dark:bg-warning-900'
  };
  return classes[color] || classes.primary;
};

const getActivityTextClasses = (color) => {
  const classes = {
    primary: 'h-4 w-4 text-primary-600 dark:text-primary-400',
    success: 'h-4 w-4 text-success-600 dark:text-success-400',
    info: 'h-4 w-4 text-info-600 dark:text-info-400',
    warning: 'h-4 w-4 text-warning-600 dark:text-warning-400'
  };
  return classes[color] || classes.primary;
};

const Dashboard = () => {
  const [stats, setStats] = useState([]);
  const [recentActivities, setRecentActivities] = useState([]);
  const [upcomingEvents, setUpcomingEvents] = useState([]);
  const [loading, setLoading] = useState(true);

  // Charger les statistiques du dashboard
  const fetchDashboardStats = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/admin/dashboard/stats', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        const stats = data.stats; // Extraire les stats de la réponse

        // Transformer les données API en format pour les cartes
        const formattedStats = [
          {
            title: 'Total Étudiants',
            value: stats.students?.total?.toString() || '0',
            change: stats.students?.change || 'Aucun changement',
            changeType: stats.students?.trend > 0 ? 'increase' : stats.students?.trend < 0 ? 'decrease' : 'neutral',
            icon: AcademicCapIcon,
            color: 'primary'
          },
          {
            title: 'Professeurs Actifs',
            value: stats.professors?.total?.toString() || '0', // Utiliser total au lieu de active
            change: stats.professors?.change || 'Stable',
            changeType: stats.professors?.trend > 0 ? 'increase' : stats.professors?.trend < 0 ? 'decrease' : 'neutral',
            icon: UserGroupIcon,
            color: 'success'
          },
          {
            title: 'Cours Disponibles',
            value: stats.courses?.total?.toString() || '0',
            change: stats.courses?.change || 'Aucun changement',
            changeType: stats.courses?.trend > 0 ? 'increase' : stats.courses?.trend < 0 ? 'decrease' : 'neutral',
            icon: BookOpenIcon,
            color: 'info'
          },
          {
            title: 'Revenus ce Mois',
            value: `${stats.revenue?.total?.toLocaleString() || '0'} TND`,
            change: stats.revenue?.change || '0%',
            changeType: stats.revenue?.trend > 0 ? 'increase' : stats.revenue?.trend < 0 ? 'decrease' : 'neutral',
            icon: CurrencyDollarIcon,
            color: 'warning'
          }
        ];

        setStats(formattedStats);
      } else {
        // Données par défaut en cas d'erreur
        setStats([
          {
            title: 'Total Étudiants',
            value: '0',
            change: 'Données non disponibles',
            changeType: 'neutral',
            icon: AcademicCapIcon,
            color: 'primary'
          },
          {
            title: 'Professeurs Actifs',
            value: '0',
            change: 'Données non disponibles',
            changeType: 'neutral',
            icon: UserGroupIcon,
            color: 'success'
          },
          {
            title: 'Cours Disponibles',
            value: '0',
            change: 'Données non disponibles',
            changeType: 'neutral',
            icon: BookOpenIcon,
            color: 'info'
          },
          {
            title: 'Revenus ce Mois',
            value: '0 TND',
            change: 'Données non disponibles',
            changeType: 'neutral',
            icon: CurrencyDollarIcon,
            color: 'warning'
          }
        ]);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des statistiques:', error);
      // Utiliser des données par défaut
      setStats([
        {
          title: 'Total Étudiants',
          value: '0',
          change: 'Erreur de chargement',
          changeType: 'neutral',
          icon: AcademicCapIcon,
          color: 'primary'
        },
        {
          title: 'Professeurs Actifs',
          value: '0',
          change: 'Erreur de chargement',
          changeType: 'neutral',
          icon: UserGroupIcon,
          color: 'success'
        },
        {
          title: 'Cours Disponibles',
          value: '0',
          change: 'Erreur de chargement',
          changeType: 'neutral',
          icon: BookOpenIcon,
          color: 'info'
        },
        {
          title: 'Revenus ce Mois',
          value: '0 TND',
          change: 'Erreur de chargement',
          changeType: 'neutral',
          icon: CurrencyDollarIcon,
          color: 'warning'
        }
      ]);
    }
  };

  // Charger les activités récentes
  const fetchRecentActivities = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/admin/dashboard/activities', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setRecentActivities(data.activities || []);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des activités:', error);
      setRecentActivities([]);
    }
  };

  // Charger les événements à venir
  const fetchUpcomingEvents = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/admin/dashboard/events', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setUpcomingEvents(data.events || []);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des événements:', error);
      setUpcomingEvents([]);
    }
  };

  useEffect(() => {
    const loadDashboardData = async () => {
      setLoading(true);
      await Promise.all([
        fetchDashboardStats(),
        fetchRecentActivities(),
        fetchUpcomingEvents()
      ]);
      setLoading(false);
    };

    loadDashboardData();
  }, []);

  // Données supprimées - maintenant chargées depuis l'API

  if (loading) {
    return (
      <div className="w-full max-w-none space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4 xl:gap-6 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-gray-200 dark:bg-gray-700 h-32 rounded-lg"></div>
            ))}
          </div>
          <div className="grid grid-cols-1 gap-6 xl:grid-cols-2">
            <div className="bg-gray-200 dark:bg-gray-700 h-64 rounded-lg"></div>
            <div className="bg-gray-200 dark:bg-gray-700 h-64 rounded-lg"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-none space-y-6">
      {/* Header */}
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div className="min-w-0 flex-1">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 dark:text-white sm:text-3xl sm:tracking-tight">
            Dashboard
          </h2>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Vue d'ensemble de votre système EMBA
          </p>
        </div>
        <div className="flex-shrink-0">
          <button className="btn btn-primary w-full md:w-auto">
            <CalendarDaysIcon className="h-4 w-4 mr-2" />
            Voir Planning
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4 xl:gap-6">
        {stats.map((stat, index) => (
          <StatsCard
            key={index}
            title={stat.title}
            value={stat.value}
            change={stat.change}
            changeType={stat.changeType}
            icon={stat.icon}
            color={stat.color}
            className="animate-fade-in w-full"
            style={{ animationDelay: `${index * 100}ms` }}
          />
        ))}
      </div>

      {/* Content Grid */}
      <div className="grid grid-cols-1 gap-6 xl:grid-cols-2">

        {/* Recent Activities */}
        <div className="card">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Activités Récentes
            </h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Dernières actions dans le système
            </p>
          </div>
          <div className="p-6">
            <div className="flow-root">
              <ul className="-mb-8">
                {recentActivities.map((activity, index) => (
                  <li key={activity.id}>
                    <div className="relative pb-8">
                      {index !== recentActivities.length - 1 && (
                        <span
                          className="absolute left-4 top-4 -ml-px h-full w-0.5 bg-gray-200 dark:bg-gray-700"
                          aria-hidden="true"
                        />
                      )}
                      <div className="relative flex space-x-3">
                        <div className={getActivityIconClasses(activity.color)}>
                          {(() => {
                            const IconComponent = getIconComponent(activity.icon);
                            return <IconComponent className={getActivityTextClasses(activity.color)} />;
                          })()}
                        </div>
                        <div className="flex min-w-0 flex-1 justify-between space-x-4">
                          <div>
                            <p className="text-sm text-gray-900 dark:text-white">
                              {activity.message}
                            </p>
                          </div>
                          <div className="whitespace-nowrap text-right text-sm text-gray-500 dark:text-gray-400">
                            {activity.time}
                          </div>
                        </div>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        {/* Upcoming Events */}
        <div className="card">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Événements à Venir
            </h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Planning des prochains cours et événements
            </p>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {upcomingEvents.map((event) => (
                <div
                  key={event.id}
                  className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg"
                >
                  <div className="flex-1">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                      {event.title}
                    </h4>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {event.instructor}
                    </p>
                    <div className="mt-1 flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                      <span>{event.date}</span>
                      <span>{event.time}</span>
                      {event.students && (
                        <span>{event.students} étudiants</span>
                      )}
                    </div>
                  </div>
                  <div className="flex-shrink-0">
                    <CalendarDaysIcon className="h-5 w-5 text-gray-400" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
