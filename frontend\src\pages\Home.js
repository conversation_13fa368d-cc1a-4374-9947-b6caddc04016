import { Link } from 'react-router-dom';
import {
  AcademicCapIcon,
  ClockIcon,
  GlobeAltIcon,
  UserGroupIcon,
  ChartBarIcon,
  CheckCircleIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline';
import FloatingThemeToggle from '../components/FloatingThemeToggle';

const Home = () => {
  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 transition-colors duration-200">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-red-600 via-red-700 to-red-800 dark:from-red-800 dark:via-red-900 dark:to-gray-900 text-white">
        <div className="absolute inset-0 bg-black opacity-20 dark:opacity-40"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="flex items-center mb-6">
                <img
                  src="/logo-emba.png"
                  alt="EMBA Logo"
                  className="h-16 w-auto mr-4"
                  onError={(e) => {
                    e.target.style.display = 'none';
                  }}
                />
                <div>
                  <h1 className="text-4xl lg:text-6xl font-bold mb-2">
                    EMBA
                  </h1>
                  <p className="text-xl lg:text-2xl text-red-100">
                    Executive Master in Business Administration
                  </p>
                </div>
              </div>

              <h2 className="text-2xl lg:text-3xl font-semibold mb-6 leading-tight">
                Transformez votre carrière avec un MBA exécutif de classe mondiale
              </h2>

              <p className="text-lg lg:text-xl text-red-100 mb-8 leading-relaxed">
                Un programme de 2 ans conçu pour les dirigeants et cadres supérieurs
                qui souhaitent accélérer leur carrière tout en continuant à travailler.
              </p>

              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  to="/apply"
                  className="inline-flex items-center justify-center px-8 py-4 bg-white text-red-600 font-semibold rounded-lg hover:bg-red-50 transition-colors duration-200"
                >
                  Postuler maintenant
                  <ArrowRightIcon className="ml-2 h-5 w-5" />
                </Link>
                <Link
                  to="/program"
                  className="inline-flex items-center justify-center px-8 py-4 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-red-600 transition-colors duration-200"
                >
                  Découvrir le programme
                </Link>
              </div>
            </div>

            <div className="hidden lg:block">
              <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-8">
                <h3 className="text-2xl font-bold mb-6">Informations clés</h3>
                <div className="space-y-4">
                  <div className="flex items-center">
                    <ClockIcon className="h-6 w-6 mr-3 text-red-200" />
                    <span>Durée : 2 ans (temps partiel)</span>
                  </div>
                  <div className="flex items-center">
                    <AcademicCapIcon className="h-6 w-6 mr-3 text-red-200" />
                    <span>Format : Cours le weekend</span>
                  </div>
                  <div className="flex items-center">
                    <GlobeAltIcon className="h-6 w-6 mr-3 text-red-200" />
                    <span>Langue : Français & Anglais</span>
                  </div>
                  <div className="flex items-center">
                    <UserGroupIcon className="h-6 w-6 mr-3 text-red-200" />
                    <span>Cohorte : 25-30 participants</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-gray-50 dark:bg-gray-800 transition-colors duration-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-4xl font-bold text-red-600 mb-2">15+</div>
              <div className="text-gray-600 dark:text-gray-300">Années d'expérience</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-red-600 mb-2">500+</div>
              <div className="text-gray-600 dark:text-gray-300">Diplômés</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-red-600 mb-2">95%</div>
              <div className="text-gray-600 dark:text-gray-300">Taux de réussite</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-red-600 mb-2">85%</div>
              <div className="text-gray-600 dark:text-gray-300">Promotion obtenue</div>
            </div>
          </div>
        </div>
      </section>

      {/* About Program Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Pourquoi choisir notre EMBA ?
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Un programme d'excellence conçu pour les dirigeants ambitieux qui souhaitent
              développer leurs compétences stratégiques et leur leadership.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white dark:bg-gray-800 p-8 rounded-xl shadow-lg border border-gray-100 dark:border-gray-700 transition-colors duration-200">
              <div className="bg-red-100 dark:bg-red-900/30 w-16 h-16 rounded-lg flex items-center justify-center mb-6">
                <ChartBarIcon className="h-8 w-8 text-red-600 dark:text-red-400" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                Excellence Académique
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Curriculum de niveau international avec des professeurs reconnus
                et des méthodes pédagogiques innovantes.
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 p-8 rounded-xl shadow-lg border border-gray-100 dark:border-gray-700 transition-colors duration-200">
              <div className="bg-red-100 dark:bg-red-900/30 w-16 h-16 rounded-lg flex items-center justify-center mb-6">
                <UserGroupIcon className="h-8 w-8 text-red-600 dark:text-red-400" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                Réseau Professionnel
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Rejoignez une communauté d'élite de dirigeants et développez
                votre réseau professionnel à vie.
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 p-8 rounded-xl shadow-lg border border-gray-100 dark:border-gray-700 transition-colors duration-200">
              <div className="bg-red-100 dark:bg-red-900/30 w-16 h-16 rounded-lg flex items-center justify-center mb-6">
                <ClockIcon className="h-8 w-8 text-red-600 dark:text-red-400" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                Flexibilité
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Format adapté aux dirigeants : cours les weekends et modules
                intensifs pour concilier travail et études.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Specializations Section */}
      <section className="py-20 bg-gray-50 dark:bg-gray-800 transition-colors duration-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Spécialisations disponibles
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300">
              Choisissez votre parcours selon vos objectifs de carrière
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[
              'Management Général',
              'Finance & Stratégie',
              'Marketing & Digital',
              'Operations & Supply Chain',
              'Entrepreneuriat',
              'Transformation Digitale'
            ].map((specialization, index) => (
              <div key={index} className="bg-white dark:bg-gray-700 p-6 rounded-lg shadow-md border border-gray-100 dark:border-gray-600 transition-colors duration-200">
                <div className="flex items-center">
                  <CheckCircleIcon className="h-6 w-6 text-red-600 dark:text-red-400 mr-3" />
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {specialization}
                  </h3>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-red-600 dark:bg-red-800 transition-colors duration-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold text-white mb-4">
            Prêt à transformer votre carrière ?
          </h2>
          <p className="text-xl text-red-100 dark:text-red-200 mb-8 max-w-2xl mx-auto">
            Rejoignez la prochaine cohorte et donnez une nouvelle dimension à votre leadership.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/apply"
              className="inline-flex items-center justify-center px-8 py-4 bg-white dark:bg-gray-100 text-red-600 dark:text-red-700 font-semibold rounded-lg hover:bg-red-50 dark:hover:bg-gray-200 transition-colors duration-200"
            >
              Déposer ma candidature
              <ArrowRightIcon className="ml-2 h-5 w-5" />
            </Link>
            <Link
              to="/contact"
              className="inline-flex items-center justify-center px-8 py-4 border-2 border-white dark:border-gray-200 text-white dark:text-gray-200 font-semibold rounded-lg hover:bg-white hover:text-red-600 dark:hover:bg-gray-200 dark:hover:text-red-700 transition-colors duration-200"
            >
              Nous contacter
            </Link>
          </div>
        </div>
      </section>

      {/* Floating Theme Toggle */}
      <FloatingThemeToggle />
    </div>
  );
};

export default Home;
