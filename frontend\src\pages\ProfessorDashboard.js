import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useLocation, Link } from 'react-router-dom';
import {
  AcademicCapIcon,
  BookOpenIcon,
  CalendarDaysIcon,
  ClockIcon,
  UserGroupIcon,
  ChartBarIcon,
  DocumentTextIcon,
  StarIcon,
  Bars3Icon,
  XMarkIcon,
  UserIcon,
  BellIcon,
  CogIcon,
  ArrowRightOnRectangleIcon
} from '@heroicons/react/24/outline';
import { cn } from '../utils/cn';
import UserMenu from '../components/UserMenu';
import DashboardThemeToggle from '../components/DashboardThemeToggle';
import NotificationBell from '../components/NotificationBell';

const ProfessorDashboard = ({ children }) => {
  const { user, logout } = useAuth();
  const location = useLocation();
  const [professorData, setProfessorData] = useState(null);
  const [stats, setStats] = useState(null);
  const [upcomingClasses, setUpcomingClasses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Charger les données du professeur
  const fetchProfessorData = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/professors/profile', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setProfessorData(data.professor);
      }
    } catch (error) {
      console.error('Erreur lors du chargement du profil professeur:', error);
    }
  };

  // Charger les statistiques
  const fetchStats = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/professors/dashboard/stats', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setStats(data.stats);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des statistiques:', error);
    }
  };

  // Charger les cours à venir
  const fetchUpcomingClasses = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/professors/dashboard/upcoming-classes', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setUpcomingClasses(data.classes);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des cours à venir:', error);
    }
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await Promise.all([
        fetchProfessorData(),
        fetchStats(),
        fetchUpcomingClasses()
      ]);
      setLoading(false);
    };

    loadData();
  }, []);

  // Données par défaut si pas encore chargées
  const defaultStats = [
    {
      title: 'Cours Assignés',
      value: stats?.totalCourses || '0',
      change: 'Cette session',
      changeType: 'neutral',
      icon: BookOpenIcon,
      color: 'primary'
    },
    {
      title: 'Étudiants Total',
      value: stats?.totalStudents || '0',
      change: 'Tous cours confondus',
      changeType: 'neutral',
      icon: UserGroupIcon,
      color: 'success'
    },
    {
      title: 'Heures/Semaine',
      value: stats?.weeklyHours || '0',
      change: 'Charge actuelle',
      changeType: 'neutral',
      icon: ClockIcon,
      color: 'info'
    },
    {
      title: 'Note Moyenne',
      value: stats?.averageRating || '0.0',
      change: 'Évaluations étudiants',
      changeType: 'neutral',
      icon: StarIcon,
      color: 'warning'
    }
  ];

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  // Configuration du menu latéral
  const sidebarItems = [
    {
      name: 'Dashboard',
      href: '/professor/dashboard',
      icon: ChartBarIcon,
      current: location.pathname === '/professor/dashboard'
    },
    {
      name: 'Mes Cours',
      href: '/professor/courses',
      icon: BookOpenIcon,
      current: location.pathname === '/professor/courses'
    },
    {
      name: 'Notes & Évaluations',
      href: '/professor/grades',
      icon: DocumentTextIcon,
      current: location.pathname === '/professor/grades'
    },
    {
      name: 'Présences',
      href: '/professor/attendance',
      icon: CalendarDaysIcon,
      current: location.pathname === '/professor/attendance'
    },
    {
      name: 'Mes Étudiants',
      href: '/professor/students',
      icon: UserGroupIcon,
      current: location.pathname === '/professor/students'
    },
    {
      name: 'Mon Profil',
      href: '/professor/profile',
      icon: UserIcon,
      current: location.pathname === '/professor/profile'
    },
    {
      name: 'Notifications',
      href: '/professor/notifications',
      icon: BellIcon,
      current: location.pathname === '/professor/notifications'
    },
    {
      name: 'Paramètres',
      href: '/professor/settings',
      icon: CogIcon,
      current: location.pathname === '/professor/settings'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Sidebar pour mobile */}
      <div className={cn(
        "fixed inset-0 flex z-40 md:hidden",
        sidebarOpen ? "block" : "hidden"
      )}>
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)} />
        <div className="relative flex-1 flex flex-col max-w-xs w-full bg-white dark:bg-gray-800">
          <div className="absolute top-0 right-0 -mr-12 pt-2">
            <button
              type="button"
              className="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
              onClick={() => setSidebarOpen(false)}
            >
              <XMarkIcon className="h-6 w-6 text-white" />
            </button>
          </div>
          <div className="flex-1 h-0 pt-5 pb-4 overflow-y-auto">
            <div className="flex-shrink-0 flex items-center px-4">
              <div className="h-8 w-8 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
                <AcademicCapIcon className="h-5 w-5 text-primary-600 dark:text-primary-400" />
              </div>
              <span className="ml-2 text-lg font-semibold text-gray-900 dark:text-white">EMBA</span>
            </div>
            <nav className="mt-5 px-2 space-y-1">
              {sidebarItems.map((item) => {
                const Icon = item.icon;
                return (
                  <Link
                    key={item.name}
                    to={item.href}
                    className={cn(
                      item.current
                        ? 'bg-primary-100 text-primary-900 dark:bg-primary-900 dark:text-primary-100'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white',
                      'group flex items-center px-2 py-2 text-base font-medium rounded-md'
                    )}
                  >
                    <Icon className="mr-4 h-6 w-6" />
                    {item.name}
                  </Link>
                );
              })}
            </nav>
          </div>
          <div className="flex-shrink-0 px-2 pb-4">
            <button
              onClick={logout}
              className="group flex items-center w-full px-2 py-2 text-base font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white"
            >
              <ArrowRightOnRectangleIcon className="mr-4 h-6 w-6" />
              Déconnexion
            </button>
          </div>
        </div>
      </div>

      {/* Sidebar pour desktop */}
      <div className="hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0">
        <div className="flex-1 flex flex-col min-h-0 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700">
          <div className="flex-1 flex flex-col pt-5 pb-4 overflow-y-auto">
            <div className="flex items-center flex-shrink-0 px-4">
              <div className="h-8 w-8 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
                <AcademicCapIcon className="h-5 w-5 text-primary-600 dark:text-primary-400" />
              </div>
              <span className="ml-2 text-lg font-semibold text-gray-900 dark:text-white">EMBA Management</span>
            </div>
            <nav className="mt-5 flex-1 px-2 space-y-1">
              {sidebarItems.map((item) => {
                const Icon = item.icon;
                return (
                  <Link
                    key={item.name}
                    to={item.href}
                    className={cn(
                      item.current
                        ? 'bg-primary-100 text-primary-900 dark:bg-primary-900 dark:text-primary-100'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white',
                      'group flex items-center px-2 py-2 text-sm font-medium rounded-md'
                    )}
                  >
                    <Icon className="mr-3 h-6 w-6" />
                    {item.name}
                  </Link>
                );
              })}
            </nav>
          </div>
          <div className="flex-shrink-0 px-2 pb-4">
            <button
              onClick={logout}
              className="group flex items-center w-full px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white"
            >
              <ArrowRightOnRectangleIcon className="mr-3 h-6 w-6" />
              Déconnexion
            </button>
          </div>
        </div>
      </div>

      {/* Contenu principal */}
      <div className="md:pl-64 flex flex-col flex-1">
        {/* Header */}
        <div className="sticky top-0 z-10 bg-white dark:bg-gray-800 shadow">
          <div className="px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center">
                <button
                  type="button"
                  className="md:hidden -ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500"
                  onClick={() => setSidebarOpen(true)}
                >
                  <Bars3Icon className="h-6 w-6" />
                </button>
                <div className="ml-4 md:ml-0">
                  <h1 className="text-lg font-semibold text-gray-900 dark:text-white">
                    Dashboard - {professorData?.academicTitle} {user?.firstName} {user?.lastName}
                  </h1>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {professorData?.department}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <DashboardThemeToggle />
                <NotificationBell />
                <UserMenu />
              </div>
            </div>
          </div>
        </div>

        {/* Contenu de la page */}
        <main className="flex-1 p-6">
          <div className="max-w-7xl mx-auto">
            {children ? (
              children
            ) : (
              <div className="space-y-8">
                {/* Statistiques */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {defaultStats.map((stat, index) => {
                    const Icon = stat.icon;
                    return (
                      <div key={index} className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                        <div className="flex items-center">
                          <div className={`flex-shrink-0 p-3 rounded-lg ${
                            stat.color === 'primary' ? 'bg-primary-100 dark:bg-primary-900' :
                            stat.color === 'success' ? 'bg-green-100 dark:bg-green-900' :
                            stat.color === 'info' ? 'bg-blue-100 dark:bg-blue-900' :
                            stat.color === 'warning' ? 'bg-yellow-100 dark:bg-yellow-900' :
                            'bg-gray-100 dark:bg-gray-900'
                          }`}>
                            <Icon className={`h-6 w-6 ${
                              stat.color === 'primary' ? 'text-primary-600 dark:text-primary-400' :
                              stat.color === 'success' ? 'text-green-600 dark:text-green-400' :
                              stat.color === 'info' ? 'text-blue-600 dark:text-blue-400' :
                              stat.color === 'warning' ? 'text-yellow-600 dark:text-yellow-400' :
                              'text-gray-600 dark:text-gray-400'
                            }`} />
                          </div>
                          <div className="ml-4">
                            <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                              {stat.title}
                            </p>
                            <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                              {stat.value}
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                              {stat.change}
                            </p>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>

                {/* Cours à venir */}
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
                  <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h2 className="text-lg font-medium text-gray-900 dark:text-white">
                      Cours à venir
                    </h2>
                  </div>
                  <div className="p-6">
                    {upcomingClasses.length === 0 ? (
                      <div className="text-center py-8">
                        <CalendarDaysIcon className="mx-auto h-12 w-12 text-gray-400" />
                        <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                          Aucun cours programmé
                        </h3>
                        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                          Vos prochains cours apparaîtront ici.
                        </p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {upcomingClasses.map((classItem, index) => (
                          <div key={index} className="flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <div className="flex-shrink-0">
                              <div className="h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
                                <BookOpenIcon className="h-5 w-5 text-primary-600 dark:text-primary-400" />
                              </div>
                            </div>
                            <div className="ml-4 flex-1">
                              <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                                {classItem.courseName}
                              </h4>
                              <p className="text-sm text-gray-500 dark:text-gray-400">
                                {classItem.time} • {classItem.room} • {classItem.studentCount} étudiants
                              </p>
                            </div>
                            <div className="flex-shrink-0">
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                {classItem.status}
                              </span>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </main>
      </div>
    </div>
  );
};

export default ProfessorDashboard;
