import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import {
  EyeIcon,
  EyeSlashIcon,
  EnvelopeIcon,
  LockClosedIcon,
  ExclamationTriangleIcon,
  AcademicCapIcon,
  UserIcon,
  ChartBarIcon,
  BookOpenIcon,
  QuestionMarkCircleIcon
} from '@heroicons/react/24/outline';
import { useAuth } from '../contexts/AuthContext';
import FloatingThemeToggle from '../components/FloatingThemeToggle';
import StudentLoginGuide from '../components/StudentLoginGuide';

const PublicLogin = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [showStudentGuide, setShowStudentGuide] = useState(false);

  const { login, isLoading, error, clearError, isAuthenticated, user, getDefaultRouteByRole } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Rediriger si déjà connecté selon le rôle
  useEffect(() => {
    // Ne rediriger que si vraiment authentifié ET que l'utilisateur est chargé ET qu'on n'est plus en loading
    if (isAuthenticated && user && !isLoading) {
      const from = location.state?.from?.pathname;
      if (from) {
        navigate(from, { replace: true });
      } else {
        const defaultRoute = getDefaultRouteByRole(user.role);
        navigate(defaultRoute, { replace: true });
      }
    }
  }, [isAuthenticated, user, isLoading, navigate, location, getDefaultRouteByRole]);

  // Effacer les erreurs quand on change de champ
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        clearError();
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [error, clearError]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    if (error) {
      clearError();
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.email || !formData.password) {
      return;
    }

    const result = await login(formData);

    if (result.success) {
      const from = location.state?.from?.pathname;
      if (from) {
        navigate(from, { replace: true });
      } else {
        // Attendre que l'utilisateur soit chargé dans le contexte
        // La redirection se fera automatiquement via useEffect
      }
    }
  };

  const roleCards = [
    {
      role: 'admin',
      title: 'Administrateur',
      description: 'Gestion complète du système EMBA',
      icon: ChartBarIcon,
      features: ['Gestion des candidatures', 'Suivi des étudiants', 'Rapports et statistiques']
    },
    {
      role: 'student',
      title: 'Étudiant',
      description: 'Accès à votre parcours EMBA',
      icon: AcademicCapIcon,
      features: ['Cours et modules', 'Notes et évaluations', 'Planning personnel'],
      comingSoon: true
    },
    {
      role: 'teacher',
      title: 'Formateur',
      description: 'Interface d\'enseignement',
      icon: BookOpenIcon,
      features: ['Gestion des cours', 'Évaluation des étudiants', 'Ressources pédagogiques'],
      comingSoon: true
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-6">
            <div className="bg-red-600 p-4 rounded-full">
              <UserIcon className="h-12 w-12 text-white" />
            </div>
          </div>
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Connexion EMBA
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Accédez à votre espace selon votre profil
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
          {/* Formulaire de connexion */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 transition-colors duration-200">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
              Se connecter
            </h2>

            {/* Message d'erreur */}
            {error && (
              <div className="mb-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                <div className="flex items-center">
                  <ExclamationTriangleIcon className="h-5 w-5 text-red-400 mr-2" />
                  <p className="text-sm text-red-800 dark:text-red-200">{error}</p>
                </div>
              </div>
            )}

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Email */}
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Adresse email
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <EnvelopeIcon className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    required
                    value={formData.email}
                    onChange={handleChange}
                    className="block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors duration-200"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              {/* Mot de passe */}
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Mot de passe
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <LockClosedIcon className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    autoComplete="current-password"
                    required
                    value={formData.password}
                    onChange={handleChange}
                    className="block w-full pl-10 pr-10 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors duration-200"
                    placeholder="Votre mot de passe"
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeSlashIcon className="h-5 w-5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" />
                    ) : (
                      <EyeIcon className="h-5 w-5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" />
                    )}
                  </button>
                </div>
              </div>

              {/* Se souvenir de moi */}
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <input
                    id="remember-me"
                    name="remember-me"
                    type="checkbox"
                    checked={rememberMe}
                    onChange={(e) => setRememberMe(e.target.checked)}
                    className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 dark:border-gray-600 rounded"
                  />
                  <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                    Se souvenir de moi
                  </label>
                </div>
                <Link
                  to="/forgot-password"
                  className="text-sm text-red-600 dark:text-red-400 hover:text-red-500 dark:hover:text-red-300"
                >
                  Mot de passe oublié ?
                </Link>
              </div>

              {/* Bouton de connexion */}
              <button
                type="submit"
                disabled={isLoading}
                className="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Connexion...
                  </div>
                ) : (
                  'Se connecter'
                )}
              </button>
            </form>

            {/* Aide pour les étudiants */}
            <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-sm font-medium text-blue-900 dark:text-blue-100">
                    Vous êtes étudiant ?
                  </h3>
                  <p className="text-xs text-blue-800 dark:text-blue-200 mt-1">
                    Découvrez comment vous connecter avec vos identifiants
                  </p>
                </div>
                <button
                  onClick={() => setShowStudentGuide(true)}
                  className="flex items-center px-3 py-2 text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors"
                >
                  <QuestionMarkCircleIcon className="h-4 w-4 mr-1" />
                  Aide
                </button>
              </div>
            </div>

            {/* Lien retour */}
            <div className="mt-6 text-center">
              <Link
                to="/"
                className="text-sm text-gray-600 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400"
              >
                ← Retour à l'accueil
              </Link>
            </div>
          </div>

          {/* Informations sur les rôles */}
          <div className="space-y-6">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
              Espaces disponibles
            </h3>

            {roleCards.map((card) => {
              const Icon = card.icon;
              return (
                <div
                  key={card.role}
                  className={`bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border-l-4 transition-colors duration-200 ${
                    card.comingSoon
                      ? 'border-gray-300 dark:border-gray-600 opacity-75'
                      : 'border-red-500'
                  }`}
                >
                  <div className="flex items-start">
                    <div className={`p-2 rounded-lg mr-4 ${
                      card.comingSoon
                        ? 'bg-gray-100 dark:bg-gray-700'
                        : 'bg-red-100 dark:bg-red-900/20'
                    }`}>
                      <Icon className={`h-6 w-6 ${
                        card.comingSoon
                          ? 'text-gray-400'
                          : 'text-red-600 dark:text-red-400'
                      }`} />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center">
                        <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                          {card.title}
                        </h4>
                        {card.comingSoon && (
                          <span className="ml-2 px-2 py-1 text-xs bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-200 rounded-full">
                            Bientôt disponible
                          </span>
                        )}
                      </div>
                      <p className="text-gray-600 dark:text-gray-300 mt-1">
                        {card.description}
                      </p>
                      <ul className="mt-3 space-y-1">
                        {card.features.map((feature, index) => (
                          <li key={index} className="text-sm text-gray-500 dark:text-gray-400 flex items-center">
                            <span className="w-1.5 h-1.5 bg-red-400 rounded-full mr-2"></span>
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Floating Theme Toggle */}
      <FloatingThemeToggle />

      {/* Student Login Guide Modal */}
      <StudentLoginGuide
        isOpen={showStudentGuide}
        onClose={() => setShowStudentGuide(false)}
      />
    </div>
  );
};

export default PublicLogin;
