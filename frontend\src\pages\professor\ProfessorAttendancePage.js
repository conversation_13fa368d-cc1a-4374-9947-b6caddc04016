import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import {
  CalendarDaysIcon,
  UserGroupIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  PlusIcon,
  ArrowDownTrayIcon
} from '@heroicons/react/24/outline';
import { cn } from '../../utils/cn';

const ProfessorAttendancePage = () => {
  const { user } = useAuth();
  const [courses, setCourses] = useState([]);
  const [selectedCourse, setSelectedCourse] = useState('');
  const [attendanceRecords, setAttendanceRecords] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);

  // Charger les cours du professeur
  const fetchCourses = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/professors/courses', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setCourses(data.courses);
        if (data.courses.length > 0) {
          setSelectedCourse(data.courses[0]._id);
        }
      }
    } catch (error) {
      console.error('Erreur lors du chargement des cours:', error);
    }
  };

  // Charger les présences pour un cours
  const fetchAttendance = async (courseId) => {
    if (!courseId) return;
    
    try {
      const response = await fetch(`http://localhost:5000/api/professors/courses/${courseId}/attendance`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setAttendanceRecords(data.attendance);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des présences:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCourses();
  }, []);

  useEffect(() => {
    if (selectedCourse) {
      setLoading(true);
      fetchAttendance(selectedCourse);
    }
  }, [selectedCourse]);

  const selectedCourseData = courses.find(c => c._id === selectedCourse);

  // Calculer les statistiques de présence
  const calculateStats = () => {
    if (attendanceRecords.length === 0) return { totalSessions: 0, averageAttendance: 0, presentCount: 0, absentCount: 0 };
    
    const totalSessions = attendanceRecords.length;
    const totalStudents = attendanceRecords.reduce((sum, record) => sum + record.students.length, 0);
    const presentCount = attendanceRecords.reduce((sum, record) => 
      sum + record.students.filter(s => s.status === 'present').length, 0
    );
    const absentCount = totalStudents - presentCount;
    const averageAttendance = totalStudents > 0 ? Math.round((presentCount / totalStudents) * 100) : 0;

    return { totalSessions, averageAttendance, presentCount, absentCount };
  };

  const stats = calculateStats();

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Présences</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Gérez les présences de vos étudiants
          </p>
        </div>
        <div className="flex space-x-3">
          <button className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
            <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
            Exporter
          </button>
          <button
            onClick={() => setShowAddModal(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Nouvelle Session
          </button>
        </div>
      </div>

      {/* Sélecteur de cours */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center space-x-4">
          <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Cours :
          </label>
          <select
            value={selectedCourse}
            onChange={(e) => setSelectedCourse(e.target.value)}
            className="block w-64 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            {courses.map((course) => (
              <option key={course._id} value={course._id}>
                {course.name} ({course.code})
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Statistiques de présence */}
      {selectedCourseData && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 p-3 rounded-lg bg-blue-100 dark:bg-blue-900">
                <CalendarDaysIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Sessions</p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {stats.totalSessions}
                </p>
              </div>
            </div>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 p-3 rounded-lg bg-green-100 dark:bg-green-900">
                <CheckCircleIcon className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Présents</p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {stats.presentCount}
                </p>
              </div>
            </div>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 p-3 rounded-lg bg-red-100 dark:bg-red-900">
                <XCircleIcon className="h-6 w-6 text-red-600 dark:text-red-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Absents</p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {stats.absentCount}
                </p>
              </div>
            </div>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 p-3 rounded-lg bg-yellow-100 dark:bg-yellow-900">
                <UserGroupIcon className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Taux Présence</p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {stats.averageAttendance}%
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Liste des sessions de présence */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">
            Sessions de Présence - {selectedCourseData?.name}
          </h2>
        </div>
        <div className="p-6">
          {attendanceRecords.length === 0 ? (
            <div className="text-center py-12">
              <CalendarDaysIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                Aucune session enregistrée
              </h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Commencez par créer une session de présence pour ce cours.
              </p>
              <div className="mt-6">
                <button
                  onClick={() => setShowAddModal(true)}
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700"
                >
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Créer une session
                </button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {attendanceRecords.map((record) => {
                const presentCount = record.students.filter(s => s.status === 'present').length;
                const totalStudents = record.students.length;
                const attendanceRate = totalStudents > 0 ? Math.round((presentCount / totalStudents) * 100) : 0;

                return (
                  <div key={record._id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                          Session du {new Date(record.date).toLocaleDateString('fr-FR')}
                        </h3>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {new Date(record.date).toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}
                        </p>
                      </div>
                      <div className="flex items-center space-x-4">
                        <div className="text-right">
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {presentCount}/{totalStudents} présents
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {attendanceRate}% de présence
                          </p>
                        </div>
                        <div className={cn(
                          "w-16 h-16 rounded-full flex items-center justify-center text-lg font-bold",
                          attendanceRate >= 80 ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200" :
                          attendanceRate >= 60 ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200" :
                          "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                        )}>
                          {attendanceRate}%
                        </div>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {record.students.map((student) => (
                        <div key={student.studentId} className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                          <div className={cn(
                            "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center",
                            student.status === 'present' 
                              ? "bg-green-100 dark:bg-green-900"
                              : "bg-red-100 dark:bg-red-900"
                          )}>
                            {student.status === 'present' ? (
                              <CheckCircleIcon className="h-5 w-5 text-green-600 dark:text-green-400" />
                            ) : (
                              <XCircleIcon className="h-5 w-5 text-red-600 dark:text-red-400" />
                            )}
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                              {student.firstName} {student.lastName}
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                              {student.status === 'present' ? 'Présent' : 'Absent'}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProfessorAttendancePage;
