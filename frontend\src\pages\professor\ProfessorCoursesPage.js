import React, { useState, useEffect } from 'react';
import {
  AcademicCapIcon,
  BookOpenIcon,
  UserGroupIcon,
  ClockIcon,
  CalendarDaysIcon,
  TrashIcon,
  EyeIcon,
  DocumentTextIcon,
  FolderIcon
} from '@heroicons/react/24/outline';
import { cn } from '../../utils/cn';
import SyllabusManager from '../../components/professor/SyllabusManager';
import CourseDocumentManager from '../../components/professor/CourseDocumentManager';

const ProfessorCoursesPage = () => {
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedCourse, setSelectedCourse] = useState(null);
  const [activeTab, setActiveTab] = useState('overview'); // 'overview', 'syllabus', 'documents'

  // Charger les cours du professeur
  const fetchCourses = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/professors/courses', {
        headers: {
          'Authorization': `Bear<PERSON> ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setCourses(data.courses);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des cours:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCourses();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Mes Cours</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Gérez vos cours et suivez les progrès des étudiants
          </p>
        </div>

      </div>

      {/* Statistiques rapides */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0 p-3 rounded-lg bg-blue-100 dark:bg-blue-900">
              <BookOpenIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Cours</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">{courses.length}</p>
            </div>
          </div>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0 p-3 rounded-lg bg-green-100 dark:bg-green-900">
              <UserGroupIcon className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Étudiants Total</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {courses.reduce((total, course) => total + (course.enrolledStudents || 0), 0)}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0 p-3 rounded-lg bg-yellow-100 dark:bg-yellow-900">
              <ClockIcon className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Heures/Semaine</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {courses.reduce((total, course) => total + (course.weeklyHours || 0), 0)}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0 p-3 rounded-lg bg-purple-100 dark:bg-purple-900">
              <CalendarDaysIcon className="h-6 w-6 text-purple-600 dark:text-purple-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Cours Actifs</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {courses.filter(course => course.status === 'active').length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Cours par Promotion */}
      {courses.length === 0 ? (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-12 text-center">
          <BookOpenIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
            Aucun cours assigné
          </h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Vos cours apparaîtront ici une fois qu'ils vous seront assignés par l'administration.
          </p>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Cours EMBA1 */}
          {(() => {
            const emba1Courses = courses.filter(course => course.promotion === 'EMBA1');
            return emba1Courses.length > 0 && (
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
                <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 p-2 rounded-lg bg-blue-100 dark:bg-blue-900">
                      <AcademicCapIcon className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div className="ml-3">
                      <h2 className="text-lg font-medium text-gray-900 dark:text-white">
                        EMBA1 - Première Année
                      </h2>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {emba1Courses.length} cours • {emba1Courses.reduce((total, course) => total + (course.enrolledStudents || 0), 0)} étudiants
                      </p>
                    </div>
                  </div>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {emba1Courses.map((course) => (
                      <CourseCard
                        key={course._id}
                        course={course}
                        onSelect={setSelectedCourse}
                        isSelected={selectedCourse?._id === course._id}
                      />
                    ))}
                  </div>
                </div>
              </div>
            );
          })()}

          {/* Cours EMBA2 */}
          {(() => {
            const emba2Courses = courses.filter(course => course.promotion === 'EMBA2');
            return emba2Courses.length > 0 && (
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
                <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 p-2 rounded-lg bg-purple-100 dark:bg-purple-900">
                      <AcademicCapIcon className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                    </div>
                    <div className="ml-3">
                      <h2 className="text-lg font-medium text-gray-900 dark:text-white">
                        EMBA2 - Deuxième Année
                      </h2>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {emba2Courses.length} cours • {emba2Courses.reduce((total, course) => total + (course.enrolledStudents || 0), 0)} étudiants
                      </p>
                    </div>
                  </div>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {emba2Courses.map((course) => (
                      <CourseCard
                        key={course._id}
                        course={course}
                        onSelect={setSelectedCourse}
                        isSelected={selectedCourse?._id === course._id}
                      />
                    ))}
                  </div>
                </div>
              </div>
            );
          })()}
        </div>
      )}

      {/* Section de gestion du cours sélectionné */}
      {selectedCourse && (
        <div className="mt-8">
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
            {/* Header du cours sélectionné */}
            <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                    {selectedCourse.title || selectedCourse.name}
                  </h2>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {selectedCourse.courseCode || selectedCourse.code} • {selectedCourse.class}
                  </p>
                </div>
                <button
                  onClick={() => setSelectedCourse(null)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <TrashIcon className="h-5 w-5" />
                </button>
              </div>
            </div>

            {/* Onglets */}
            <div className="border-b border-gray-200 dark:border-gray-700">
              <nav className="-mb-px flex space-x-8 px-6">
                <button
                  onClick={() => setActiveTab('overview')}
                  className={cn(
                    "py-4 px-1 border-b-2 font-medium text-sm",
                    activeTab === 'overview'
                      ? "border-primary-500 text-primary-600 dark:text-primary-400"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"
                  )}
                >
                  <BookOpenIcon className="h-5 w-5 mr-2 inline-block" />
                  Aperçu
                </button>
                <button
                  onClick={() => setActiveTab('syllabus')}
                  className={cn(
                    "py-4 px-1 border-b-2 font-medium text-sm",
                    activeTab === 'syllabus'
                      ? "border-primary-500 text-primary-600 dark:text-primary-400"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"
                  )}
                >
                  <DocumentTextIcon className="h-5 w-5 mr-2 inline-block" />
                  Syllabus
                </button>
                <button
                  onClick={() => setActiveTab('documents')}
                  className={cn(
                    "py-4 px-1 border-b-2 font-medium text-sm",
                    activeTab === 'documents'
                      ? "border-primary-500 text-primary-600 dark:text-primary-400"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"
                  )}
                >
                  <FolderIcon className="h-5 w-5 mr-2 inline-block" />
                  Documents
                </button>
              </nav>
            </div>

            {/* Contenu des onglets */}
            <div className="p-6">
              {activeTab === 'overview' && (
                <CourseOverview course={selectedCourse} />
              )}
              {activeTab === 'syllabus' && (
                <SyllabusManager
                  courseId={selectedCourse._id}
                  courseName={selectedCourse.title || selectedCourse.name}
                />
              )}
              {activeTab === 'documents' && (
                <CourseDocumentManager
                  courseId={selectedCourse._id}
                  courseName={selectedCourse.title || selectedCourse.name}
                />
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Composant CourseCard réutilisable
const CourseCard = ({ course, onSelect, isSelected }) => {
  return (
    <div className={cn(
      "border rounded-lg p-6 hover:shadow-md transition-all cursor-pointer",
      isSelected
        ? "border-primary-500 bg-primary-50 dark:bg-primary-900/20 shadow-md"
        : "border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"
    )}
    onClick={() => onSelect(course)}>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            {course.title || course.name}
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            {course.courseCode || course.code} • {course.class}
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-300 mt-2">
            {course.shortDescription || course.description}
          </p>
        </div>
        <span className={cn(
          "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",
          course.status === 'active'
            ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
            : course.status === 'draft'
            ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
            : "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
        )}>
          {course.status === 'active' ? 'Actif' : course.status === 'draft' ? 'Brouillon' : 'Inactif'}
        </span>
      </div>

      <div className="mt-4 flex items-center text-sm text-gray-500 dark:text-gray-400">
        <UserGroupIcon className="h-4 w-4 mr-1" />
        <span className="mr-4">{course.capacity?.current || course.enrolledStudents || 0} étudiants</span>
        <ClockIcon className="h-4 w-4 mr-1" />
        <span>{course.contactHours || course.weeklyHours || 0}h</span>
      </div>

      {isSelected && (
        <div className="mt-4 flex items-center justify-center">
          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200">
            <EyeIcon className="h-3 w-3 mr-1" />
            Cours sélectionné
          </span>
        </div>
      )}
    </div>
  );
};

// Composant CourseOverview
const CourseOverview = ({ course }) => {
  return (
    <div className="space-y-6">
      {/* Informations générales */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Informations Générales
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <div className="flex items-center">
              <BookOpenIcon className="h-8 w-8 text-primary-600 dark:text-primary-400" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Code du cours</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-white">
                  {course.courseCode || course.code || 'N/A'}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <div className="flex items-center">
              <UserGroupIcon className="h-8 w-8 text-green-600 dark:text-green-400" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Étudiants</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-white">
                  {course.capacity?.current || course.enrolledStudents || 0}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <div className="flex items-center">
              <ClockIcon className="h-8 w-8 text-blue-600 dark:text-blue-400" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Heures/semaine</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-white">
                  {course.contactHours || course.weeklyHours || 0}h
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Description */}
      {(course.description || course.shortDescription) && (
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Description
          </h3>
          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <p className="text-gray-700 dark:text-gray-300">
              {course.description || course.shortDescription}
            </p>
          </div>
        </div>
      )}

      {/* Objectifs */}
      {course.objectives && course.objectives.length > 0 && (
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Objectifs du Cours
          </h3>
          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <ul className="list-disc list-inside space-y-2">
              {course.objectives.map((objective, index) => (
                <li key={index} className="text-gray-700 dark:text-gray-300">
                  {objective}
                </li>
              ))}
            </ul>
          </div>
        </div>
      )}

      {/* Prérequis */}
      {course.prerequisites && course.prerequisites.length > 0 && (
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Prérequis
          </h3>
          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <ul className="list-disc list-inside space-y-2">
              {course.prerequisites.map((prerequisite, index) => (
                <li key={index} className="text-gray-700 dark:text-gray-300">
                  {prerequisite}
                </li>
              ))}
            </ul>
          </div>
        </div>
      )}

      {/* Statut et dates */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Statut et Planning
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-500 dark:text-gray-400">Statut</span>
              <span className={cn(
                "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",
                course.status === 'active'
                  ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                  : course.status === 'draft'
                  ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
                  : "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
              )}>
                {course.status === 'active' ? 'Actif' : course.status === 'draft' ? 'Brouillon' : 'Inactif'}
              </span>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-500 dark:text-gray-400">Classe</span>
              <span className="text-sm font-semibold text-gray-900 dark:text-white">
                {course.class || 'N/A'}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfessorCoursesPage;
