import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import {
  BellIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  XMarkIcon,
  EyeIcon,
  TrashIcon
} from '@heroicons/react/24/outline';
import { cn } from '../../utils/cn';

const ProfessorNotificationsPage = () => {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all');

  // Données d'exemple pour les notifications
  const exampleNotifications = [
    {
      id: 1,
      type: 'info',
      title: 'Nouveau message d\'un étudiant',
      message: '<PERSON> a envoyé un message concernant le cours de Management Stratégique.',
      date: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 heures
      read: false,
      actionUrl: '/professor/messages'
    },
    {
      id: 2,
      type: 'warning',
      title: 'Évaluation en retard',
      message: 'Les notes du contrôle du 15 décembre doivent être saisies avant demain.',
      date: new Date(Date.now() - 5 * 60 * 60 * 1000), // 5 heures
      read: false,
      actionUrl: '/professor/grades'
    },
    {
      id: 3,
      type: 'success',
      title: 'Présence enregistrée',
      message: 'La présence pour le cours de Leadership du 16 décembre a été enregistrée avec succès.',
      date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 jour
      read: true,
      actionUrl: '/professor/attendance'
    },
    {
      id: 4,
      type: 'info',
      title: 'Rappel de cours',
      message: 'Votre cours de Finance d\'Entreprise commence dans 30 minutes (Salle C301).',
      date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 jours
      read: true,
      actionUrl: '/professor/courses'
    }
  ];

  useEffect(() => {
    // Simuler le chargement des notifications
    setTimeout(() => {
      setNotifications(exampleNotifications);
      setLoading(false);
    }, 500);
  }, []);

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'success':
        return <CheckCircleIcon className="h-6 w-6 text-green-500" />;
      case 'warning':
        return <ExclamationTriangleIcon className="h-6 w-6 text-yellow-500" />;
      case 'error':
        return <XMarkIcon className="h-6 w-6 text-red-500" />;
      default:
        return <InformationCircleIcon className="h-6 w-6 text-blue-500" />;
    }
  };

  const getNotificationBg = (type, read) => {
    const baseClasses = read ? 'bg-white dark:bg-gray-800' : 'bg-blue-50 dark:bg-blue-900/20';
    return baseClasses;
  };

  const formatDate = (date) => {
    const now = new Date();
    const diffInHours = Math.floor((now - date) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'À l\'instant';
    if (diffInHours < 24) return `Il y a ${diffInHours}h`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `Il y a ${diffInDays}j`;
    
    return date.toLocaleDateString('fr-FR');
  };

  const markAsRead = (id) => {
    setNotifications(prev => 
      prev.map(notif => 
        notif.id === id ? { ...notif, read: true } : notif
      )
    );
  };

  const deleteNotification = (id) => {
    setNotifications(prev => prev.filter(notif => notif.id !== id));
  };

  const filteredNotifications = notifications.filter(notif => {
    if (filter === 'unread') return !notif.read;
    if (filter === 'read') return notif.read;
    return true;
  });

  const unreadCount = notifications.filter(n => !n.read).length;

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Notifications</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Restez informé des dernières activités
            {unreadCount > 0 && (
              <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                {unreadCount} non lue{unreadCount > 1 ? 's' : ''}
              </span>
            )}
          </p>
        </div>
        {unreadCount > 0 && (
          <button
            onClick={() => setNotifications(prev => prev.map(n => ({ ...n, read: true })))}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700"
          >
            <CheckCircleIcon className="h-4 w-4 mr-2" />
            Tout marquer comme lu
          </button>
        )}
      </div>

      {/* Filtres */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex space-x-4">
          {[
            { key: 'all', label: 'Toutes', count: notifications.length },
            { key: 'unread', label: 'Non lues', count: unreadCount },
            { key: 'read', label: 'Lues', count: notifications.length - unreadCount }
          ].map((filterOption) => (
            <button
              key={filterOption.key}
              onClick={() => setFilter(filterOption.key)}
              className={cn(
                'px-3 py-2 text-sm font-medium rounded-md transition-colors',
                filter === filterOption.key
                  ? 'bg-primary-100 text-primary-700 dark:bg-primary-900/20 dark:text-primary-400'
                  : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              )}
            >
              {filterOption.label} ({filterOption.count})
            </button>
          ))}
        </div>
      </div>

      {/* Liste des notifications */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">
            Notifications récentes
          </h2>
        </div>
        <div className="divide-y divide-gray-200 dark:divide-gray-700">
          {filteredNotifications.length === 0 ? (
            <div className="text-center py-12">
              <BellIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                Aucune notification
              </h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                {filter === 'unread' 
                  ? 'Toutes vos notifications ont été lues.'
                  : 'Vous n\'avez pas encore de notifications.'
                }
              </p>
            </div>
          ) : (
            filteredNotifications.map((notification) => (
              <div
                key={notification.id}
                className={cn(
                  'p-6 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors',
                  getNotificationBg(notification.type, notification.read)
                )}
              >
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    {getNotificationIcon(notification.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h3 className={cn(
                          'text-sm font-medium',
                          notification.read 
                            ? 'text-gray-900 dark:text-white' 
                            : 'text-gray-900 dark:text-white font-semibold'
                        )}>
                          {notification.title}
                        </h3>
                        <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                          {notification.message}
                        </p>
                        <p className="mt-2 text-xs text-gray-500 dark:text-gray-500">
                          {formatDate(notification.date)}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2 ml-4">
                        {!notification.read && (
                          <button
                            onClick={() => markAsRead(notification.id)}
                            className="text-primary-600 hover:text-primary-800 dark:text-primary-400 dark:hover:text-primary-300"
                            title="Marquer comme lu"
                          >
                            <EyeIcon className="h-4 w-4" />
                          </button>
                        )}
                        <button
                          onClick={() => deleteNotification(notification.id)}
                          className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                          title="Supprimer"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default ProfessorNotificationsPage;
