import axios from 'axios';

// Configuration de base d'axios
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Intercepteur pour ajouter le token d'authentification
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Intercepteur pour gérer les erreurs de réponse
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expiré ou invalide
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Services pour les étudiants
export const studentService = {
  // Obtenir tous les étudiants
  getAll: async (params = {}) => {
    const response = await api.get('/students', { params });
    return response.data;
  },

  // Obtenir un étudiant par ID
  getById: async (id) => {
    const response = await api.get(`/students/${id}`);
    return response.data;
  },

  // Créer un nouvel étudiant
  create: async (studentData) => {
    const response = await api.post('/students', studentData);
    return response.data;
  },

  // Mettre à jour un étudiant
  update: async (id, studentData) => {
    const response = await api.put(`/students/${id}`, studentData);
    return response.data;
  },

  // Supprimer un étudiant
  delete: async (id) => {
    const response = await api.delete(`/students/${id}`);
    return response.data;
  },

  // Rechercher des étudiants
  search: async (query, filters = {}) => {
    const params = { q: query, ...filters };
    const response = await api.get('/students/search', { params });
    return response.data;
  },

  // Obtenir les statistiques
  getStatistics: async (filters = {}) => {
    const response = await api.get('/students/statistics', { params: filters });
    return response.data;
  },

  // Obtenir la progression académique
  getAcademicProgress: async (id) => {
    const response = await api.get(`/students/${id}/academic-progress`);
    return response.data;
  },

  // Obtenir les informations de présence
  getAttendance: async (id) => {
    const response = await api.get(`/students/${id}/attendance`);
    return response.data;
  },

  // Obtenir les informations financières
  getFinancial: async (id) => {
    const response = await api.get(`/students/${id}/financial`);
    return response.data;
  },

  // Ajouter une note
  addNote: async (id, noteData) => {
    const response = await api.post(`/students/${id}/notes`, noteData);
    return response.data;
  },

  // Ajouter un document
  addDocument: async (id, documentData) => {
    const response = await api.post(`/students/${id}/documents`, documentData);
    return response.data;
  }
};

// Services pour les utilisateurs
export const userService = {
  // Créer un utilisateur
  create: async (userData) => {
    const response = await api.post('/users', userData);
    return response.data;
  },

  // Obtenir un utilisateur par ID
  getById: async (id) => {
    const response = await api.get(`/users/${id}`);
    return response.data;
  },

  // Mettre à jour un utilisateur
  update: async (id, userData) => {
    const response = await api.put(`/users/${id}`, userData);
    return response.data;
  },

  // Supprimer un utilisateur
  delete: async (id) => {
    const response = await api.delete(`/users/${id}`);
    return response.data;
  }
};

// Services d'authentification
export const authService = {
  // Connexion
  login: async (credentials) => {
    const response = await api.post('/auth/login', credentials);
    if (response.data.token) {
      localStorage.setItem('token', response.data.token);
    }
    return response.data;
  },

  // Inscription
  register: async (userData) => {
    const response = await api.post('/auth/register', userData);
    return response.data;
  },

  // Déconnexion
  logout: () => {
    localStorage.removeItem('token');
  },

  // Vérifier le token
  verifyToken: async () => {
    const response = await api.get('/auth/verify');
    return response.data;
  },

  // Obtenir le profil utilisateur
  getProfile: async () => {
    const response = await api.get('/auth/me');
    return response.data;
  },

  // Mettre à jour le profil
  updateProfile: async (profileData) => {
    const response = await api.put('/auth/profile', profileData);
    return response.data;
  },

  // Changer le mot de passe
  changePassword: async (passwordData) => {
    const response = await api.post('/auth/change-password', passwordData);
    return response.data;
  },

  // Mot de passe oublié
  forgotPassword: async (email) => {
    const response = await api.post('/auth/forgot-password', { email });
    return response.data;
  },

  // Réinitialiser le mot de passe
  resetPassword: async (token, password) => {
    const response = await api.post('/auth/reset-password', { token, password });
    return response.data;
  }
};

export default api;
