import { studentService, userService } from './api';

// Adapter pour convertir les données du formulaire frontend vers le format backend
export const adaptFormDataToBackend = (formData) => {
  return {
    // Données utilisateur (table User)
    user: {
      firstName: formData.firstName,
      lastName: formData.lastName,
      email: formData.email,
      phone: formData.phone,
      role: 'student',
      isActive: formData.status === 'Actif'
    },
    // Données étudiant (table Student)
    program: formData.program,
    enrollmentStatus: mapStatusToBackend(formData.status),
    enrollmentDate: formData.enrollmentDate,
    academicYear: getCurrentAcademicYear(),
    cohort: generateCohortName(formData.program, formData.enrollmentDate),
    expectedGraduationDate: calculateGraduationDate(formData.enrollmentDate),
    tuitionFee: {
      total: getDefaultTuitionFee(formData.program),
      paid: 0,
      remaining: getDefaultTuitionFee(formData.program),
      currency: 'TND',
      paymentPlan: 'installments'
    },
    academicRecord: {
      creditsRequired: getRequiredCredits(formData.program),
      creditsCompleted: 0,
      currentGPA: 0,
      cumulativeGPA: 0,
      academicStanding: 'satisfactory'
    }
  };
};

// Adapter pour convertir les données backend vers le format frontend
export const adaptBackendToFormData = (backendData) => {
  // Construire le nom du programme à partir du programme et de la spécialisation
  let programName = 'EMBA';
  if (backendData.specialization) {
    const specializationMap = {
      'Finance': 'EMBA Finance',
      'Marketing': 'EMBA Marketing',
      'General Management': 'EMBA Management',
      'Digital Transformation': 'EMBA Digital',
      'Operations': 'EMBA Management',
      'Strategy': 'EMBA Management'
    };
    programName = specializationMap[backendData.specialization] || 'EMBA Management';
  }

  return {
    id: backendData._id,
    firstName: backendData.user?.firstName || '',
    lastName: backendData.user?.lastName || '',
    email: backendData.user?.email || '',
    phone: backendData.user?.phone || '',
    program: programName,
    status: mapStatusToFrontend(backendData.enrollmentStatus),
    enrollmentDate: backendData.enrollmentDate ?
      new Date(backendData.enrollmentDate).toISOString().split('T')[0] : '',
    avatar: backendData.user?.profilePicture || null,
    studentNumber: backendData.studentNumber || '',
    academicYear: backendData.academicYear || '',
    cohort: backendData.cohort || ''
  };
};

// Mapper les statuts frontend vers backend
const mapStatusToBackend = (frontendStatus) => {
  const statusMap = {
    'Actif': 'active',
    'Inactif': 'inactive',
    'En attente': 'deferred',
    'Suspendu': 'suspended'
  };
  return statusMap[frontendStatus] || 'active';
};

// Mapper les statuts backend vers frontend
const mapStatusToFrontend = (backendStatus) => {
  const statusMap = {
    'active': 'Actif',
    'inactive': 'Inactif',
    'deferred': 'En attente',
    'suspended': 'Suspendu',
    'graduated': 'Diplômé',
    'dropped': 'Abandonné'
  };
  return statusMap[backendStatus] || 'Actif';
};

// Générer l'année académique actuelle
const getCurrentAcademicYear = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = now.getMonth();

  // L'année académique commence en septembre
  if (month >= 8) { // Septembre = mois 8
    return `${year}-${year + 1}`;
  } else {
    return `${year - 1}-${year}`;
  }
};

// Générer le nom de la cohorte
const generateCohortName = (program, enrollmentDate) => {
  const date = new Date(enrollmentDate);
  const year = date.getFullYear();
  const programCode = program.replace('EMBA ', '').substring(0, 3).toUpperCase();
  return `${programCode}${year}`;
};

// Calculer la date de graduation prévue (2 ans après l'inscription)
const calculateGraduationDate = (enrollmentDate) => {
  const date = new Date(enrollmentDate);
  date.setFullYear(date.getFullYear() + 2);
  return date;
};

// Obtenir les frais de scolarité par défaut selon le programme
const getDefaultTuitionFee = (program) => {
  const fees = {
    'EMBA Finance': 25000,
    'EMBA Marketing': 23000,
    'EMBA Management': 24000,
    'EMBA Digital': 26000,
    'EMBA International': 28000
  };
  return fees[program] || 25000;
};

// Obtenir le nombre de crédits requis selon le programme
const getRequiredCredits = (program) => {
  const credits = {
    'EMBA Finance': 60,
    'EMBA Marketing': 60,
    'EMBA Management': 60,
    'EMBA Digital': 65,
    'EMBA International': 70
  };
  return credits[program] || 60;
};

// Mapper les programmes frontend vers backend
const mapProgramToBackend = (frontendProgram) => {
  // Le backend attend 'EMBA' comme programme principal
  return 'EMBA';
};

// Extraire la spécialisation du programme frontend
const extractSpecialization = (frontendProgram) => {
  const specializationMap = {
    'EMBA Finance': 'Finance',
    'EMBA Marketing': 'Marketing',
    'EMBA Management': 'General Management',
    'EMBA Digital': 'Digital Transformation',
    'EMBA International': 'General Management'
  };
  return specializationMap[frontendProgram] || 'General Management';
};

// Service principal pour gérer les étudiants avec adaptation des données
export const studentAdapterService = {
  // Créer un étudiant avec adaptation des données
  create: async (formData) => {
    try {
      // Préparer les données dans le format attendu par le backend
      const studentData = {
        // Données utilisateur intégrées
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        phone: formData.phone,

        // Données étudiant
        program: mapProgramToBackend(formData.program),
        specialization: extractSpecialization(formData.program),
        enrollmentStatus: mapStatusToBackend(formData.status),
        enrollmentDate: formData.enrollmentDate,
        academicYear: getCurrentAcademicYear(),
        cohort: generateCohortName(formData.program, formData.enrollmentDate),
        expectedGraduationDate: calculateGraduationDate(formData.enrollmentDate),

        // Données académiques
        academicRecord: {
          creditsRequired: getRequiredCredits(formData.program),
          creditsCompleted: 0,
          currentGPA: 0,
          cumulativeGPA: 0,
          academicStanding: 'satisfactory'
        },

        // Données financières
        tuitionFee: {
          total: getDefaultTuitionFee(formData.program),
          paid: 0,
          remaining: getDefaultTuitionFee(formData.program),
          currency: 'TND',
          paymentPlan: 'installments'
        }
      };

      console.log('Données envoyées au backend:', studentData);
      const student = await studentService.create(studentData);
      return adaptBackendToFormData(student);
    } catch (error) {
      console.error('Erreur lors de la création de l\'étudiant:', error);

      // Améliorer le message d'erreur pour l'utilisateur
      if (error.response?.status === 400) {
        const message = error.response.data?.message || 'Erreur de validation';
        if (message.includes('email') || message.includes('Email')) {
          throw new Error('Cette adresse email est déjà utilisée par un autre utilisateur.');
        } else if (message.includes('Student already exists')) {
          throw new Error('Cet étudiant existe déjà dans le système.');
        } else {
          throw new Error(message);
        }
      }

      throw new Error('Erreur lors de la création de l\'étudiant. Veuillez réessayer.');
    }
  },

  // Obtenir tous les étudiants avec adaptation
  getAll: async (params = {}) => {
    try {
      const response = await studentService.getAll(params);
      return {
        ...response,
        students: response.students.map(adaptBackendToFormData)
      };
    } catch (error) {
      console.error('Erreur lors de la récupération des étudiants:', error);
      throw error;
    }
  },

  // Mettre à jour un étudiant avec adaptation
  update: async (id, formData) => {
    try {
      // Mettre à jour les données utilisateur
      const userData = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        phone: formData.phone,
        isActive: formData.status === 'Actif'
      };

      // Obtenir l'étudiant actuel pour récupérer l'ID utilisateur
      const currentStudent = await studentService.getById(id);
      if (currentStudent.user?._id) {
        await userService.update(currentStudent.user._id, userData);
      }

      // Mettre à jour les données étudiant
      const studentData = {
        program: formData.program,
        enrollmentStatus: mapStatusToBackend(formData.status),
        enrollmentDate: formData.enrollmentDate
      };

      const updatedStudent = await studentService.update(id, studentData);
      return adaptBackendToFormData(updatedStudent);
    } catch (error) {
      console.error('Erreur lors de la mise à jour de l\'étudiant:', error);
      throw error;
    }
  },

  // Supprimer un étudiant
  delete: async (id) => {
    try {
      // Obtenir l'étudiant pour récupérer l'ID utilisateur
      const student = await studentService.getById(id);

      // Supprimer l'étudiant
      await studentService.delete(id);

      // Supprimer l'utilisateur associé
      if (student.user?._id) {
        await userService.delete(student.user._id);
      }

      return { success: true };
    } catch (error) {
      console.error('Erreur lors de la suppression de l\'étudiant:', error);
      throw error;
    }
  }
};

// Générer un mot de passe temporaire
const generateTemporaryPassword = () => {
  return Math.random().toString(36).slice(-8) + Math.random().toString(36).slice(-8);
};

export default studentAdapterService;
